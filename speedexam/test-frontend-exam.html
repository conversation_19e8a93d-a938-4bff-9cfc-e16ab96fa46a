<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Frontend Exam</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
            font-size: 16px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .success {
            background: #28a745;
            color: white;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .error {
            background: #dc3545;
            color: white;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .info {
            background: #17a2b8;
            color: white;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎓 SpeedExam Frontend Test</h1>
        
        <div class="info">
            <strong>Testing Frontend Connection to Backend API</strong><br>
            This will help diagnose exam taking issues.
        </div>

        <h2>Step 1: Test API Connection</h2>
        <button class="btn" onclick="testAPI()">Test API Connection</button>
        <div id="apiResult"></div>

        <h2>Step 2: Test Student Login</h2>
        <button class="btn" onclick="testLogin()">Test Student Login</button>
        <div id="loginResult"></div>

        <h2>Step 3: Test Exam Start</h2>
        <button class="btn" onclick="testExamStart()">Test Exam Start</button>
        <div id="examStartResult"></div>

        <h2>Step 4: Test Complete Flow</h2>
        <button class="btn" onclick="testCompleteFlow()">Test Complete Exam Flow</button>
        <div id="completeFlowResult"></div>

        <h2>Frontend Links</h2>
        <a href="http://localhost:3000/login" class="btn" target="_blank">Open Student Login</a>
        <a href="http://localhost:3000/exams" class="btn" target="_blank">Open Exam List</a>
        <a href="http://localhost:3000/exam/1" class="btn" target="_blank">Direct Exam Link</a>
    </div>

    <script>
        const API_BASE = 'http://localhost:5000/api';
        let authToken = '';

        async function testAPI() {
            const resultDiv = document.getElementById('apiResult');
            resultDiv.innerHTML = '<div class="info">Testing API connection...</div>';
            
            try {
                const response = await fetch(`${API_BASE}/test`);
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `<div class="success">✅ API Connection Successful<br>Response: ${JSON.stringify(data)}</div>`;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ API Error: ${response.status}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Connection Failed: ${error.message}</div>`;
            }
        }

        async function testLogin() {
            const resultDiv = document.getElementById('loginResult');
            resultDiv.innerHTML = '<div class="info">Testing student login...</div>';
            
            try {
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'password123'
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    authToken = data.token;
                    resultDiv.innerHTML = `<div class="success">✅ Login Successful<br>User: ${data.user.name}<br>Token: ${data.token.substring(0, 20)}...</div>`;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ Login Failed: ${JSON.stringify(data)}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Login Error: ${error.message}</div>`;
            }
        }

        async function testExamStart() {
            const resultDiv = document.getElementById('examStartResult');
            
            if (!authToken) {
                resultDiv.innerHTML = '<div class="error">❌ Please login first</div>';
                return;
            }
            
            resultDiv.innerHTML = '<div class="info">Testing exam start...</div>';
            
            try {
                const response = await fetch(`${API_BASE}/exams/1/start`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `<div class="success">✅ Exam Start Successful<br>Attempt ID: ${data.attempt}<br>Exam: ${data.exam.title}<br>Questions: ${data.exam.questions.length}</div>`;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ Exam Start Failed: ${JSON.stringify(data)}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Exam Start Error: ${error.message}</div>`;
            }
        }

        async function testCompleteFlow() {
            const resultDiv = document.getElementById('completeFlowResult');
            resultDiv.innerHTML = '<div class="info">Testing complete exam flow...</div>';
            
            try {
                // Step 1: Login
                const loginResponse = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'password123'
                    })
                });
                const loginData = await loginResponse.json();
                
                if (!loginResponse.ok) {
                    throw new Error('Login failed');
                }
                
                // Step 2: Start Exam
                const startResponse = await fetch(`${API_BASE}/exams/1/start`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${loginData.token}`
                    }
                });
                const startData = await startResponse.json();
                
                if (!startResponse.ok) {
                    throw new Error('Exam start failed');
                }
                
                // Step 3: Submit Exam
                const answers = [
                    { question: 'q1', selectedOptions: ['4'] },
                    { question: 'q2', selectedOptions: ['50'] },
                    { question: 'q3', selectedOptions: ['True'] }
                ];
                
                const submitResponse = await fetch(`${API_BASE}/exam-attempts/${startData.attempt}/submit`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${loginData.token}`
                    },
                    body: JSON.stringify({ answers })
                });
                const submitData = await submitResponse.json();
                
                if (!submitResponse.ok) {
                    throw new Error('Exam submission failed');
                }
                
                // Step 4: Get Results
                const resultsResponse = await fetch(`${API_BASE}/exam-attempts/${startData.attempt}/results`, {
                    headers: {
                        'Authorization': `Bearer ${loginData.token}`
                    }
                });
                const resultsData = await resultsResponse.json();
                
                resultDiv.innerHTML = `
                    <div class="success">
                        ✅ Complete Flow Successful!<br><br>
                        <strong>Results:</strong><br>
                        Score: ${resultsData.score}/${resultsData.totalQuestions}<br>
                        Percentage: ${resultsData.percentage}%<br>
                        Grade: ${resultsData.grade}<br><br>
                        <strong>Frontend URLs to Test:</strong><br>
                        • Login: <a href="http://localhost:3000/login" target="_blank">http://localhost:3000/login</a><br>
                        • Exams: <a href="http://localhost:3000/exams" target="_blank">http://localhost:3000/exams</a><br>
                        • Direct Exam: <a href="http://localhost:3000/exam/1" target="_blank">http://localhost:3000/exam/1</a><br><br>
                        <strong>Credentials:</strong><br>
                        Email: <EMAIL><br>
                        Password: password123
                    </div>
                `;
                
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Complete Flow Error: ${error.message}</div>`;
            }
        }

        // Auto-test on page load
        window.onload = function() {
            testAPI();
        };
    </script>
</body>
</html>
