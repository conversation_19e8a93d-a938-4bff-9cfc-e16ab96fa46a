{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/StudentDashboard.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport api from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StudentDashboard = () => {\n  _s();\n  const [student, setStudent] = useState(null);\n  const [exams, setExams] = useState([]);\n  const [recentAttempts, setRecentAttempts] = useState([]);\n  const [stats, setStats] = useState({\n    totalAttempts: 0,\n    averageScore: 0,\n    bestScore: 0,\n    totalTimeSpent: 0\n  });\n  const navigate = useNavigate();\n  useEffect(() => {\n    const token = localStorage.getItem('token');\n    if (!token) {\n      navigate('/login');\n      return;\n    }\n    fetchStudentData();\n  }, [navigate]);\n  const fetchStudentData = async () => {\n    try {\n      // Mock student data\n      setStudent({\n        name: 'Test User',\n        email: '<EMAIL>',\n        studentId: 'STU001',\n        joinDate: '2024-01-01'\n      });\n\n      // Fetch available exams\n      const examsResponse = await api.get('/exams');\n      setExams(examsResponse.data);\n\n      // Mock recent attempts\n      setRecentAttempts([{\n        id: 1,\n        examTitle: 'Basic Mathematics Test',\n        score: 3,\n        totalQuestions: 3,\n        percentage: 100,\n        timeTaken: 15,\n        date: '2024-01-15',\n        status: 'Completed'\n      }, {\n        id: 2,\n        examTitle: 'General Science Quiz',\n        score: 1,\n        totalQuestions: 2,\n        percentage: 50,\n        timeTaken: 12,\n        date: '2024-01-14',\n        status: 'Completed'\n      }, {\n        id: 3,\n        examTitle: 'Basic Mathematics Test',\n        score: 2,\n        totalQuestions: 3,\n        percentage: 67,\n        timeTaken: 20,\n        date: '2024-01-13',\n        status: 'Completed'\n      }]);\n\n      // Calculate stats\n      setStats({\n        totalAttempts: 3,\n        averageScore: 72,\n        bestScore: 100,\n        totalTimeSpent: 47\n      });\n    } catch (error) {\n      console.error('Error fetching student data:', error);\n    }\n  };\n  const handleLogout = () => {\n    localStorage.removeItem('token');\n    localStorage.removeItem('userRole');\n    navigate('/');\n  };\n  if (!student) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading\",\n      children: \"Loading student dashboard...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 95,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"student-dashboard\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"student-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: [\"\\uD83D\\uDC4B Welcome, \", student.name, \"!\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"student-details\",\n          children: [\"Student ID: \", student.studentId, \" | Email: \", student.email]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-actions\",\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          to: \"/admin/login\",\n          className: \"btn btn-secondary\",\n          children: \"Admin Panel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleLogout,\n          className: \"btn btn-outline\",\n          children: \"Logout\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"student-stats\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-icon\",\n          children: \"\\uD83D\\uDCDD\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: stats.totalAttempts\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Exams Taken\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-icon\",\n          children: \"\\uD83D\\uDCCA\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: [stats.averageScore, \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Average Score\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-icon\",\n          children: \"\\uD83C\\uDFC6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: [stats.bestScore, \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Best Score\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-icon\",\n          children: \"\\u23F1\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: stats.totalTimeSpent\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Minutes Studied\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 115,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"\\uD83D\\uDCDA Available Exams\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"exams-grid\",\n        children: exams.map(exam => {\n          var _exam$subject, _exam$questions;\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"student-exam-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"exam-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: exam.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 156,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"exam-subject\",\n                children: (_exam$subject = exam.subject) === null || _exam$subject === void 0 ? void 0 : _exam$subject.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 157,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"exam-details\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"exam-detail\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"detail-icon\",\n                  children: \"\\u2753\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 162,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [(_exam$questions = exam.questions) === null || _exam$questions === void 0 ? void 0 : _exam$questions.length, \" Questions\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 163,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"exam-detail\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"detail-icon\",\n                  children: \"\\u23F0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 166,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [exam.duration, \" Minutes\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 167,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 165,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"exam-detail\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"detail-icon\",\n                  children: \"\\uD83D\\uDCCB\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 170,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: exam.examType\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 171,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"exam-description\",\n              children: exam.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"exam-actions\",\n              children: [/*#__PURE__*/_jsxDEV(Link, {\n                to: `/exam/${exam._id}`,\n                className: \"btn btn-primary\",\n                children: \"Start Exam\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 178,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"btn btn-outline\",\n                children: \"View Details\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 15\n            }, this)]\n          }, exam._id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 13\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 150,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"\\uD83D\\uDCCB Recent Exam Attempts\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 190,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"attempts-list\",\n        children: recentAttempts.map(attempt => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"attempt-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"attempt-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: attempt.examTitle\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"attempt-date\",\n              children: attempt.date\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"attempt-score\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"score-circle\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"score-percentage\",\n                children: [attempt.percentage, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"score-details\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"Score: \", attempt.score, \"/\", attempt.totalQuestions]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 204,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"Time: \", attempt.timeTaken, \" min\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"attempt-status\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `status-badge ${attempt.percentage >= 70 ? 'passed' : 'failed'}`,\n              children: attempt.percentage >= 70 ? 'Passed' : 'Failed'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 15\n          }, this)]\n        }, attempt.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 189,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"\\u26A1 Quick Actions\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 223,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"quick-actions\",\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          to: \"/exams\",\n          className: \"action-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"action-icon\",\n            children: \"\\uD83D\\uDCDA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Browse All Exams\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"View all available exams\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/student/results\",\n          className: \"action-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"action-icon\",\n            children: \"\\uD83D\\uDCCA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"View All Results\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Check your exam history\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 234,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/student/profile\",\n          className: \"action-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"action-icon\",\n            children: \"\\uD83D\\uDC64\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Update Profile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Manage your account\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 224,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 222,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 99,\n    columnNumber: 5\n  }, this);\n};\n_s(StudentDashboard, \"Y4c7+N2fryvY6QM/dBDsit3fM1M=\", false, function () {\n  return [useNavigate];\n});\n_c = StudentDashboard;\nexport default StudentDashboard;\nvar _c;\n$RefreshReg$(_c, \"StudentDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "useNavigate", "api", "jsxDEV", "_jsxDEV", "StudentDashboard", "_s", "student", "setStudent", "exams", "setExams", "recentAttempts", "setRecentAttempts", "stats", "setStats", "totalAttempts", "averageScore", "bestScore", "totalTimeSpent", "navigate", "token", "localStorage", "getItem", "fetchStudentData", "name", "email", "studentId", "joinDate", "examsResponse", "get", "data", "id", "examTitle", "score", "totalQuestions", "percentage", "timeTaken", "date", "status", "error", "console", "handleLogout", "removeItem", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "onClick", "map", "exam", "_exam$subject", "_exam$questions", "title", "subject", "questions", "length", "duration", "examType", "description", "_id", "attempt", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/StudentDashboard.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport api from '../services/api';\n\nconst StudentDashboard = () => {\n  const [student, setStudent] = useState(null);\n  const [exams, setExams] = useState([]);\n  const [recentAttempts, setRecentAttempts] = useState([]);\n  const [stats, setStats] = useState({\n    totalAttempts: 0,\n    averageScore: 0,\n    bestScore: 0,\n    totalTimeSpent: 0\n  });\n  const navigate = useNavigate();\n\n  useEffect(() => {\n    const token = localStorage.getItem('token');\n    if (!token) {\n      navigate('/login');\n      return;\n    }\n\n    fetchStudentData();\n  }, [navigate]);\n\n  const fetchStudentData = async () => {\n    try {\n      // Mock student data\n      setStudent({\n        name: 'Test User',\n        email: '<EMAIL>',\n        studentId: 'STU001',\n        joinDate: '2024-01-01'\n      });\n\n      // Fetch available exams\n      const examsResponse = await api.get('/exams');\n      setExams(examsResponse.data);\n\n      // Mock recent attempts\n      setRecentAttempts([\n        {\n          id: 1,\n          examTitle: 'Basic Mathematics Test',\n          score: 3,\n          totalQuestions: 3,\n          percentage: 100,\n          timeTaken: 15,\n          date: '2024-01-15',\n          status: 'Completed'\n        },\n        {\n          id: 2,\n          examTitle: 'General Science Quiz',\n          score: 1,\n          totalQuestions: 2,\n          percentage: 50,\n          timeTaken: 12,\n          date: '2024-01-14',\n          status: 'Completed'\n        },\n        {\n          id: 3,\n          examTitle: 'Basic Mathematics Test',\n          score: 2,\n          totalQuestions: 3,\n          percentage: 67,\n          timeTaken: 20,\n          date: '2024-01-13',\n          status: 'Completed'\n        }\n      ]);\n\n      // Calculate stats\n      setStats({\n        totalAttempts: 3,\n        averageScore: 72,\n        bestScore: 100,\n        totalTimeSpent: 47\n      });\n\n    } catch (error) {\n      console.error('Error fetching student data:', error);\n    }\n  };\n\n  const handleLogout = () => {\n    localStorage.removeItem('token');\n    localStorage.removeItem('userRole');\n    navigate('/');\n  };\n\n  if (!student) {\n    return <div className=\"loading\">Loading student dashboard...</div>;\n  }\n\n  return (\n    <div className=\"student-dashboard\">\n      {/* Header */}\n      <div className=\"dashboard-header\">\n        <div className=\"student-info\">\n          <h1>👋 Welcome, {student.name}!</h1>\n          <p className=\"student-details\">\n            Student ID: {student.studentId} | Email: {student.email}\n          </p>\n        </div>\n        <div className=\"header-actions\">\n          <Link to=\"/admin/login\" className=\"btn btn-secondary\">Admin Panel</Link>\n          <button onClick={handleLogout} className=\"btn btn-outline\">Logout</button>\n        </div>\n      </div>\n\n      {/* Statistics */}\n      <div className=\"student-stats\">\n        <div className=\"stat-card\">\n          <div className=\"stat-icon\">📝</div>\n          <div className=\"stat-content\">\n            <h3>{stats.totalAttempts}</h3>\n            <p>Exams Taken</p>\n          </div>\n        </div>\n        \n        <div className=\"stat-card\">\n          <div className=\"stat-icon\">📊</div>\n          <div className=\"stat-content\">\n            <h3>{stats.averageScore}%</h3>\n            <p>Average Score</p>\n          </div>\n        </div>\n        \n        <div className=\"stat-card\">\n          <div className=\"stat-icon\">🏆</div>\n          <div className=\"stat-content\">\n            <h3>{stats.bestScore}%</h3>\n            <p>Best Score</p>\n          </div>\n        </div>\n        \n        <div className=\"stat-card\">\n          <div className=\"stat-icon\">⏱️</div>\n          <div className=\"stat-content\">\n            <h3>{stats.totalTimeSpent}</h3>\n            <p>Minutes Studied</p>\n          </div>\n        </div>\n      </div>\n\n      {/* Available Exams */}\n      <div className=\"dashboard-section\">\n        <h2>📚 Available Exams</h2>\n        <div className=\"exams-grid\">\n          {exams.map(exam => (\n            <div key={exam._id} className=\"student-exam-card\">\n              <div className=\"exam-header\">\n                <h3>{exam.title}</h3>\n                <span className=\"exam-subject\">{exam.subject?.name}</span>\n              </div>\n              \n              <div className=\"exam-details\">\n                <div className=\"exam-detail\">\n                  <span className=\"detail-icon\">❓</span>\n                  <span>{exam.questions?.length} Questions</span>\n                </div>\n                <div className=\"exam-detail\">\n                  <span className=\"detail-icon\">⏰</span>\n                  <span>{exam.duration} Minutes</span>\n                </div>\n                <div className=\"exam-detail\">\n                  <span className=\"detail-icon\">📋</span>\n                  <span>{exam.examType}</span>\n                </div>\n              </div>\n              \n              <p className=\"exam-description\">{exam.description}</p>\n              \n              <div className=\"exam-actions\">\n                <Link to={`/exam/${exam._id}`} className=\"btn btn-primary\">\n                  Start Exam\n                </Link>\n                <button className=\"btn btn-outline\">View Details</button>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n\n      {/* Recent Attempts */}\n      <div className=\"dashboard-section\">\n        <h2>📋 Recent Exam Attempts</h2>\n        <div className=\"attempts-list\">\n          {recentAttempts.map(attempt => (\n            <div key={attempt.id} className=\"attempt-card\">\n              <div className=\"attempt-info\">\n                <h4>{attempt.examTitle}</h4>\n                <p className=\"attempt-date\">{attempt.date}</p>\n              </div>\n              \n              <div className=\"attempt-score\">\n                <div className=\"score-circle\">\n                  <span className=\"score-percentage\">{attempt.percentage}%</span>\n                </div>\n                <div className=\"score-details\">\n                  <p>Score: {attempt.score}/{attempt.totalQuestions}</p>\n                  <p>Time: {attempt.timeTaken} min</p>\n                </div>\n              </div>\n              \n              <div className=\"attempt-status\">\n                <span className={`status-badge ${\n                  attempt.percentage >= 70 ? 'passed' : 'failed'\n                }`}>\n                  {attempt.percentage >= 70 ? 'Passed' : 'Failed'}\n                </span>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n\n      {/* Quick Actions */}\n      <div className=\"dashboard-section\">\n        <h2>⚡ Quick Actions</h2>\n        <div className=\"quick-actions\">\n          <Link to=\"/exams\" className=\"action-card\">\n            <div className=\"action-icon\">📚</div>\n            <h3>Browse All Exams</h3>\n            <p>View all available exams</p>\n          </Link>\n          \n          <Link to=\"/student/results\" className=\"action-card\">\n            <div className=\"action-icon\">📊</div>\n            <h3>View All Results</h3>\n            <p>Check your exam history</p>\n          </Link>\n          \n          <Link to=\"/student/profile\" className=\"action-card\">\n            <div className=\"action-icon\">👤</div>\n            <h3>Update Profile</h3>\n            <p>Manage your account</p>\n          </Link>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default StudentDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,OAAOC,GAAG,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACW,KAAK,EAAEC,QAAQ,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACa,cAAc,EAAEC,iBAAiB,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACe,KAAK,EAAEC,QAAQ,CAAC,GAAGhB,QAAQ,CAAC;IACjCiB,aAAa,EAAE,CAAC;IAChBC,YAAY,EAAE,CAAC;IACfC,SAAS,EAAE,CAAC;IACZC,cAAc,EAAE;EAClB,CAAC,CAAC;EACF,MAAMC,QAAQ,GAAGlB,WAAW,CAAC,CAAC;EAE9BF,SAAS,CAAC,MAAM;IACd,MAAMqB,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAI,CAACF,KAAK,EAAE;MACVD,QAAQ,CAAC,QAAQ,CAAC;MAClB;IACF;IAEAI,gBAAgB,CAAC,CAAC;EACpB,CAAC,EAAE,CAACJ,QAAQ,CAAC,CAAC;EAEd,MAAMI,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF;MACAf,UAAU,CAAC;QACTgB,IAAI,EAAE,WAAW;QACjBC,KAAK,EAAE,kBAAkB;QACzBC,SAAS,EAAE,QAAQ;QACnBC,QAAQ,EAAE;MACZ,CAAC,CAAC;;MAEF;MACA,MAAMC,aAAa,GAAG,MAAM1B,GAAG,CAAC2B,GAAG,CAAC,QAAQ,CAAC;MAC7CnB,QAAQ,CAACkB,aAAa,CAACE,IAAI,CAAC;;MAE5B;MACAlB,iBAAiB,CAAC,CAChB;QACEmB,EAAE,EAAE,CAAC;QACLC,SAAS,EAAE,wBAAwB;QACnCC,KAAK,EAAE,CAAC;QACRC,cAAc,EAAE,CAAC;QACjBC,UAAU,EAAE,GAAG;QACfC,SAAS,EAAE,EAAE;QACbC,IAAI,EAAE,YAAY;QAClBC,MAAM,EAAE;MACV,CAAC,EACD;QACEP,EAAE,EAAE,CAAC;QACLC,SAAS,EAAE,sBAAsB;QACjCC,KAAK,EAAE,CAAC;QACRC,cAAc,EAAE,CAAC;QACjBC,UAAU,EAAE,EAAE;QACdC,SAAS,EAAE,EAAE;QACbC,IAAI,EAAE,YAAY;QAClBC,MAAM,EAAE;MACV,CAAC,EACD;QACEP,EAAE,EAAE,CAAC;QACLC,SAAS,EAAE,wBAAwB;QACnCC,KAAK,EAAE,CAAC;QACRC,cAAc,EAAE,CAAC;QACjBC,UAAU,EAAE,EAAE;QACdC,SAAS,EAAE,EAAE;QACbC,IAAI,EAAE,YAAY;QAClBC,MAAM,EAAE;MACV,CAAC,CACF,CAAC;;MAEF;MACAxB,QAAQ,CAAC;QACPC,aAAa,EAAE,CAAC;QAChBC,YAAY,EAAE,EAAE;QAChBC,SAAS,EAAE,GAAG;QACdC,cAAc,EAAE;MAClB,CAAC,CAAC;IAEJ,CAAC,CAAC,OAAOqB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD;EACF,CAAC;EAED,MAAME,YAAY,GAAGA,CAAA,KAAM;IACzBpB,YAAY,CAACqB,UAAU,CAAC,OAAO,CAAC;IAChCrB,YAAY,CAACqB,UAAU,CAAC,UAAU,CAAC;IACnCvB,QAAQ,CAAC,GAAG,CAAC;EACf,CAAC;EAED,IAAI,CAACZ,OAAO,EAAE;IACZ,oBAAOH,OAAA;MAAKuC,SAAS,EAAC,SAAS;MAAAC,QAAA,EAAC;IAA4B;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EACpE;EAEA,oBACE5C,OAAA;IAAKuC,SAAS,EAAC,mBAAmB;IAAAC,QAAA,gBAEhCxC,OAAA;MAAKuC,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BxC,OAAA;QAAKuC,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BxC,OAAA;UAAAwC,QAAA,GAAI,wBAAY,EAACrC,OAAO,CAACiB,IAAI,EAAC,GAAC;QAAA;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACpC5C,OAAA;UAAGuC,SAAS,EAAC,iBAAiB;UAAAC,QAAA,GAAC,cACjB,EAACrC,OAAO,CAACmB,SAAS,EAAC,YAAU,EAACnB,OAAO,CAACkB,KAAK;QAAA;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACN5C,OAAA;QAAKuC,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BxC,OAAA,CAACJ,IAAI;UAACiD,EAAE,EAAC,cAAc;UAACN,SAAS,EAAC,mBAAmB;UAAAC,QAAA,EAAC;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACxE5C,OAAA;UAAQ8C,OAAO,EAAET,YAAa;UAACE,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAC;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN5C,OAAA;MAAKuC,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5BxC,OAAA;QAAKuC,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBxC,OAAA;UAAKuC,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACnC5C,OAAA;UAAKuC,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BxC,OAAA;YAAAwC,QAAA,EAAK/B,KAAK,CAACE;UAAa;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC9B5C,OAAA;YAAAwC,QAAA,EAAG;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN5C,OAAA;QAAKuC,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBxC,OAAA;UAAKuC,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACnC5C,OAAA;UAAKuC,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BxC,OAAA;YAAAwC,QAAA,GAAK/B,KAAK,CAACG,YAAY,EAAC,GAAC;UAAA;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9B5C,OAAA;YAAAwC,QAAA,EAAG;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN5C,OAAA;QAAKuC,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBxC,OAAA;UAAKuC,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACnC5C,OAAA;UAAKuC,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BxC,OAAA;YAAAwC,QAAA,GAAK/B,KAAK,CAACI,SAAS,EAAC,GAAC;UAAA;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3B5C,OAAA;YAAAwC,QAAA,EAAG;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN5C,OAAA;QAAKuC,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBxC,OAAA;UAAKuC,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACnC5C,OAAA;UAAKuC,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BxC,OAAA;YAAAwC,QAAA,EAAK/B,KAAK,CAACK;UAAc;YAAA2B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC/B5C,OAAA;YAAAwC,QAAA,EAAG;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN5C,OAAA;MAAKuC,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChCxC,OAAA;QAAAwC,QAAA,EAAI;MAAkB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC3B5C,OAAA;QAAKuC,SAAS,EAAC,YAAY;QAAAC,QAAA,EACxBnC,KAAK,CAAC0C,GAAG,CAACC,IAAI;UAAA,IAAAC,aAAA,EAAAC,eAAA;UAAA,oBACblD,OAAA;YAAoBuC,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAC/CxC,OAAA;cAAKuC,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BxC,OAAA;gBAAAwC,QAAA,EAAKQ,IAAI,CAACG;cAAK;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACrB5C,OAAA;gBAAMuC,SAAS,EAAC,cAAc;gBAAAC,QAAA,GAAAS,aAAA,GAAED,IAAI,CAACI,OAAO,cAAAH,aAAA,uBAAZA,aAAA,CAAc7B;cAAI;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CAAC,eAEN5C,OAAA;cAAKuC,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BxC,OAAA;gBAAKuC,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BxC,OAAA;kBAAMuC,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACtC5C,OAAA;kBAAAwC,QAAA,IAAAU,eAAA,GAAOF,IAAI,CAACK,SAAS,cAAAH,eAAA,uBAAdA,eAAA,CAAgBI,MAAM,EAAC,YAAU;gBAAA;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C,CAAC,eACN5C,OAAA;gBAAKuC,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BxC,OAAA;kBAAMuC,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACtC5C,OAAA;kBAAAwC,QAAA,GAAOQ,IAAI,CAACO,QAAQ,EAAC,UAAQ;gBAAA;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC,eACN5C,OAAA;gBAAKuC,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BxC,OAAA;kBAAMuC,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACvC5C,OAAA;kBAAAwC,QAAA,EAAOQ,IAAI,CAACQ;gBAAQ;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN5C,OAAA;cAAGuC,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAEQ,IAAI,CAACS;YAAW;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAEtD5C,OAAA;cAAKuC,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BxC,OAAA,CAACJ,IAAI;gBAACiD,EAAE,EAAE,SAASG,IAAI,CAACU,GAAG,EAAG;gBAACnB,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,EAAC;cAE3D;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACP5C,OAAA;gBAAQuC,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC;UAAA,GA5BEI,IAAI,CAACU,GAAG;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA6Bb,CAAC;QAAA,CACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN5C,OAAA;MAAKuC,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChCxC,OAAA;QAAAwC,QAAA,EAAI;MAAuB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAChC5C,OAAA;QAAKuC,SAAS,EAAC,eAAe;QAAAC,QAAA,EAC3BjC,cAAc,CAACwC,GAAG,CAACY,OAAO,iBACzB3D,OAAA;UAAsBuC,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC5CxC,OAAA;YAAKuC,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BxC,OAAA;cAAAwC,QAAA,EAAKmB,OAAO,CAAC/B;YAAS;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC5B5C,OAAA;cAAGuC,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAEmB,OAAO,CAAC1B;YAAI;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,eAEN5C,OAAA;YAAKuC,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BxC,OAAA;cAAKuC,SAAS,EAAC,cAAc;cAAAC,QAAA,eAC3BxC,OAAA;gBAAMuC,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,GAAEmB,OAAO,CAAC5B,UAAU,EAAC,GAAC;cAAA;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D,CAAC,eACN5C,OAAA;cAAKuC,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5BxC,OAAA;gBAAAwC,QAAA,GAAG,SAAO,EAACmB,OAAO,CAAC9B,KAAK,EAAC,GAAC,EAAC8B,OAAO,CAAC7B,cAAc;cAAA;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACtD5C,OAAA;gBAAAwC,QAAA,GAAG,QAAM,EAACmB,OAAO,CAAC3B,SAAS,EAAC,MAAI;cAAA;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN5C,OAAA;YAAKuC,SAAS,EAAC,gBAAgB;YAAAC,QAAA,eAC7BxC,OAAA;cAAMuC,SAAS,EAAE,gBACfoB,OAAO,CAAC5B,UAAU,IAAI,EAAE,GAAG,QAAQ,GAAG,QAAQ,EAC7C;cAAAS,QAAA,EACAmB,OAAO,CAAC5B,UAAU,IAAI,EAAE,GAAG,QAAQ,GAAG;YAAQ;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA,GAtBEe,OAAO,CAAChC,EAAE;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAuBf,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN5C,OAAA;MAAKuC,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChCxC,OAAA;QAAAwC,QAAA,EAAI;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACxB5C,OAAA;QAAKuC,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BxC,OAAA,CAACJ,IAAI;UAACiD,EAAE,EAAC,QAAQ;UAACN,SAAS,EAAC,aAAa;UAAAC,QAAA,gBACvCxC,OAAA;YAAKuC,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACrC5C,OAAA;YAAAwC,QAAA,EAAI;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzB5C,OAAA;YAAAwC,QAAA,EAAG;UAAwB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC,eAEP5C,OAAA,CAACJ,IAAI;UAACiD,EAAE,EAAC,kBAAkB;UAACN,SAAS,EAAC,aAAa;UAAAC,QAAA,gBACjDxC,OAAA;YAAKuC,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACrC5C,OAAA;YAAAwC,QAAA,EAAI;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzB5C,OAAA;YAAAwC,QAAA,EAAG;UAAuB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC,eAEP5C,OAAA,CAACJ,IAAI;UAACiD,EAAE,EAAC,kBAAkB;UAACN,SAAS,EAAC,aAAa;UAAAC,QAAA,gBACjDxC,OAAA;YAAKuC,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACrC5C,OAAA;YAAAwC,QAAA,EAAI;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvB5C,OAAA;YAAAwC,QAAA,EAAG;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC1C,EAAA,CAjPID,gBAAgB;EAAA,QAUHJ,WAAW;AAAA;AAAA+D,EAAA,GAVxB3D,gBAAgB;AAmPtB,eAAeA,gBAAgB;AAAC,IAAA2D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}