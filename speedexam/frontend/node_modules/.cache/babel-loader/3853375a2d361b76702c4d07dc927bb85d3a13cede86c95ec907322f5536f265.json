{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/ExamPreview.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ExamPreview = () => {\n  _s();\n  var _question$options$, _question$options$2;\n  const [exam, setExam] = useState(null);\n  const [currentQuestion, setCurrentQuestion] = useState(0);\n  useEffect(() => {\n    // Get exam data from localStorage (set by CreateExam component)\n    const previewData = localStorage.getItem('previewExam');\n    if (previewData) {\n      setExam(JSON.parse(previewData));\n    }\n  }, []);\n  if (!exam) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"No Exam to Preview\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Please create an exam first to preview it.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/admin/create-exam\",\n          className: \"btn\",\n          children: \"Create Exam\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 22,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 7\n    }, this);\n  }\n  const question = exam.questions[currentQuestion];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"exam-preview-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"preview-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"\\uD83D\\uDCCB Exam Preview\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"preview-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => window.close(),\n          className: \"btn btn-secondary\",\n          children: \"Close Preview\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/admin/create-exam\",\n          className: \"btn\",\n          children: \"Back to Editor\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"exam-info-card\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: exam.title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"exam-meta\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"meta-item\",\n          children: [\"\\uD83D\\uDCDA Subject: \", exam.subject]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"meta-item\",\n          children: [\"\\u23F0 Duration: \", exam.duration, \" minutes\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"meta-item\",\n          children: [\"\\u2753 Questions: \", exam.questions.length]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"meta-item\",\n          children: [\"\\uD83D\\uDCCA Type: \", exam.examType]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 9\n      }, this), exam.description && /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"exam-description\",\n        children: exam.description\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 11\n      }, this), exam.instructions && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"exam-instructions\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          children: \"Instructions:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: exam.instructions\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"preview-navigation\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: [\"Questions (\", exam.questions.length, \")\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"question-nav\",\n        children: exam.questions.map((_, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `nav-btn ${index === currentQuestion ? 'active' : ''}`,\n          onClick: () => setCurrentQuestion(index),\n          children: index + 1\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 7\n    }, this), question && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"question-preview\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"question-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: [\"Question \", currentQuestion + 1]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"question-badges\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: `difficulty-badge ${question.difficulty.toLowerCase()}`,\n            children: question.difficulty\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"type-badge\",\n            children: question.type\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 86,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"points-badge\",\n            children: [question.points, \" pts\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"question-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"question-text\",\n          children: question.text\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"question-options\",\n          children: question.type === 'SHORT_ANSWER' ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"answer-input\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"Student will type their answer here...\",\n              disabled: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 97,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"correct-answer\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Correct Answer:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 103,\n                columnNumber: 21\n              }, this), \" \", (_question$options$ = question.options[0]) === null || _question$options$ === void 0 ? void 0 : _question$options$.text]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 102,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 17\n          }, this) : question.type === 'FILL_BLANK' ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"fill-blank\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Fill in the blank: ___________\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 108,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"correct-answer\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Correct Answer:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 110,\n                columnNumber: 21\n              }, this), \" \", (_question$options$2 = question.options[0]) === null || _question$options$2 === void 0 ? void 0 : _question$options$2.text]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 109,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mcq-options\",\n            children: question.options.map((option, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `preview-option ${option.isCorrect ? 'correct' : ''}`,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"option-selector\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: question.type === 'MULTI_ANSWER' ? 'checkbox' : 'radio',\n                  name: `preview_question_${currentQuestion}`,\n                  checked: option.isCorrect,\n                  disabled: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 121,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"option-letter\",\n                  children: String.fromCharCode(65 + index)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 127,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 120,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"option-text\",\n                children: option.text\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 131,\n                columnNumber: 23\n              }, this), option.isCorrect && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"correct-indicator\",\n                children: \"\\u2713\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 133,\n                columnNumber: 25\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 116,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 114,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 13\n        }, this), question.explanation && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"question-explanation\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"Explanation:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: question.explanation\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"preview-controls\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setCurrentQuestion(Math.max(0, currentQuestion - 1)),\n          disabled: currentQuestion === 0,\n          className: \"btn btn-secondary\",\n          children: \"\\u2190 Previous\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"question-counter\",\n          children: [currentQuestion + 1, \" of \", exam.questions.length]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setCurrentQuestion(Math.min(exam.questions.length - 1, currentQuestion + 1)),\n          disabled: currentQuestion === exam.questions.length - 1,\n          className: \"btn btn-secondary\",\n          children: \"Next \\u2192\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 79,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"exam-summary\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"\\uD83D\\uDCCA Exam Summary\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 174,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"summary-stats\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"Question Types\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"type-breakdown\",\n            children: ['MCQ', 'MULTI_ANSWER', 'TRUE_FALSE', 'FILL_BLANK', 'SHORT_ANSWER'].map(type => {\n              const count = exam.questions.filter(q => q.type === type).length;\n              return count > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"type-stat\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"type-name\",\n                  children: type.replace('_', ' ')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 183,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"type-count\",\n                  children: count\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 184,\n                  columnNumber: 21\n                }, this)]\n              }, type, true, {\n                fileName: _jsxFileName,\n                lineNumber: 182,\n                columnNumber: 19\n              }, this) : null;\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"Difficulty Distribution\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"difficulty-breakdown\",\n            children: ['EASY', 'MEDIUM', 'HARD'].map(difficulty => {\n              const count = exam.questions.filter(q => q.difficulty === difficulty).length;\n              return /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"difficulty-stat\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `difficulty-name ${difficulty.toLowerCase()}`,\n                  children: difficulty\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 198,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"difficulty-count\",\n                  children: count\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 201,\n                  columnNumber: 21\n                }, this)]\n              }, difficulty, true, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"Scoring\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"scoring-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"score-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Total Points:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: exam.questions.reduce((sum, q) => sum + q.points, 0)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 213,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"score-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Passing Score:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 216,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [exam.passingScore, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 217,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"score-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Average Points per Question:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: (exam.questions.reduce((sum, q) => sum + q.points, 0) / exam.questions.length).toFixed(1)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 175,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 173,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"preview-footer\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"footer-note\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\uD83D\\uDCDD This is a preview of how the exam will appear to students.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u2705 Correct answers are highlighted for your reference.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"footer-actions\",\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          to: \"/admin/create-exam\",\n          className: \"btn btn-primary\",\n          children: \"\\u270F\\uFE0F Edit Exam\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => {\n            if (window.confirm('Are you ready to publish this exam?')) {\n              // In a real app, this would call the API to save the exam\n              alert('Exam would be published in a real application!');\n            }\n          },\n          className: \"btn btn-success\",\n          children: \"\\uD83D\\uDE80 Publish Exam\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 234,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 228,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 31,\n    columnNumber: 5\n  }, this);\n};\n_s(ExamPreview, \"rmawkyQmT5PLWlcmcaqn75IE/2Y=\");\n_c = ExamPreview;\nexport default ExamPreview;\nvar _c;\n$RefreshReg$(_c, \"ExamPreview\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "jsxDEV", "_jsxDEV", "ExamPreview", "_s", "_question$options$", "_question$options$2", "exam", "setExam", "currentQuestion", "setCurrentQuestion", "previewData", "localStorage", "getItem", "JSON", "parse", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "question", "questions", "onClick", "window", "close", "title", "subject", "duration", "length", "examType", "description", "instructions", "map", "_", "index", "difficulty", "toLowerCase", "type", "points", "text", "placeholder", "disabled", "options", "option", "isCorrect", "name", "checked", "String", "fromCharCode", "explanation", "Math", "max", "min", "count", "filter", "q", "replace", "reduce", "sum", "passingScore", "toFixed", "confirm", "alert", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/ExamPreview.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\n\nconst ExamPreview = () => {\n  const [exam, setExam] = useState(null);\n  const [currentQuestion, setCurrentQuestion] = useState(0);\n\n  useEffect(() => {\n    // Get exam data from localStorage (set by CreateExam component)\n    const previewData = localStorage.getItem('previewExam');\n    if (previewData) {\n      setExam(JSON.parse(previewData));\n    }\n  }, []);\n\n  if (!exam) {\n    return (\n      <div className=\"container\">\n        <div className=\"error\">\n          <h2>No Exam to Preview</h2>\n          <p>Please create an exam first to preview it.</p>\n          <Link to=\"/admin/create-exam\" className=\"btn\">Create Exam</Link>\n        </div>\n      </div>\n    );\n  }\n\n  const question = exam.questions[currentQuestion];\n\n  return (\n    <div className=\"exam-preview-container\">\n      <div className=\"preview-header\">\n        <h1>📋 Exam Preview</h1>\n        <div className=\"preview-actions\">\n          <button onClick={() => window.close()} className=\"btn btn-secondary\">\n            Close Preview\n          </button>\n          <Link to=\"/admin/create-exam\" className=\"btn\">\n            Back to Editor\n          </Link>\n        </div>\n      </div>\n\n      <div className=\"exam-info-card\">\n        <h2>{exam.title}</h2>\n        <div className=\"exam-meta\">\n          <span className=\"meta-item\">📚 Subject: {exam.subject}</span>\n          <span className=\"meta-item\">⏰ Duration: {exam.duration} minutes</span>\n          <span className=\"meta-item\">❓ Questions: {exam.questions.length}</span>\n          <span className=\"meta-item\">📊 Type: {exam.examType}</span>\n        </div>\n        {exam.description && (\n          <p className=\"exam-description\">{exam.description}</p>\n        )}\n        {exam.instructions && (\n          <div className=\"exam-instructions\">\n            <h4>Instructions:</h4>\n            <p>{exam.instructions}</p>\n          </div>\n        )}\n      </div>\n\n      <div className=\"preview-navigation\">\n        <h3>Questions ({exam.questions.length})</h3>\n        <div className=\"question-nav\">\n          {exam.questions.map((_, index) => (\n            <button\n              key={index}\n              className={`nav-btn ${index === currentQuestion ? 'active' : ''}`}\n              onClick={() => setCurrentQuestion(index)}\n            >\n              {index + 1}\n            </button>\n          ))}\n        </div>\n      </div>\n\n      {question && (\n        <div className=\"question-preview\">\n          <div className=\"question-header\">\n            <h3>Question {currentQuestion + 1}</h3>\n            <div className=\"question-badges\">\n              <span className={`difficulty-badge ${question.difficulty.toLowerCase()}`}>\n                {question.difficulty}\n              </span>\n              <span className=\"type-badge\">{question.type}</span>\n              <span className=\"points-badge\">{question.points} pts</span>\n            </div>\n          </div>\n\n          <div className=\"question-content\">\n            <div className=\"question-text\">{question.text}</div>\n            \n            <div className=\"question-options\">\n              {question.type === 'SHORT_ANSWER' ? (\n                <div className=\"answer-input\">\n                  <input \n                    type=\"text\" \n                    placeholder=\"Student will type their answer here...\"\n                    disabled\n                  />\n                  <div className=\"correct-answer\">\n                    <strong>Correct Answer:</strong> {question.options[0]?.text}\n                  </div>\n                </div>\n              ) : question.type === 'FILL_BLANK' ? (\n                <div className=\"fill-blank\">\n                  <p>Fill in the blank: ___________</p>\n                  <div className=\"correct-answer\">\n                    <strong>Correct Answer:</strong> {question.options[0]?.text}\n                  </div>\n                </div>\n              ) : (\n                <div className=\"mcq-options\">\n                  {question.options.map((option, index) => (\n                    <div \n                      key={index} \n                      className={`preview-option ${option.isCorrect ? 'correct' : ''}`}\n                    >\n                      <div className=\"option-selector\">\n                        <input\n                          type={question.type === 'MULTI_ANSWER' ? 'checkbox' : 'radio'}\n                          name={`preview_question_${currentQuestion}`}\n                          checked={option.isCorrect}\n                          disabled\n                        />\n                        <span className=\"option-letter\">\n                          {String.fromCharCode(65 + index)}\n                        </span>\n                      </div>\n                      <div className=\"option-text\">{option.text}</div>\n                      {option.isCorrect && (\n                        <div className=\"correct-indicator\">✓</div>\n                      )}\n                    </div>\n                  ))}\n                </div>\n              )}\n            </div>\n\n            {question.explanation && (\n              <div className=\"question-explanation\">\n                <h4>Explanation:</h4>\n                <p>{question.explanation}</p>\n              </div>\n            )}\n          </div>\n\n          <div className=\"preview-controls\">\n            <button \n              onClick={() => setCurrentQuestion(Math.max(0, currentQuestion - 1))}\n              disabled={currentQuestion === 0}\n              className=\"btn btn-secondary\"\n            >\n              ← Previous\n            </button>\n            \n            <span className=\"question-counter\">\n              {currentQuestion + 1} of {exam.questions.length}\n            </span>\n            \n            <button \n              onClick={() => setCurrentQuestion(Math.min(exam.questions.length - 1, currentQuestion + 1))}\n              disabled={currentQuestion === exam.questions.length - 1}\n              className=\"btn btn-secondary\"\n            >\n              Next →\n            </button>\n          </div>\n        </div>\n      )}\n\n      <div className=\"exam-summary\">\n        <h3>📊 Exam Summary</h3>\n        <div className=\"summary-stats\">\n          <div className=\"stat-group\">\n            <h4>Question Types</h4>\n            <div className=\"type-breakdown\">\n              {['MCQ', 'MULTI_ANSWER', 'TRUE_FALSE', 'FILL_BLANK', 'SHORT_ANSWER'].map(type => {\n                const count = exam.questions.filter(q => q.type === type).length;\n                return count > 0 ? (\n                  <div key={type} className=\"type-stat\">\n                    <span className=\"type-name\">{type.replace('_', ' ')}</span>\n                    <span className=\"type-count\">{count}</span>\n                  </div>\n                ) : null;\n              })}\n            </div>\n          </div>\n\n          <div className=\"stat-group\">\n            <h4>Difficulty Distribution</h4>\n            <div className=\"difficulty-breakdown\">\n              {['EASY', 'MEDIUM', 'HARD'].map(difficulty => {\n                const count = exam.questions.filter(q => q.difficulty === difficulty).length;\n                return (\n                  <div key={difficulty} className=\"difficulty-stat\">\n                    <span className={`difficulty-name ${difficulty.toLowerCase()}`}>\n                      {difficulty}\n                    </span>\n                    <span className=\"difficulty-count\">{count}</span>\n                  </div>\n                );\n              })}\n            </div>\n          </div>\n\n          <div className=\"stat-group\">\n            <h4>Scoring</h4>\n            <div className=\"scoring-info\">\n              <div className=\"score-item\">\n                <span>Total Points:</span>\n                <span>{exam.questions.reduce((sum, q) => sum + q.points, 0)}</span>\n              </div>\n              <div className=\"score-item\">\n                <span>Passing Score:</span>\n                <span>{exam.passingScore}%</span>\n              </div>\n              <div className=\"score-item\">\n                <span>Average Points per Question:</span>\n                <span>{(exam.questions.reduce((sum, q) => sum + q.points, 0) / exam.questions.length).toFixed(1)}</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"preview-footer\">\n        <div className=\"footer-note\">\n          <p>📝 This is a preview of how the exam will appear to students.</p>\n          <p>✅ Correct answers are highlighted for your reference.</p>\n        </div>\n        \n        <div className=\"footer-actions\">\n          <Link to=\"/admin/create-exam\" className=\"btn btn-primary\">\n            ✏️ Edit Exam\n          </Link>\n          <button \n            onClick={() => {\n              if (window.confirm('Are you ready to publish this exam?')) {\n                // In a real app, this would call the API to save the exam\n                alert('Exam would be published in a real application!');\n              }\n            }}\n            className=\"btn btn-success\"\n          >\n            🚀 Publish Exam\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ExamPreview;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,kBAAA,EAAAC,mBAAA;EACxB,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACW,eAAe,EAAEC,kBAAkB,CAAC,GAAGZ,QAAQ,CAAC,CAAC,CAAC;EAEzDC,SAAS,CAAC,MAAM;IACd;IACA,MAAMY,WAAW,GAAGC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;IACvD,IAAIF,WAAW,EAAE;MACfH,OAAO,CAACM,IAAI,CAACC,KAAK,CAACJ,WAAW,CAAC,CAAC;IAClC;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,IAAI,CAACJ,IAAI,EAAE;IACT,oBACEL,OAAA;MAAKc,SAAS,EAAC,WAAW;MAAAC,QAAA,eACxBf,OAAA;QAAKc,SAAS,EAAC,OAAO;QAAAC,QAAA,gBACpBf,OAAA;UAAAe,QAAA,EAAI;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3BnB,OAAA;UAAAe,QAAA,EAAG;QAA0C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACjDnB,OAAA,CAACF,IAAI;UAACsB,EAAE,EAAC,oBAAoB;UAACN,SAAS,EAAC,KAAK;UAAAC,QAAA,EAAC;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7D;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,MAAME,QAAQ,GAAGhB,IAAI,CAACiB,SAAS,CAACf,eAAe,CAAC;EAEhD,oBACEP,OAAA;IAAKc,SAAS,EAAC,wBAAwB;IAAAC,QAAA,gBACrCf,OAAA;MAAKc,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7Bf,OAAA;QAAAe,QAAA,EAAI;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACxBnB,OAAA;QAAKc,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9Bf,OAAA;UAAQuB,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,KAAK,CAAC,CAAE;UAACX,SAAS,EAAC,mBAAmB;UAAAC,QAAA,EAAC;QAErE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTnB,OAAA,CAACF,IAAI;UAACsB,EAAE,EAAC,oBAAoB;UAACN,SAAS,EAAC,KAAK;UAAAC,QAAA,EAAC;QAE9C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENnB,OAAA;MAAKc,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7Bf,OAAA;QAAAe,QAAA,EAAKV,IAAI,CAACqB;MAAK;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACrBnB,OAAA;QAAKc,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBf,OAAA;UAAMc,SAAS,EAAC,WAAW;UAAAC,QAAA,GAAC,wBAAY,EAACV,IAAI,CAACsB,OAAO;QAAA;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC7DnB,OAAA;UAAMc,SAAS,EAAC,WAAW;UAAAC,QAAA,GAAC,mBAAY,EAACV,IAAI,CAACuB,QAAQ,EAAC,UAAQ;QAAA;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtEnB,OAAA;UAAMc,SAAS,EAAC,WAAW;UAAAC,QAAA,GAAC,oBAAa,EAACV,IAAI,CAACiB,SAAS,CAACO,MAAM;QAAA;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACvEnB,OAAA;UAAMc,SAAS,EAAC,WAAW;UAAAC,QAAA,GAAC,qBAAS,EAACV,IAAI,CAACyB,QAAQ;QAAA;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD,CAAC,EACLd,IAAI,CAAC0B,WAAW,iBACf/B,OAAA;QAAGc,SAAS,EAAC,kBAAkB;QAAAC,QAAA,EAAEV,IAAI,CAAC0B;MAAW;QAAAf,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CACtD,EACAd,IAAI,CAAC2B,YAAY,iBAChBhC,OAAA;QAAKc,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCf,OAAA;UAAAe,QAAA,EAAI;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtBnB,OAAA;UAAAe,QAAA,EAAIV,IAAI,CAAC2B;QAAY;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvB,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAENnB,OAAA;MAAKc,SAAS,EAAC,oBAAoB;MAAAC,QAAA,gBACjCf,OAAA;QAAAe,QAAA,GAAI,aAAW,EAACV,IAAI,CAACiB,SAAS,CAACO,MAAM,EAAC,GAAC;MAAA;QAAAb,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC5CnB,OAAA;QAAKc,SAAS,EAAC,cAAc;QAAAC,QAAA,EAC1BV,IAAI,CAACiB,SAAS,CAACW,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK,kBAC3BnC,OAAA;UAEEc,SAAS,EAAE,WAAWqB,KAAK,KAAK5B,eAAe,GAAG,QAAQ,GAAG,EAAE,EAAG;UAClEgB,OAAO,EAAEA,CAAA,KAAMf,kBAAkB,CAAC2B,KAAK,CAAE;UAAApB,QAAA,EAExCoB,KAAK,GAAG;QAAC,GAJLA,KAAK;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAKJ,CACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAELE,QAAQ,iBACPrB,OAAA;MAAKc,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/Bf,OAAA;QAAKc,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9Bf,OAAA;UAAAe,QAAA,GAAI,WAAS,EAACR,eAAe,GAAG,CAAC;QAAA;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACvCnB,OAAA;UAAKc,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9Bf,OAAA;YAAMc,SAAS,EAAE,oBAAoBO,QAAQ,CAACe,UAAU,CAACC,WAAW,CAAC,CAAC,EAAG;YAAAtB,QAAA,EACtEM,QAAQ,CAACe;UAAU;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC,eACPnB,OAAA;YAAMc,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAEM,QAAQ,CAACiB;UAAI;YAAAtB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACnDnB,OAAA;YAAMc,SAAS,EAAC,cAAc;YAAAC,QAAA,GAAEM,QAAQ,CAACkB,MAAM,EAAC,MAAI;UAAA;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENnB,OAAA;QAAKc,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/Bf,OAAA;UAAKc,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAEM,QAAQ,CAACmB;QAAI;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAEpDnB,OAAA;UAAKc,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAC9BM,QAAQ,CAACiB,IAAI,KAAK,cAAc,gBAC/BtC,OAAA;YAAKc,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3Bf,OAAA;cACEsC,IAAI,EAAC,MAAM;cACXG,WAAW,EAAC,wCAAwC;cACpDC,QAAQ;YAAA;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACFnB,OAAA;cAAKc,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7Bf,OAAA;gBAAAe,QAAA,EAAQ;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,GAAAhB,kBAAA,GAACkB,QAAQ,CAACsB,OAAO,CAAC,CAAC,CAAC,cAAAxC,kBAAA,uBAAnBA,kBAAA,CAAqBqC,IAAI;YAAA;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,GACJE,QAAQ,CAACiB,IAAI,KAAK,YAAY,gBAChCtC,OAAA;YAAKc,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBf,OAAA;cAAAe,QAAA,EAAG;YAA8B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACrCnB,OAAA;cAAKc,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7Bf,OAAA;gBAAAe,QAAA,EAAQ;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,GAAAf,mBAAA,GAACiB,QAAQ,CAACsB,OAAO,CAAC,CAAC,CAAC,cAAAvC,mBAAA,uBAAnBA,mBAAA,CAAqBoC,IAAI;YAAA;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,gBAENnB,OAAA;YAAKc,SAAS,EAAC,aAAa;YAAAC,QAAA,EACzBM,QAAQ,CAACsB,OAAO,CAACV,GAAG,CAAC,CAACW,MAAM,EAAET,KAAK,kBAClCnC,OAAA;cAEEc,SAAS,EAAE,kBAAkB8B,MAAM,CAACC,SAAS,GAAG,SAAS,GAAG,EAAE,EAAG;cAAA9B,QAAA,gBAEjEf,OAAA;gBAAKc,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9Bf,OAAA;kBACEsC,IAAI,EAAEjB,QAAQ,CAACiB,IAAI,KAAK,cAAc,GAAG,UAAU,GAAG,OAAQ;kBAC9DQ,IAAI,EAAE,oBAAoBvC,eAAe,EAAG;kBAC5CwC,OAAO,EAAEH,MAAM,CAACC,SAAU;kBAC1BH,QAAQ;gBAAA;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC,eACFnB,OAAA;kBAAMc,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAC5BiC,MAAM,CAACC,YAAY,CAAC,EAAE,GAAGd,KAAK;gBAAC;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNnB,OAAA;gBAAKc,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAE6B,MAAM,CAACJ;cAAI;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EAC/CyB,MAAM,CAACC,SAAS,iBACf7C,OAAA;gBAAKc,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAC1C;YAAA,GAjBIgB,KAAK;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAkBP,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,EAELE,QAAQ,CAAC6B,WAAW,iBACnBlD,OAAA;UAAKc,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBACnCf,OAAA;YAAAe,QAAA,EAAI;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrBnB,OAAA;YAAAe,QAAA,EAAIM,QAAQ,CAAC6B;UAAW;YAAAlC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENnB,OAAA;QAAKc,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/Bf,OAAA;UACEuB,OAAO,EAAEA,CAAA,KAAMf,kBAAkB,CAAC2C,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE7C,eAAe,GAAG,CAAC,CAAC,CAAE;UACpEmC,QAAQ,EAAEnC,eAAe,KAAK,CAAE;UAChCO,SAAS,EAAC,mBAAmB;UAAAC,QAAA,EAC9B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAETnB,OAAA;UAAMc,SAAS,EAAC,kBAAkB;UAAAC,QAAA,GAC/BR,eAAe,GAAG,CAAC,EAAC,MAAI,EAACF,IAAI,CAACiB,SAAS,CAACO,MAAM;QAAA;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CAAC,eAEPnB,OAAA;UACEuB,OAAO,EAAEA,CAAA,KAAMf,kBAAkB,CAAC2C,IAAI,CAACE,GAAG,CAAChD,IAAI,CAACiB,SAAS,CAACO,MAAM,GAAG,CAAC,EAAEtB,eAAe,GAAG,CAAC,CAAC,CAAE;UAC5FmC,QAAQ,EAAEnC,eAAe,KAAKF,IAAI,CAACiB,SAAS,CAACO,MAAM,GAAG,CAAE;UACxDf,SAAS,EAAC,mBAAmB;UAAAC,QAAA,EAC9B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAEDnB,OAAA;MAAKc,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC3Bf,OAAA;QAAAe,QAAA,EAAI;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACxBnB,OAAA;QAAKc,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5Bf,OAAA;UAAKc,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBf,OAAA;YAAAe,QAAA,EAAI;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvBnB,OAAA;YAAKc,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAC5B,CAAC,KAAK,EAAE,cAAc,EAAE,YAAY,EAAE,YAAY,EAAE,cAAc,CAAC,CAACkB,GAAG,CAACK,IAAI,IAAI;cAC/E,MAAMgB,KAAK,GAAGjD,IAAI,CAACiB,SAAS,CAACiC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAClB,IAAI,KAAKA,IAAI,CAAC,CAACT,MAAM;cAChE,OAAOyB,KAAK,GAAG,CAAC,gBACdtD,OAAA;gBAAgBc,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACnCf,OAAA;kBAAMc,SAAS,EAAC,WAAW;kBAAAC,QAAA,EAAEuB,IAAI,CAACmB,OAAO,CAAC,GAAG,EAAE,GAAG;gBAAC;kBAAAzC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC3DnB,OAAA;kBAAMc,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAEuC;gBAAK;kBAAAtC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA,GAFnCmB,IAAI;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAGT,CAAC,GACJ,IAAI;YACV,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENnB,OAAA;UAAKc,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBf,OAAA;YAAAe,QAAA,EAAI;UAAuB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChCnB,OAAA;YAAKc,SAAS,EAAC,sBAAsB;YAAAC,QAAA,EAClC,CAAC,MAAM,EAAE,QAAQ,EAAE,MAAM,CAAC,CAACkB,GAAG,CAACG,UAAU,IAAI;cAC5C,MAAMkB,KAAK,GAAGjD,IAAI,CAACiB,SAAS,CAACiC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACpB,UAAU,KAAKA,UAAU,CAAC,CAACP,MAAM;cAC5E,oBACE7B,OAAA;gBAAsBc,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC/Cf,OAAA;kBAAMc,SAAS,EAAE,mBAAmBsB,UAAU,CAACC,WAAW,CAAC,CAAC,EAAG;kBAAAtB,QAAA,EAC5DqB;gBAAU;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC,eACPnB,OAAA;kBAAMc,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,EAAEuC;gBAAK;kBAAAtC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA,GAJzCiB,UAAU;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAKf,CAAC;YAEV,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENnB,OAAA;UAAKc,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBf,OAAA;YAAAe,QAAA,EAAI;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChBnB,OAAA;YAAKc,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3Bf,OAAA;cAAKc,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBf,OAAA;gBAAAe,QAAA,EAAM;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC1BnB,OAAA;gBAAAe,QAAA,EAAOV,IAAI,CAACiB,SAAS,CAACoC,MAAM,CAAC,CAACC,GAAG,EAAEH,CAAC,KAAKG,GAAG,GAAGH,CAAC,CAACjB,MAAM,EAAE,CAAC;cAAC;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChE,CAAC,eACNnB,OAAA;cAAKc,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBf,OAAA;gBAAAe,QAAA,EAAM;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC3BnB,OAAA;gBAAAe,QAAA,GAAOV,IAAI,CAACuD,YAAY,EAAC,GAAC;cAAA;gBAAA5C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC,eACNnB,OAAA;cAAKc,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBf,OAAA;gBAAAe,QAAA,EAAM;cAA4B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACzCnB,OAAA;gBAAAe,QAAA,EAAO,CAACV,IAAI,CAACiB,SAAS,CAACoC,MAAM,CAAC,CAACC,GAAG,EAAEH,CAAC,KAAKG,GAAG,GAAGH,CAAC,CAACjB,MAAM,EAAE,CAAC,CAAC,GAAGlC,IAAI,CAACiB,SAAS,CAACO,MAAM,EAAEgC,OAAO,CAAC,CAAC;cAAC;gBAAA7C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENnB,OAAA;MAAKc,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7Bf,OAAA;QAAKc,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1Bf,OAAA;UAAAe,QAAA,EAAG;QAA6D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACpEnB,OAAA;UAAAe,QAAA,EAAG;QAAqD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzD,CAAC,eAENnB,OAAA;QAAKc,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7Bf,OAAA,CAACF,IAAI;UAACsB,EAAE,EAAC,oBAAoB;UAACN,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAC;QAE1D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACPnB,OAAA;UACEuB,OAAO,EAAEA,CAAA,KAAM;YACb,IAAIC,MAAM,CAACsC,OAAO,CAAC,qCAAqC,CAAC,EAAE;cACzD;cACAC,KAAK,CAAC,gDAAgD,CAAC;YACzD;UACF,CAAE;UACFjD,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAC5B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjB,EAAA,CAzPID,WAAW;AAAA+D,EAAA,GAAX/D,WAAW;AA2PjB,eAAeA,WAAW;AAAC,IAAA+D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}