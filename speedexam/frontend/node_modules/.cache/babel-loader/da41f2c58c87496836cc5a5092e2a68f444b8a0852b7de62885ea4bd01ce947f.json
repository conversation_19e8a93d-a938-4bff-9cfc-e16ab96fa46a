{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/ExamTaking.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport api from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ExamTaking = () => {\n  _s();\n  var _exam$subject, _answers$question$_id2;\n  const {\n    examId\n  } = useParams();\n  const navigate = useNavigate();\n  const [exam, setExam] = useState(null);\n  const [attemptId, setAttemptId] = useState(null);\n  const [answers, setAnswers] = useState({});\n  const [currentQuestion, setCurrentQuestion] = useState(0);\n  const [timeLeft, setTimeLeft] = useState(0);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [submitting, setSubmitting] = useState(false);\n  const [examStartTime, setExamStartTime] = useState(null);\n  const [questionTimes, setQuestionTimes] = useState({});\n  const [showInstructions, setShowInstructions] = useState(true);\n  const [isFullscreen, setIsFullscreen] = useState(false);\n  const [warningCount, setWarningCount] = useState(0);\n  const [questionStartTime, setQuestionStartTime] = useState(null);\n  useEffect(() => {\n    const token = localStorage.getItem('token');\n    if (!token) {\n      navigate('/login');\n      return;\n    }\n    startExam();\n  }, [examId, navigate]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  useEffect(() => {\n    if (timeLeft > 0) {\n      const timer = setTimeout(() => setTimeLeft(timeLeft - 1), 1000);\n      return () => clearTimeout(timer);\n    } else if (timeLeft === 0 && exam) {\n      handleSubmit();\n    }\n  }, [timeLeft, exam]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  const startExam = async () => {\n    try {\n      console.log('Starting exam with ID:', examId);\n      const response = await api.post(`/exams/${examId}/start`);\n      console.log('Start exam response:', response.data);\n      setExam(response.data.exam);\n      setAttemptId(response.data.attempt);\n      setTimeLeft(response.data.exam.duration * 60); // Convert minutes to seconds\n      setExamStartTime(Date.now());\n      setQuestionStartTime(Date.now());\n\n      // Initialize answers\n      const initialAnswers = {};\n      response.data.exam.questions.forEach(question => {\n        initialAnswers[question._id] = {\n          question: question._id,\n          selectedOptions: [],\n          answer: ''\n        };\n      });\n      setAnswers(initialAnswers);\n\n      // Initialize question times\n      const initialTimes = {};\n      response.data.exam.questions.forEach(question => {\n        initialTimes[question._id] = 0;\n      });\n      setQuestionTimes(initialTimes);\n      console.log('Exam started successfully');\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('Error starting exam:', error);\n      setError(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Failed to start exam');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleAnswerChange = (questionId, value, isMultiple = false) => {\n    setAnswers(prev => ({\n      ...prev,\n      [questionId]: {\n        ...prev[questionId],\n        selectedOptions: isMultiple ? prev[questionId].selectedOptions.includes(value) ? prev[questionId].selectedOptions.filter(opt => opt !== value) : [...prev[questionId].selectedOptions, value] : [value]\n      }\n    }));\n  };\n  const handleSubmit = async () => {\n    if (submitting) return;\n    setSubmitting(true);\n    try {\n      const answersArray = Object.values(answers);\n      await api.post(`/exam-attempts/${attemptId}/submit`, {\n        answers: answersArray\n      });\n      navigate(`/results/${attemptId}`);\n    } catch (error) {\n      setError('Failed to submit exam');\n      setSubmitting(false);\n    }\n  };\n  const formatTime = seconds => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins}:${secs.toString().padStart(2, '0')}`;\n  };\n  const trackQuestionTime = questionId => {\n    if (questionStartTime && questionId) {\n      const timeSpent = Math.floor((Date.now() - questionStartTime) / 1000);\n      setQuestionTimes(prev => ({\n        ...prev,\n        [questionId]: (prev[questionId] || 0) + timeSpent\n      }));\n    }\n  };\n  const navigateToQuestion = questionIndex => {\n    if (exam && exam.questions[currentQuestion]) {\n      trackQuestionTime(exam.questions[currentQuestion]._id);\n    }\n    setCurrentQuestion(questionIndex);\n    setQuestionStartTime(Date.now());\n  };\n  const toggleFullscreen = () => {\n    if (!isFullscreen) {\n      var _document$documentEle, _document$documentEle2;\n      (_document$documentEle = (_document$documentEle2 = document.documentElement).requestFullscreen) === null || _document$documentEle === void 0 ? void 0 : _document$documentEle.call(_document$documentEle2);\n      setIsFullscreen(true);\n    } else {\n      var _document$exitFullscr, _document;\n      (_document$exitFullscr = (_document = document).exitFullscreen) === null || _document$exitFullscr === void 0 ? void 0 : _document$exitFullscr.call(_document);\n      setIsFullscreen(false);\n    }\n  };\n  const handleTabSwitch = () => {\n    setWarningCount(prev => prev + 1);\n    if (warningCount >= 2) {\n      alert('Warning: Multiple tab switches detected. This exam will be auto-submitted for security.');\n      handleSubmit();\n    } else {\n      alert(`Warning ${warningCount + 1}/3: Please stay on this tab during the exam.`);\n    }\n  };\n\n  // Add event listeners for tab switching detection\n  useEffect(() => {\n    const handleVisibilityChange = () => {\n      if (document.hidden && exam && !submitting) {\n        handleTabSwitch();\n      }\n    };\n    document.addEventListener('visibilitychange', handleVisibilityChange);\n    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);\n  }, [exam, submitting, warningCount]);\n  const startExamProper = () => {\n    setShowInstructions(false);\n    setQuestionStartTime(Date.now());\n  };\n  if (loading) return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"loading\",\n    children: \"Starting exam...\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 170,\n    columnNumber: 23\n  }, this);\n  if (error) return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"error\",\n    children: error\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 171,\n    columnNumber: 21\n  }, this);\n  if (!exam) return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"error\",\n    children: \"Exam not found\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 172,\n    columnNumber: 21\n  }, this);\n  const question = exam.questions[currentQuestion];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"timer\",\n      children: [\"Time Left: \", formatTime(timeLeft)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 179,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"exam-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: exam.title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 185,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Subject:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 12\n        }, this), \" \", (_exam$subject = exam.subject) === null || _exam$subject === void 0 ? void 0 : _exam$subject.name]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 186,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [\"Question \", currentQuestion + 1, \" of \", exam.questions.length]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 184,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"question-nav\",\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        children: /*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Questions:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 12\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 192,\n        columnNumber: 9\n      }, this), exam.questions.map((_, index) => {\n        var _answers$exam$questio;\n        return /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `nav-btn ${index === currentQuestion ? 'active' : ''} ${((_answers$exam$questio = answers[exam.questions[index]._id]) === null || _answers$exam$questio === void 0 ? void 0 : _answers$exam$questio.selectedOptions.length) > 0 ? 'answered' : ''}`,\n          onClick: () => setCurrentQuestion(index),\n          children: index + 1\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 11\n        }, this);\n      })]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 191,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"question-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"question-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: [\"Question \", currentQuestion + 1]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"question-type\",\n          children: question.type === 'MCQ' ? 'Multiple Choice' : question.type === 'TRUE_FALSE' ? 'True/False' : 'Multiple Answer'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 208,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"question-text\",\n        children: question.text\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 217,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"options\",\n        children: question.options.map((option, index) => {\n          var _answers$question$_id;\n          const isSelected = (_answers$question$_id = answers[question._id]) === null || _answers$question$_id === void 0 ? void 0 : _answers$question$_id.selectedOptions.includes(option.text);\n          const optionLetter = String.fromCharCode(65 + index);\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `option ${isSelected ? 'selected' : ''}`,\n            children: /*#__PURE__*/_jsxDEV(\"label\", {\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: question.type === 'MULTI_ANSWER' ? 'checkbox' : 'radio',\n                name: `question_${question._id}`,\n                value: option.text,\n                checked: isSelected,\n                onChange: e => handleAnswerChange(question._id, option.text, question.type === 'MULTI_ANSWER')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 227,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"option-letter\",\n                children: [optionLetter, \".\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"option-text\",\n                children: option.text\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 17\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 15\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 219,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"answer-status\",\n        children: ((_answers$question$_id2 = answers[question._id]) === null || _answers$question$_id2 === void 0 ? void 0 : _answers$question$_id2.selectedOptions.length) > 0 ? /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"answered\",\n          children: \"\\u2713 Answered\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"unanswered\",\n          children: \"\\u26A0 Not answered\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 247,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 207,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"navigation\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn btn-secondary\",\n        onClick: () => setCurrentQuestion(Math.max(0, currentQuestion - 1)),\n        disabled: currentQuestion === 0,\n        children: \"\\u2190 Previous\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 258,\n        columnNumber: 9\n      }, this), currentQuestion < exam.questions.length - 1 ? /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn\",\n        onClick: () => setCurrentQuestion(currentQuestion + 1),\n        children: \"Next \\u2192\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 267,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn btn-success\",\n        onClick: handleSubmit,\n        disabled: submitting,\n        children: submitting ? 'Submitting...' : 'Submit Exam'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 274,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 257,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"exam-summary\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        children: \"Exam Progress\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 286,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [\"Answered: \", Object.values(answers).filter(a => a.selectedOptions.length > 0).length, \" / \", exam.questions.length]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 287,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"progress-bar\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"progress\",\n          style: {\n            width: `${Object.values(answers).filter(a => a.selectedOptions.length > 0).length / exam.questions.length * 100}%`\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 291,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 290,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 285,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 177,\n    columnNumber: 5\n  }, this);\n};\n_s(ExamTaking, \"mn9lSx9B9gFKUXu2XCdVINe+1Cg=\", false, function () {\n  return [useParams, useNavigate];\n});\n_c = ExamTaking;\nexport default ExamTaking;\nvar _c;\n$RefreshReg$(_c, \"ExamTaking\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useNavigate", "api", "jsxDEV", "_jsxDEV", "ExamTaking", "_s", "_exam$subject", "_answers$question$_id2", "examId", "navigate", "exam", "setExam", "attemptId", "setAttemptId", "answers", "setAnswers", "currentQuestion", "setCurrentQuestion", "timeLeft", "setTimeLeft", "loading", "setLoading", "error", "setError", "submitting", "setSubmitting", "examStartTime", "setExamStartTime", "questionTimes", "setQuestionTimes", "showInstructions", "setShowInstructions", "isFullscreen", "setIsFullscreen", "warningCount", "setWarningCount", "questionStartTime", "setQuestionStartTime", "token", "localStorage", "getItem", "startExam", "timer", "setTimeout", "clearTimeout", "handleSubmit", "console", "log", "response", "post", "data", "attempt", "duration", "Date", "now", "initialAnswers", "questions", "for<PERSON>ach", "question", "_id", "selectedOptions", "answer", "initialTimes", "_error$response", "_error$response$data", "message", "handleAnswerChange", "questionId", "value", "isMultiple", "prev", "includes", "filter", "opt", "answersArray", "Object", "values", "formatTime", "seconds", "mins", "Math", "floor", "secs", "toString", "padStart", "trackQuestionTime", "timeSpent", "navigateToQuestion", "questionIndex", "toggleFullscreen", "_document$documentEle", "_document$documentEle2", "document", "documentElement", "requestFullscreen", "call", "_document$exitFullscr", "_document", "exitFullscreen", "handleTabSwitch", "alert", "handleVisibilityChange", "hidden", "addEventListener", "removeEventListener", "startExamProper", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "title", "subject", "name", "length", "map", "_", "index", "_answers$exam$questio", "onClick", "type", "text", "options", "option", "_answers$question$_id", "isSelected", "optionLetter", "String", "fromCharCode", "checked", "onChange", "e", "max", "disabled", "a", "style", "width", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/ExamTaking.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport api from '../services/api';\n\nconst ExamTaking = () => {\n  const { examId } = useParams();\n  const navigate = useNavigate();\n  const [exam, setExam] = useState(null);\n  const [attemptId, setAttemptId] = useState(null);\n  const [answers, setAnswers] = useState({});\n  const [currentQuestion, setCurrentQuestion] = useState(0);\n  const [timeLeft, setTimeLeft] = useState(0);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [submitting, setSubmitting] = useState(false);\n  const [examStartTime, setExamStartTime] = useState(null);\n  const [questionTimes, setQuestionTimes] = useState({});\n  const [showInstructions, setShowInstructions] = useState(true);\n  const [isFullscreen, setIsFullscreen] = useState(false);\n  const [warningCount, setWarningCount] = useState(0);\n  const [questionStartTime, setQuestionStartTime] = useState(null);\n\n  useEffect(() => {\n    const token = localStorage.getItem('token');\n    if (!token) {\n      navigate('/login');\n      return;\n    }\n\n    startExam();\n  }, [examId, navigate]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  useEffect(() => {\n    if (timeLeft > 0) {\n      const timer = setTimeout(() => setTimeLeft(timeLeft - 1), 1000);\n      return () => clearTimeout(timer);\n    } else if (timeLeft === 0 && exam) {\n      handleSubmit();\n    }\n  }, [timeLeft, exam]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  const startExam = async () => {\n    try {\n      console.log('Starting exam with ID:', examId);\n      const response = await api.post(`/exams/${examId}/start`);\n      console.log('Start exam response:', response.data);\n\n      setExam(response.data.exam);\n      setAttemptId(response.data.attempt);\n      setTimeLeft(response.data.exam.duration * 60); // Convert minutes to seconds\n      setExamStartTime(Date.now());\n      setQuestionStartTime(Date.now());\n\n      // Initialize answers\n      const initialAnswers = {};\n      response.data.exam.questions.forEach(question => {\n        initialAnswers[question._id] = {\n          question: question._id,\n          selectedOptions: [],\n          answer: ''\n        };\n      });\n      setAnswers(initialAnswers);\n\n      // Initialize question times\n      const initialTimes = {};\n      response.data.exam.questions.forEach(question => {\n        initialTimes[question._id] = 0;\n      });\n      setQuestionTimes(initialTimes);\n\n      console.log('Exam started successfully');\n    } catch (error) {\n      console.error('Error starting exam:', error);\n      setError(error.response?.data?.message || 'Failed to start exam');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleAnswerChange = (questionId, value, isMultiple = false) => {\n    setAnswers(prev => ({\n      ...prev,\n      [questionId]: {\n        ...prev[questionId],\n        selectedOptions: isMultiple \n          ? (prev[questionId].selectedOptions.includes(value)\n              ? prev[questionId].selectedOptions.filter(opt => opt !== value)\n              : [...prev[questionId].selectedOptions, value])\n          : [value]\n      }\n    }));\n  };\n\n  const handleSubmit = async () => {\n    if (submitting) return;\n    \n    setSubmitting(true);\n    try {\n      const answersArray = Object.values(answers);\n      await api.post(`/exam-attempts/${attemptId}/submit`, { answers: answersArray });\n      navigate(`/results/${attemptId}`);\n    } catch (error) {\n      setError('Failed to submit exam');\n      setSubmitting(false);\n    }\n  };\n\n  const formatTime = (seconds) => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins}:${secs.toString().padStart(2, '0')}`;\n  };\n\n  const trackQuestionTime = (questionId) => {\n    if (questionStartTime && questionId) {\n      const timeSpent = Math.floor((Date.now() - questionStartTime) / 1000);\n      setQuestionTimes(prev => ({\n        ...prev,\n        [questionId]: (prev[questionId] || 0) + timeSpent\n      }));\n    }\n  };\n\n  const navigateToQuestion = (questionIndex) => {\n    if (exam && exam.questions[currentQuestion]) {\n      trackQuestionTime(exam.questions[currentQuestion]._id);\n    }\n    setCurrentQuestion(questionIndex);\n    setQuestionStartTime(Date.now());\n  };\n\n  const toggleFullscreen = () => {\n    if (!isFullscreen) {\n      document.documentElement.requestFullscreen?.();\n      setIsFullscreen(true);\n    } else {\n      document.exitFullscreen?.();\n      setIsFullscreen(false);\n    }\n  };\n\n  const handleTabSwitch = () => {\n    setWarningCount(prev => prev + 1);\n    if (warningCount >= 2) {\n      alert('Warning: Multiple tab switches detected. This exam will be auto-submitted for security.');\n      handleSubmit();\n    } else {\n      alert(`Warning ${warningCount + 1}/3: Please stay on this tab during the exam.`);\n    }\n  };\n\n  // Add event listeners for tab switching detection\n  useEffect(() => {\n    const handleVisibilityChange = () => {\n      if (document.hidden && exam && !submitting) {\n        handleTabSwitch();\n      }\n    };\n\n    document.addEventListener('visibilitychange', handleVisibilityChange);\n    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);\n  }, [exam, submitting, warningCount]);\n\n  const startExamProper = () => {\n    setShowInstructions(false);\n    setQuestionStartTime(Date.now());\n  };\n\n  if (loading) return <div className=\"loading\">Starting exam...</div>;\n  if (error) return <div className=\"error\">{error}</div>;\n  if (!exam) return <div className=\"error\">Exam not found</div>;\n\n  const question = exam.questions[currentQuestion];\n\n  return (\n    <div className=\"container\">\n      {/* Timer */}\n      <div className=\"timer\">\n        Time Left: {formatTime(timeLeft)}\n      </div>\n\n      {/* Exam Header */}\n      <div className=\"exam-header\">\n        <h2>{exam.title}</h2>\n        <p><strong>Subject:</strong> {exam.subject?.name}</p>\n        <p>Question {currentQuestion + 1} of {exam.questions.length}</p>\n      </div>\n\n      {/* Question Navigation */}\n      <div className=\"question-nav\">\n        <p><strong>Questions:</strong></p>\n        {exam.questions.map((_, index) => (\n          <button\n            key={index}\n            className={`nav-btn ${index === currentQuestion ? 'active' : ''} ${\n              answers[exam.questions[index]._id]?.selectedOptions.length > 0 ? 'answered' : ''\n            }`}\n            onClick={() => setCurrentQuestion(index)}\n          >\n            {index + 1}\n          </button>\n        ))}\n      </div>\n\n      {/* Question */}\n      <div className=\"question-container\">\n        <div className=\"question-header\">\n          <h3>Question {currentQuestion + 1}</h3>\n          <span className=\"question-type\">\n            {question.type === 'MCQ' ? 'Multiple Choice' :\n             question.type === 'TRUE_FALSE' ? 'True/False' :\n             'Multiple Answer'}\n          </span>\n        </div>\n\n        <div className=\"question-text\">{question.text}</div>\n\n        <div className=\"options\">\n          {question.options.map((option, index) => {\n            const isSelected = answers[question._id]?.selectedOptions.includes(option.text);\n            const optionLetter = String.fromCharCode(65 + index);\n\n            return (\n              <div key={index} className={`option ${isSelected ? 'selected' : ''}`}>\n                <label>\n                  <input\n                    type={question.type === 'MULTI_ANSWER' ? 'checkbox' : 'radio'}\n                    name={`question_${question._id}`}\n                    value={option.text}\n                    checked={isSelected}\n                    onChange={(e) => handleAnswerChange(\n                      question._id,\n                      option.text,\n                      question.type === 'MULTI_ANSWER'\n                    )}\n                  />\n                  <span className=\"option-letter\">{optionLetter}.</span>\n                  <span className=\"option-text\">{option.text}</span>\n                </label>\n              </div>\n            );\n          })}\n        </div>\n\n        {/* Answer Status */}\n        <div className=\"answer-status\">\n          {answers[question._id]?.selectedOptions.length > 0 ? (\n            <span className=\"answered\">✓ Answered</span>\n          ) : (\n            <span className=\"unanswered\">⚠ Not answered</span>\n          )}\n        </div>\n      </div>\n\n      {/* Navigation */}\n      <div className=\"navigation\">\n        <button\n          className=\"btn btn-secondary\"\n          onClick={() => setCurrentQuestion(Math.max(0, currentQuestion - 1))}\n          disabled={currentQuestion === 0}\n        >\n          ← Previous\n        </button>\n\n        {currentQuestion < exam.questions.length - 1 ? (\n          <button\n            className=\"btn\"\n            onClick={() => setCurrentQuestion(currentQuestion + 1)}\n          >\n            Next →\n          </button>\n        ) : (\n          <button\n            className=\"btn btn-success\"\n            onClick={handleSubmit}\n            disabled={submitting}\n          >\n            {submitting ? 'Submitting...' : 'Submit Exam'}\n          </button>\n        )}\n      </div>\n\n      {/* Exam Summary */}\n      <div className=\"exam-summary\">\n        <h4>Exam Progress</h4>\n        <p>\n          Answered: {Object.values(answers).filter(a => a.selectedOptions.length > 0).length} / {exam.questions.length}\n        </p>\n        <div className=\"progress-bar\">\n          <div\n            className=\"progress\"\n            style={{\n              width: `${(Object.values(answers).filter(a => a.selectedOptions.length > 0).length / exam.questions.length) * 100}%`\n            }}\n          ></div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ExamTaking;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,OAAOC,GAAG,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,aAAA,EAAAC,sBAAA;EACvB,MAAM;IAAEC;EAAO,CAAC,GAAGT,SAAS,CAAC,CAAC;EAC9B,MAAMU,QAAQ,GAAGT,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACU,IAAI,EAAEC,OAAO,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACe,SAAS,EAAEC,YAAY,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1C,MAAM,CAACmB,eAAe,EAAEC,kBAAkB,CAAC,GAAGpB,QAAQ,CAAC,CAAC,CAAC;EACzD,MAAM,CAACqB,QAAQ,EAAEC,WAAW,CAAC,GAAGtB,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAACuB,OAAO,EAAEC,UAAU,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACyB,KAAK,EAAEC,QAAQ,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC2B,UAAU,EAAEC,aAAa,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC6B,aAAa,EAAEC,gBAAgB,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAAC+B,aAAa,EAAEC,gBAAgB,CAAC,GAAGhC,QAAQ,CAAC,CAAC,CAAC,CAAC;EACtD,MAAM,CAACiC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACmC,YAAY,EAAEC,eAAe,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACqC,YAAY,EAAEC,eAAe,CAAC,GAAGtC,QAAQ,CAAC,CAAC,CAAC;EACnD,MAAM,CAACuC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGxC,QAAQ,CAAC,IAAI,CAAC;EAEhEC,SAAS,CAAC,MAAM;IACd,MAAMwC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAI,CAACF,KAAK,EAAE;MACV7B,QAAQ,CAAC,QAAQ,CAAC;MAClB;IACF;IAEAgC,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAACjC,MAAM,EAAEC,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAExBX,SAAS,CAAC,MAAM;IACd,IAAIoB,QAAQ,GAAG,CAAC,EAAE;MAChB,MAAMwB,KAAK,GAAGC,UAAU,CAAC,MAAMxB,WAAW,CAACD,QAAQ,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC;MAC/D,OAAO,MAAM0B,YAAY,CAACF,KAAK,CAAC;IAClC,CAAC,MAAM,IAAIxB,QAAQ,KAAK,CAAC,IAAIR,IAAI,EAAE;MACjCmC,YAAY,CAAC,CAAC;IAChB;EACF,CAAC,EAAE,CAAC3B,QAAQ,EAAER,IAAI,CAAC,CAAC,CAAC,CAAC;;EAEtB,MAAM+B,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI;MACFK,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEvC,MAAM,CAAC;MAC7C,MAAMwC,QAAQ,GAAG,MAAM/C,GAAG,CAACgD,IAAI,CAAC,UAAUzC,MAAM,QAAQ,CAAC;MACzDsC,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEC,QAAQ,CAACE,IAAI,CAAC;MAElDvC,OAAO,CAACqC,QAAQ,CAACE,IAAI,CAACxC,IAAI,CAAC;MAC3BG,YAAY,CAACmC,QAAQ,CAACE,IAAI,CAACC,OAAO,CAAC;MACnChC,WAAW,CAAC6B,QAAQ,CAACE,IAAI,CAACxC,IAAI,CAAC0C,QAAQ,GAAG,EAAE,CAAC,CAAC,CAAC;MAC/CzB,gBAAgB,CAAC0B,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;MAC5BjB,oBAAoB,CAACgB,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;;MAEhC;MACA,MAAMC,cAAc,GAAG,CAAC,CAAC;MACzBP,QAAQ,CAACE,IAAI,CAACxC,IAAI,CAAC8C,SAAS,CAACC,OAAO,CAACC,QAAQ,IAAI;QAC/CH,cAAc,CAACG,QAAQ,CAACC,GAAG,CAAC,GAAG;UAC7BD,QAAQ,EAAEA,QAAQ,CAACC,GAAG;UACtBC,eAAe,EAAE,EAAE;UACnBC,MAAM,EAAE;QACV,CAAC;MACH,CAAC,CAAC;MACF9C,UAAU,CAACwC,cAAc,CAAC;;MAE1B;MACA,MAAMO,YAAY,GAAG,CAAC,CAAC;MACvBd,QAAQ,CAACE,IAAI,CAACxC,IAAI,CAAC8C,SAAS,CAACC,OAAO,CAACC,QAAQ,IAAI;QAC/CI,YAAY,CAACJ,QAAQ,CAACC,GAAG,CAAC,GAAG,CAAC;MAChC,CAAC,CAAC;MACF9B,gBAAgB,CAACiC,YAAY,CAAC;MAE9BhB,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;IAC1C,CAAC,CAAC,OAAOzB,KAAK,EAAE;MAAA,IAAAyC,eAAA,EAAAC,oBAAA;MACdlB,OAAO,CAACxB,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5CC,QAAQ,CAAC,EAAAwC,eAAA,GAAAzC,KAAK,CAAC0B,QAAQ,cAAAe,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBb,IAAI,cAAAc,oBAAA,uBAApBA,oBAAA,CAAsBC,OAAO,KAAI,sBAAsB,CAAC;IACnE,CAAC,SAAS;MACR5C,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM6C,kBAAkB,GAAGA,CAACC,UAAU,EAAEC,KAAK,EAAEC,UAAU,GAAG,KAAK,KAAK;IACpEtD,UAAU,CAACuD,IAAI,KAAK;MAClB,GAAGA,IAAI;MACP,CAACH,UAAU,GAAG;QACZ,GAAGG,IAAI,CAACH,UAAU,CAAC;QACnBP,eAAe,EAAES,UAAU,GACtBC,IAAI,CAACH,UAAU,CAAC,CAACP,eAAe,CAACW,QAAQ,CAACH,KAAK,CAAC,GAC7CE,IAAI,CAACH,UAAU,CAAC,CAACP,eAAe,CAACY,MAAM,CAACC,GAAG,IAAIA,GAAG,KAAKL,KAAK,CAAC,GAC7D,CAAC,GAAGE,IAAI,CAACH,UAAU,CAAC,CAACP,eAAe,EAAEQ,KAAK,CAAC,GAChD,CAACA,KAAK;MACZ;IACF,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMvB,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAIrB,UAAU,EAAE;IAEhBC,aAAa,CAAC,IAAI,CAAC;IACnB,IAAI;MACF,MAAMiD,YAAY,GAAGC,MAAM,CAACC,MAAM,CAAC9D,OAAO,CAAC;MAC3C,MAAMb,GAAG,CAACgD,IAAI,CAAC,kBAAkBrC,SAAS,SAAS,EAAE;QAAEE,OAAO,EAAE4D;MAAa,CAAC,CAAC;MAC/EjE,QAAQ,CAAC,YAAYG,SAAS,EAAE,CAAC;IACnC,CAAC,CAAC,OAAOU,KAAK,EAAE;MACdC,QAAQ,CAAC,uBAAuB,CAAC;MACjCE,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAMoD,UAAU,GAAIC,OAAO,IAAK;IAC9B,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;IACrC,MAAMI,IAAI,GAAGJ,OAAO,GAAG,EAAE;IACzB,OAAO,GAAGC,IAAI,IAAIG,IAAI,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EACtD,CAAC;EAED,MAAMC,iBAAiB,GAAIlB,UAAU,IAAK;IACxC,IAAI/B,iBAAiB,IAAI+B,UAAU,EAAE;MACnC,MAAMmB,SAAS,GAAGN,IAAI,CAACC,KAAK,CAAC,CAAC5B,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGlB,iBAAiB,IAAI,IAAI,CAAC;MACrEP,gBAAgB,CAACyC,IAAI,KAAK;QACxB,GAAGA,IAAI;QACP,CAACH,UAAU,GAAG,CAACG,IAAI,CAACH,UAAU,CAAC,IAAI,CAAC,IAAImB;MAC1C,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAMC,kBAAkB,GAAIC,aAAa,IAAK;IAC5C,IAAI9E,IAAI,IAAIA,IAAI,CAAC8C,SAAS,CAACxC,eAAe,CAAC,EAAE;MAC3CqE,iBAAiB,CAAC3E,IAAI,CAAC8C,SAAS,CAACxC,eAAe,CAAC,CAAC2C,GAAG,CAAC;IACxD;IACA1C,kBAAkB,CAACuE,aAAa,CAAC;IACjCnD,oBAAoB,CAACgB,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;EAClC,CAAC;EAED,MAAMmC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI,CAACzD,YAAY,EAAE;MAAA,IAAA0D,qBAAA,EAAAC,sBAAA;MACjB,CAAAD,qBAAA,IAAAC,sBAAA,GAAAC,QAAQ,CAACC,eAAe,EAACC,iBAAiB,cAAAJ,qBAAA,uBAA1CA,qBAAA,CAAAK,IAAA,CAAAJ,sBAA6C,CAAC;MAC9C1D,eAAe,CAAC,IAAI,CAAC;IACvB,CAAC,MAAM;MAAA,IAAA+D,qBAAA,EAAAC,SAAA;MACL,CAAAD,qBAAA,IAAAC,SAAA,GAAAL,QAAQ,EAACM,cAAc,cAAAF,qBAAA,uBAAvBA,qBAAA,CAAAD,IAAA,CAAAE,SAA0B,CAAC;MAC3BhE,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,MAAMkE,eAAe,GAAGA,CAAA,KAAM;IAC5BhE,eAAe,CAACmC,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IACjC,IAAIpC,YAAY,IAAI,CAAC,EAAE;MACrBkE,KAAK,CAAC,yFAAyF,CAAC;MAChGvD,YAAY,CAAC,CAAC;IAChB,CAAC,MAAM;MACLuD,KAAK,CAAC,WAAWlE,YAAY,GAAG,CAAC,8CAA8C,CAAC;IAClF;EACF,CAAC;;EAED;EACApC,SAAS,CAAC,MAAM;IACd,MAAMuG,sBAAsB,GAAGA,CAAA,KAAM;MACnC,IAAIT,QAAQ,CAACU,MAAM,IAAI5F,IAAI,IAAI,CAACc,UAAU,EAAE;QAC1C2E,eAAe,CAAC,CAAC;MACnB;IACF,CAAC;IAEDP,QAAQ,CAACW,gBAAgB,CAAC,kBAAkB,EAAEF,sBAAsB,CAAC;IACrE,OAAO,MAAMT,QAAQ,CAACY,mBAAmB,CAAC,kBAAkB,EAAEH,sBAAsB,CAAC;EACvF,CAAC,EAAE,CAAC3F,IAAI,EAAEc,UAAU,EAAEU,YAAY,CAAC,CAAC;EAEpC,MAAMuE,eAAe,GAAGA,CAAA,KAAM;IAC5B1E,mBAAmB,CAAC,KAAK,CAAC;IAC1BM,oBAAoB,CAACgB,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;EAClC,CAAC;EAED,IAAIlC,OAAO,EAAE,oBAAOjB,OAAA;IAAKuG,SAAS,EAAC,SAAS;IAAAC,QAAA,EAAC;EAAgB;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;EACnE,IAAIzF,KAAK,EAAE,oBAAOnB,OAAA;IAAKuG,SAAS,EAAC,OAAO;IAAAC,QAAA,EAAErF;EAAK;IAAAsF,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EACtD,IAAI,CAACrG,IAAI,EAAE,oBAAOP,OAAA;IAAKuG,SAAS,EAAC,OAAO;IAAAC,QAAA,EAAC;EAAc;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;EAE7D,MAAMrD,QAAQ,GAAGhD,IAAI,CAAC8C,SAAS,CAACxC,eAAe,CAAC;EAEhD,oBACEb,OAAA;IAAKuG,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExBxG,OAAA;MAAKuG,SAAS,EAAC,OAAO;MAAAC,QAAA,GAAC,aACV,EAAC9B,UAAU,CAAC3D,QAAQ,CAAC;IAAA;MAAA0F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7B,CAAC,eAGN5G,OAAA;MAAKuG,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BxG,OAAA;QAAAwG,QAAA,EAAKjG,IAAI,CAACsG;MAAK;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACrB5G,OAAA;QAAAwG,QAAA,gBAAGxG,OAAA;UAAAwG,QAAA,EAAQ;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,KAAC,GAAAzG,aAAA,GAACI,IAAI,CAACuG,OAAO,cAAA3G,aAAA,uBAAZA,aAAA,CAAc4G,IAAI;MAAA;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACrD5G,OAAA;QAAAwG,QAAA,GAAG,WAAS,EAAC3F,eAAe,GAAG,CAAC,EAAC,MAAI,EAACN,IAAI,CAAC8C,SAAS,CAAC2D,MAAM;MAAA;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7D,CAAC,eAGN5G,OAAA;MAAKuG,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC3BxG,OAAA;QAAAwG,QAAA,eAAGxG,OAAA;UAAAwG,QAAA,EAAQ;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,EACjCrG,IAAI,CAAC8C,SAAS,CAAC4D,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK;QAAA,IAAAC,qBAAA;QAAA,oBAC3BpH,OAAA;UAEEuG,SAAS,EAAE,WAAWY,KAAK,KAAKtG,eAAe,GAAG,QAAQ,GAAG,EAAE,IAC7D,EAAAuG,qBAAA,GAAAzG,OAAO,CAACJ,IAAI,CAAC8C,SAAS,CAAC8D,KAAK,CAAC,CAAC3D,GAAG,CAAC,cAAA4D,qBAAA,uBAAlCA,qBAAA,CAAoC3D,eAAe,CAACuD,MAAM,IAAG,CAAC,GAAG,UAAU,GAAG,EAAE,EAC/E;UACHK,OAAO,EAAEA,CAAA,KAAMvG,kBAAkB,CAACqG,KAAK,CAAE;UAAAX,QAAA,EAExCW,KAAK,GAAG;QAAC,GANLA,KAAK;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAOJ,CAAC;MAAA,CACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGN5G,OAAA;MAAKuG,SAAS,EAAC,oBAAoB;MAAAC,QAAA,gBACjCxG,OAAA;QAAKuG,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BxG,OAAA;UAAAwG,QAAA,GAAI,WAAS,EAAC3F,eAAe,GAAG,CAAC;QAAA;UAAA4F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACvC5G,OAAA;UAAMuG,SAAS,EAAC,eAAe;UAAAC,QAAA,EAC5BjD,QAAQ,CAAC+D,IAAI,KAAK,KAAK,GAAG,iBAAiB,GAC3C/D,QAAQ,CAAC+D,IAAI,KAAK,YAAY,GAAG,YAAY,GAC7C;QAAiB;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAEN5G,OAAA;QAAKuG,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAEjD,QAAQ,CAACgE;MAAI;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAEpD5G,OAAA;QAAKuG,SAAS,EAAC,SAAS;QAAAC,QAAA,EACrBjD,QAAQ,CAACiE,OAAO,CAACP,GAAG,CAAC,CAACQ,MAAM,EAAEN,KAAK,KAAK;UAAA,IAAAO,qBAAA;UACvC,MAAMC,UAAU,IAAAD,qBAAA,GAAG/G,OAAO,CAAC4C,QAAQ,CAACC,GAAG,CAAC,cAAAkE,qBAAA,uBAArBA,qBAAA,CAAuBjE,eAAe,CAACW,QAAQ,CAACqD,MAAM,CAACF,IAAI,CAAC;UAC/E,MAAMK,YAAY,GAAGC,MAAM,CAACC,YAAY,CAAC,EAAE,GAAGX,KAAK,CAAC;UAEpD,oBACEnH,OAAA;YAAiBuG,SAAS,EAAE,UAAUoB,UAAU,GAAG,UAAU,GAAG,EAAE,EAAG;YAAAnB,QAAA,eACnExG,OAAA;cAAAwG,QAAA,gBACExG,OAAA;gBACEsH,IAAI,EAAE/D,QAAQ,CAAC+D,IAAI,KAAK,cAAc,GAAG,UAAU,GAAG,OAAQ;gBAC9DP,IAAI,EAAE,YAAYxD,QAAQ,CAACC,GAAG,EAAG;gBACjCS,KAAK,EAAEwD,MAAM,CAACF,IAAK;gBACnBQ,OAAO,EAAEJ,UAAW;gBACpBK,QAAQ,EAAGC,CAAC,IAAKlE,kBAAkB,CACjCR,QAAQ,CAACC,GAAG,EACZiE,MAAM,CAACF,IAAI,EACXhE,QAAQ,CAAC+D,IAAI,KAAK,cACpB;cAAE;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACF5G,OAAA;gBAAMuG,SAAS,EAAC,eAAe;gBAAAC,QAAA,GAAEoB,YAAY,EAAC,GAAC;cAAA;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACtD5G,OAAA;gBAAMuG,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAEiB,MAAM,CAACF;cAAI;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C;UAAC,GAfAO,KAAK;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgBV,CAAC;QAEV,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGN5G,OAAA;QAAKuG,SAAS,EAAC,eAAe;QAAAC,QAAA,EAC3B,EAAApG,sBAAA,GAAAO,OAAO,CAAC4C,QAAQ,CAACC,GAAG,CAAC,cAAApD,sBAAA,uBAArBA,sBAAA,CAAuBqD,eAAe,CAACuD,MAAM,IAAG,CAAC,gBAChDhH,OAAA;UAAMuG,SAAS,EAAC,UAAU;UAAAC,QAAA,EAAC;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,gBAE5C5G,OAAA;UAAMuG,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAClD;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN5G,OAAA;MAAKuG,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACzBxG,OAAA;QACEuG,SAAS,EAAC,mBAAmB;QAC7Bc,OAAO,EAAEA,CAAA,KAAMvG,kBAAkB,CAAC+D,IAAI,CAACqD,GAAG,CAAC,CAAC,EAAErH,eAAe,GAAG,CAAC,CAAC,CAAE;QACpEsH,QAAQ,EAAEtH,eAAe,KAAK,CAAE;QAAA2F,QAAA,EACjC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,EAER/F,eAAe,GAAGN,IAAI,CAAC8C,SAAS,CAAC2D,MAAM,GAAG,CAAC,gBAC1ChH,OAAA;QACEuG,SAAS,EAAC,KAAK;QACfc,OAAO,EAAEA,CAAA,KAAMvG,kBAAkB,CAACD,eAAe,GAAG,CAAC,CAAE;QAAA2F,QAAA,EACxD;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,gBAET5G,OAAA;QACEuG,SAAS,EAAC,iBAAiB;QAC3Bc,OAAO,EAAE3E,YAAa;QACtByF,QAAQ,EAAE9G,UAAW;QAAAmF,QAAA,EAEpBnF,UAAU,GAAG,eAAe,GAAG;MAAa;QAAAoF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGN5G,OAAA;MAAKuG,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC3BxG,OAAA;QAAAwG,QAAA,EAAI;MAAa;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACtB5G,OAAA;QAAAwG,QAAA,GAAG,YACS,EAAChC,MAAM,CAACC,MAAM,CAAC9D,OAAO,CAAC,CAAC0D,MAAM,CAAC+D,CAAC,IAAIA,CAAC,CAAC3E,eAAe,CAACuD,MAAM,GAAG,CAAC,CAAC,CAACA,MAAM,EAAC,KAAG,EAACzG,IAAI,CAAC8C,SAAS,CAAC2D,MAAM;MAAA;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3G,CAAC,eACJ5G,OAAA;QAAKuG,SAAS,EAAC,cAAc;QAAAC,QAAA,eAC3BxG,OAAA;UACEuG,SAAS,EAAC,UAAU;UACpB8B,KAAK,EAAE;YACLC,KAAK,EAAE,GAAI9D,MAAM,CAACC,MAAM,CAAC9D,OAAO,CAAC,CAAC0D,MAAM,CAAC+D,CAAC,IAAIA,CAAC,CAAC3E,eAAe,CAACuD,MAAM,GAAG,CAAC,CAAC,CAACA,MAAM,GAAGzG,IAAI,CAAC8C,SAAS,CAAC2D,MAAM,GAAI,GAAG;UACnH;QAAE;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC1G,EAAA,CAxSID,UAAU;EAAA,QACKL,SAAS,EACXC,WAAW;AAAA;AAAA0I,EAAA,GAFxBtI,UAAU;AA0ShB,eAAeA,UAAU;AAAC,IAAAsI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}