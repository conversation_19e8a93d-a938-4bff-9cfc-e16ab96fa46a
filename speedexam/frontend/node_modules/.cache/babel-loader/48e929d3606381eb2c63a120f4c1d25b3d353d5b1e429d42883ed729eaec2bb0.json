{"ast": null, "code": "import axios from 'axios';\n\n// Safari-compatible API configuration\nconst getApiBaseUrl = () => {\n  // Check if we're on Safari and having localhost issues\n  const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);\n  const networkUrl = 'http://*************:5000/api';\n  const localUrl = 'http://localhost:5000/api';\n\n  // Use environment variable first, then network IP for Safari, then localhost\n  return process.env.REACT_APP_API_URL || (isSafari ? networkUrl : localUrl) || localUrl;\n};\nconst api = axios.create({\n  baseURL: getApiBaseUrl()\n});\n\n// Add auth token to requests\napi.interceptors.request.use(config => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n});\nexport default api;", "map": {"version": 3, "names": ["axios", "getApiBaseUrl", "<PERSON><PERSON><PERSON><PERSON>", "test", "navigator", "userAgent", "networkUrl", "localUrl", "process", "env", "REACT_APP_API_URL", "api", "create", "baseURL", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "headers", "Authorization"], "sources": ["/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/services/api.js"], "sourcesContent": ["import axios from 'axios';\n\n// Safari-compatible API configuration\nconst getApiBaseUrl = () => {\n  // Check if we're on Safari and having localhost issues\n  const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);\n  const networkUrl = 'http://*************:5000/api';\n  const localUrl = 'http://localhost:5000/api';\n\n  // Use environment variable first, then network IP for Safari, then localhost\n  return process.env.REACT_APP_API_URL ||\n         (isSafari ? networkUrl : localUrl) ||\n         localUrl;\n};\n\nconst api = axios.create({\n  baseURL: getApiBaseUrl()\n});\n\n// Add auth token to requests\napi.interceptors.request.use(config => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n});\n\nexport default api;"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;;AAEzB;AACA,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAC1B;EACA,MAAMC,QAAQ,GAAG,gCAAgC,CAACC,IAAI,CAACC,SAAS,CAACC,SAAS,CAAC;EAC3E,MAAMC,UAAU,GAAG,+BAA+B;EAClD,MAAMC,QAAQ,GAAG,2BAA2B;;EAE5C;EACA,OAAOC,OAAO,CAACC,GAAG,CAACC,iBAAiB,KAC5BR,QAAQ,GAAGI,UAAU,GAAGC,QAAQ,CAAC,IAClCA,QAAQ;AACjB,CAAC;AAED,MAAMI,GAAG,GAAGX,KAAK,CAACY,MAAM,CAAC;EACvBC,OAAO,EAAEZ,aAAa,CAAC;AACzB,CAAC,CAAC;;AAEF;AACAU,GAAG,CAACG,YAAY,CAACC,OAAO,CAACC,GAAG,CAACC,MAAM,IAAI;EACrC,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACI,OAAO,CAACC,aAAa,GAAG,UAAUJ,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,CAAC;AAEF,eAAeN,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}