{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/AdminLogin.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useNavigate, Link } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminLogin = () => {\n  _s();\n  const [formData, setFormData] = useState({\n    email: '',\n    password: ''\n  });\n  const [error, setError] = useState('');\n  const [loading, setLoading] = useState(false);\n  const navigate = useNavigate();\n  const handleChange = e => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n    try {\n      // Mock admin login - in real app, this would call admin login API\n      if (formData.email === '<EMAIL>' && formData.password === 'admin123') {\n        localStorage.setItem('adminToken', 'mock_admin_token_' + Date.now());\n        localStorage.setItem('userRole', 'admin');\n        navigate('/admin');\n      } else {\n        setError('Invalid admin credentials');\n      }\n    } catch (error) {\n      setError('Login failed');\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"admin-login-container\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"admin-login-card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"admin-login-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"\\uD83D\\uDD10 Admin Login\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"SpeedExam Administration Panel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 9\n      }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 49,\n        columnNumber: 19\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        className: \"admin-login-form\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Admin Email:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"email\",\n            name: \"email\",\n            value: formData.email,\n            onChange: handleChange,\n            placeholder: \"<EMAIL>\",\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 54,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Password:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"password\",\n            name: \"password\",\n            value: formData.password,\n            onChange: handleChange,\n            placeholder: \"Enter admin password\",\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"btn btn-admin\",\n          disabled: loading,\n          children: loading ? 'Logging in...' : 'Login as Admin'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"admin-demo-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          children: \"\\uD83C\\uDFAF Demo Admin Credentials:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"demo-credentials\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Email:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 84,\n              columnNumber: 16\n            }, this), \" <EMAIL>\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Password:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 16\n            }, this), \" admin123\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"login-links\",\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          to: \"/login\",\n          className: \"link\",\n          children: \"Student Login\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/\",\n          className: \"link\",\n          children: \"Back to Home\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 42,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminLogin, \"xgbvgKFAJw5hexSliSarJCeDaxw=\", false, function () {\n  return [useNavigate];\n});\n_c = AdminLogin;\nexport default AdminLogin;\nvar _c;\n$RefreshReg$(_c, \"AdminLogin\");", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "Link", "jsxDEV", "_jsxDEV", "AdminLogin", "_s", "formData", "setFormData", "email", "password", "error", "setError", "loading", "setLoading", "navigate", "handleChange", "e", "target", "name", "value", "handleSubmit", "preventDefault", "localStorage", "setItem", "Date", "now", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "type", "onChange", "placeholder", "required", "disabled", "to", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/AdminLogin.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useNavigate, Link } from 'react-router-dom';\n\nconst AdminLogin = () => {\n  const [formData, setFormData] = useState({\n    email: '',\n    password: ''\n  });\n  const [error, setError] = useState('');\n  const [loading, setLoading] = useState(false);\n  const navigate = useNavigate();\n\n  const handleChange = (e) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n\n    try {\n      // Mock admin login - in real app, this would call admin login API\n      if (formData.email === '<EMAIL>' && formData.password === 'admin123') {\n        localStorage.setItem('adminToken', 'mock_admin_token_' + Date.now());\n        localStorage.setItem('userRole', 'admin');\n        navigate('/admin');\n      } else {\n        setError('Invalid admin credentials');\n      }\n    } catch (error) {\n      setError('Login failed');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"admin-login-container\">\n      <div className=\"admin-login-card\">\n        <div className=\"admin-login-header\">\n          <h2>🔐 Admin Login</h2>\n          <p>SpeedExam Administration Panel</p>\n        </div>\n        \n        {error && <div className=\"error\">{error}</div>}\n        \n        <form onSubmit={handleSubmit} className=\"admin-login-form\">\n          <div className=\"form-group\">\n            <label>Admin Email:</label>\n            <input\n              type=\"email\"\n              name=\"email\"\n              value={formData.email}\n              onChange={handleChange}\n              placeholder=\"<EMAIL>\"\n              required\n            />\n          </div>\n          \n          <div className=\"form-group\">\n            <label>Password:</label>\n            <input\n              type=\"password\"\n              name=\"password\"\n              value={formData.password}\n              onChange={handleChange}\n              placeholder=\"Enter admin password\"\n              required\n            />\n          </div>\n          \n          <button type=\"submit\" className=\"btn btn-admin\" disabled={loading}>\n            {loading ? 'Logging in...' : 'Login as Admin'}\n          </button>\n        </form>\n        \n        <div className=\"admin-demo-info\">\n          <h4>🎯 Demo Admin Credentials:</h4>\n          <div className=\"demo-credentials\">\n            <p><strong>Email:</strong> <EMAIL></p>\n            <p><strong>Password:</strong> admin123</p>\n          </div>\n        </div>\n        \n        <div className=\"login-links\">\n          <Link to=\"/login\" className=\"link\">Student Login</Link>\n          <Link to=\"/\" className=\"link\">Back to Home</Link>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default AdminLogin;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,EAAEC,IAAI,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGR,QAAQ,CAAC;IACvCS,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACa,OAAO,EAAEC,UAAU,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAMe,QAAQ,GAAGd,WAAW,CAAC,CAAC;EAE9B,MAAMe,YAAY,GAAIC,CAAC,IAAK;IAC1BT,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACU,CAAC,CAACC,MAAM,CAACC,IAAI,GAAGF,CAAC,CAACC,MAAM,CAACE;IAC5B,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,YAAY,GAAG,MAAOJ,CAAC,IAAK;IAChCA,CAAC,CAACK,cAAc,CAAC,CAAC;IAClBR,UAAU,CAAC,IAAI,CAAC;IAChBF,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF;MACA,IAAIL,QAAQ,CAACE,KAAK,KAAK,qBAAqB,IAAIF,QAAQ,CAACG,QAAQ,KAAK,UAAU,EAAE;QAChFa,YAAY,CAACC,OAAO,CAAC,YAAY,EAAE,mBAAmB,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;QACpEH,YAAY,CAACC,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC;QACzCT,QAAQ,CAAC,QAAQ,CAAC;MACpB,CAAC,MAAM;QACLH,QAAQ,CAAC,2BAA2B,CAAC;MACvC;IACF,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdC,QAAQ,CAAC,cAAc,CAAC;IAC1B,CAAC,SAAS;MACRE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACEV,OAAA;IAAKuB,SAAS,EAAC,uBAAuB;IAAAC,QAAA,eACpCxB,OAAA;MAAKuB,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BxB,OAAA;QAAKuB,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACjCxB,OAAA;UAAAwB,QAAA,EAAI;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvB5B,OAAA;UAAAwB,QAAA,EAAG;QAA8B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC,EAELrB,KAAK,iBAAIP,OAAA;QAAKuB,SAAS,EAAC,OAAO;QAAAC,QAAA,EAAEjB;MAAK;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAE9C5B,OAAA;QAAM6B,QAAQ,EAAEZ,YAAa;QAACM,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBACxDxB,OAAA;UAAKuB,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBxB,OAAA;YAAAwB,QAAA,EAAO;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC3B5B,OAAA;YACE8B,IAAI,EAAC,OAAO;YACZf,IAAI,EAAC,OAAO;YACZC,KAAK,EAAEb,QAAQ,CAACE,KAAM;YACtB0B,QAAQ,EAAEnB,YAAa;YACvBoB,WAAW,EAAC,qBAAqB;YACjCC,QAAQ;UAAA;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN5B,OAAA;UAAKuB,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBxB,OAAA;YAAAwB,QAAA,EAAO;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACxB5B,OAAA;YACE8B,IAAI,EAAC,UAAU;YACff,IAAI,EAAC,UAAU;YACfC,KAAK,EAAEb,QAAQ,CAACG,QAAS;YACzByB,QAAQ,EAAEnB,YAAa;YACvBoB,WAAW,EAAC,sBAAsB;YAClCC,QAAQ;UAAA;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN5B,OAAA;UAAQ8B,IAAI,EAAC,QAAQ;UAACP,SAAS,EAAC,eAAe;UAACW,QAAQ,EAAEzB,OAAQ;UAAAe,QAAA,EAC/Df,OAAO,GAAG,eAAe,GAAG;QAAgB;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEP5B,OAAA;QAAKuB,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BxB,OAAA;UAAAwB,QAAA,EAAI;QAA0B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACnC5B,OAAA;UAAKuB,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/BxB,OAAA;YAAAwB,QAAA,gBAAGxB,OAAA;cAAAwB,QAAA,EAAQ;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,wBAAoB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAClD5B,OAAA;YAAAwB,QAAA,gBAAGxB,OAAA;cAAAwB,QAAA,EAAQ;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,aAAS;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN5B,OAAA;QAAKuB,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BxB,OAAA,CAACF,IAAI;UAACqC,EAAE,EAAC,QAAQ;UAACZ,SAAS,EAAC,MAAM;UAAAC,QAAA,EAAC;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvD5B,OAAA,CAACF,IAAI;UAACqC,EAAE,EAAC,GAAG;UAACZ,SAAS,EAAC,MAAM;UAAAC,QAAA,EAAC;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC1B,EAAA,CA5FID,UAAU;EAAA,QAOGJ,WAAW;AAAA;AAAAuC,EAAA,GAPxBnC,UAAU;AA8FhB,eAAeA,UAAU;AAAC,IAAAmC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}