{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/SimpleExamTaking.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport api from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SimpleExamTaking = () => {\n  _s();\n  const {\n    examId\n  } = useParams();\n  const navigate = useNavigate();\n  const [exam, setExam] = useState(null);\n  const [attemptId, setAttemptId] = useState(null);\n  const [answers, setAnswers] = useState({});\n  const [currentQuestion, setCurrentQuestion] = useState(0);\n  const [timeLeft, setTimeLeft] = useState(0);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [submitting, setSubmitting] = useState(false);\n  useEffect(() => {\n    const token = localStorage.getItem('token');\n    if (!token) {\n      navigate('/login');\n      return;\n    }\n    startExam();\n  }, [examId, navigate]);\n  useEffect(() => {\n    if (timeLeft > 0) {\n      const timer = setTimeout(() => setTimeLeft(timeLeft - 1), 1000);\n      return () => clearTimeout(timer);\n    } else if (timeLeft === 0 && exam) {\n      handleSubmit();\n    }\n  }, [timeLeft, exam]);\n  const startExam = async () => {\n    try {\n      console.log('🚀 Starting exam with ID:', examId);\n      const response = await api.post(`/exams/${examId}/start`);\n      console.log('✅ Start exam response:', response.data);\n      setExam(response.data.exam);\n      setAttemptId(response.data.attempt);\n      setTimeLeft(response.data.exam.duration * 60);\n\n      // Initialize answers\n      const initialAnswers = {};\n      response.data.exam.questions.forEach(question => {\n        initialAnswers[question._id] = {\n          question: question._id,\n          selectedOptions: []\n        };\n      });\n      setAnswers(initialAnswers);\n      setLoading(false);\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('❌ Error starting exam:', error);\n      setError(`Failed to start exam: ${((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || error.message}`);\n      setLoading(false);\n    }\n  };\n  const handleAnswerChange = (questionId, value) => {\n    setAnswers(prev => ({\n      ...prev,\n      [questionId]: {\n        question: questionId,\n        selectedOptions: [value]\n      }\n    }));\n  };\n  const handleSubmit = async () => {\n    if (submitting) return;\n    setSubmitting(true);\n    try {\n      console.log('📤 Submitting exam with attemptId:', attemptId);\n      const answersArray = Object.values(answers);\n      console.log('📝 Answers to submit:', answersArray);\n      const response = await api.post(`/exam-attempts/${attemptId}/submit`, {\n        answers: answersArray\n      });\n      console.log('✅ Submit response:', response.data);\n      navigate(`/results/${attemptId}`);\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      console.error('❌ Submit error:', error);\n      setError(`Failed to submit exam: ${((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || error.message}`);\n      setSubmitting(false);\n    }\n  };\n  const formatTime = seconds => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins}:${secs.toString().padStart(2, '0')}`;\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: 'center',\n        padding: '50px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"\\uD83D\\uDD04 Loading Exam...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Please wait while we prepare your exam.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: 'center',\n        padding: '50px',\n        color: 'red'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"\\u274C Error\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => navigate('/exams'),\n        style: {\n          padding: '10px 20px',\n          marginTop: '20px'\n        },\n        children: \"Back to Exams\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 112,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 109,\n      columnNumber: 7\n    }, this);\n  }\n  if (!exam) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: 'center',\n        padding: '50px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"\\u274C Exam Not Found\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => navigate('/exams'),\n        style: {\n          padding: '10px 20px'\n        },\n        children: \"Back to Exams\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 121,\n      columnNumber: 7\n    }, this);\n  }\n  const currentQ = exam.questions[currentQuestion];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      maxWidth: '800px',\n      margin: '0 auto',\n      padding: '20px'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: '#007bff',\n        color: 'white',\n        padding: '20px',\n        borderRadius: '10px',\n        marginBottom: '20px',\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          style: {\n            margin: 0\n          },\n          children: exam.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            margin: '5px 0 0 0'\n          },\n          children: [\"Question \", currentQuestion + 1, \" of \", exam.questions.length]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'right'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '24px',\n            fontWeight: 'bold'\n          },\n          children: [\"\\u23F0 \", formatTime(timeLeft)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '14px'\n          },\n          children: \"Time Remaining\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 135,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: 'white',\n        padding: '30px',\n        borderRadius: '10px',\n        boxShadow: '0 2px 10px rgba(0,0,0,0.1)',\n        marginBottom: '20px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        style: {\n          marginBottom: '20px',\n          fontSize: '20px'\n        },\n        children: currentQ.text\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: '20px'\n        },\n        children: currentQ.options.map((option, index) => {\n          var _answers$currentQ$_id;\n          return /*#__PURE__*/_jsxDEV(\"label\", {\n            style: {\n              display: 'block',\n              padding: '15px',\n              margin: '10px 0',\n              background: '#f8f9fa',\n              border: '2px solid #dee2e6',\n              borderRadius: '8px',\n              cursor: 'pointer',\n              transition: 'all 0.3s'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"radio\",\n              name: `question_${currentQ._id}`,\n              value: option.text,\n              checked: ((_answers$currentQ$_id = answers[currentQ._id]) === null || _answers$currentQ$_id === void 0 ? void 0 : _answers$currentQ$_id.selectedOptions[0]) === option.text,\n              onChange: e => handleAnswerChange(currentQ._id, e.target.value),\n              style: {\n                marginRight: '10px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                fontSize: '16px'\n              },\n              children: option.text\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 15\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 13\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 158,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        background: 'white',\n        padding: '20px',\n        borderRadius: '10px',\n        boxShadow: '0 2px 10px rgba(0,0,0,0.1)'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => setCurrentQuestion(Math.max(0, currentQuestion - 1)),\n        disabled: currentQuestion === 0,\n        style: {\n          padding: '12px 24px',\n          background: currentQuestion === 0 ? '#ccc' : '#6c757d',\n          color: 'white',\n          border: 'none',\n          borderRadius: '5px',\n          cursor: currentQuestion === 0 ? 'not-allowed' : 'pointer'\n        },\n        children: \"\\u2190 Previous\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          gap: '10px'\n        },\n        children: exam.questions.map((_, index) => {\n          var _answers$exam$questio, _answers$exam$questio2;\n          return /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setCurrentQuestion(index),\n            style: {\n              width: '40px',\n              height: '40px',\n              borderRadius: '50%',\n              border: 'none',\n              background: index === currentQuestion ? '#007bff' : ((_answers$exam$questio = answers[exam.questions[index]._id]) === null || _answers$exam$questio === void 0 ? void 0 : _answers$exam$questio.selectedOptions.length) > 0 ? '#28a745' : '#dee2e6',\n              color: index === currentQuestion || ((_answers$exam$questio2 = answers[exam.questions[index]._id]) === null || _answers$exam$questio2 === void 0 ? void 0 : _answers$exam$questio2.selectedOptions.length) > 0 ? 'white' : '#6c757d',\n              cursor: 'pointer',\n              fontWeight: 'bold'\n            },\n            children: index + 1\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 13\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 220,\n        columnNumber: 9\n      }, this), currentQuestion < exam.questions.length - 1 ? /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => setCurrentQuestion(currentQuestion + 1),\n        style: {\n          padding: '12px 24px',\n          background: '#007bff',\n          color: 'white',\n          border: 'none',\n          borderRadius: '5px',\n          cursor: 'pointer'\n        },\n        children: \"Next \\u2192\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 243,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleSubmit,\n        disabled: submitting,\n        style: {\n          padding: '12px 24px',\n          background: submitting ? '#ccc' : '#28a745',\n          color: 'white',\n          border: 'none',\n          borderRadius: '5px',\n          cursor: submitting ? 'not-allowed' : 'pointer',\n          fontWeight: 'bold'\n        },\n        children: submitting ? '⏳ Submitting...' : '✅ Submit Exam'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 257,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 196,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginTop: '20px',\n        textAlign: 'center',\n        background: 'white',\n        padding: '15px',\n        borderRadius: '10px',\n        boxShadow: '0 2px 10px rgba(0,0,0,0.1)'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          margin: 0,\n          fontSize: '16px'\n        },\n        children: [\"\\uD83D\\uDCCA Progress: \", Object.values(answers).filter(a => a.selectedOptions.length > 0).length, \" / \", exam.questions.length, \" answered\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 284,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 276,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 133,\n    columnNumber: 5\n  }, this);\n};\n_s(SimpleExamTaking, \"QklB5P3SQZq5oYTBHAuKel33V8A=\", false, function () {\n  return [useParams, useNavigate];\n});\n_c = SimpleExamTaking;\nexport default SimpleExamTaking;\nvar _c;\n$RefreshReg$(_c, \"SimpleExamTaking\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useNavigate", "api", "jsxDEV", "_jsxDEV", "SimpleExamTaking", "_s", "examId", "navigate", "exam", "setExam", "attemptId", "setAttemptId", "answers", "setAnswers", "currentQuestion", "setCurrentQuestion", "timeLeft", "setTimeLeft", "loading", "setLoading", "error", "setError", "submitting", "setSubmitting", "token", "localStorage", "getItem", "startExam", "timer", "setTimeout", "clearTimeout", "handleSubmit", "console", "log", "response", "post", "data", "attempt", "duration", "initialAnswers", "questions", "for<PERSON>ach", "question", "_id", "selectedOptions", "_error$response", "_error$response$data", "message", "handleAnswerChange", "questionId", "value", "prev", "answersArray", "Object", "values", "_error$response2", "_error$response2$data", "formatTime", "seconds", "mins", "Math", "floor", "secs", "toString", "padStart", "style", "textAlign", "padding", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "onClick", "marginTop", "currentQ", "max<PERSON><PERSON><PERSON>", "margin", "background", "borderRadius", "marginBottom", "display", "justifyContent", "alignItems", "title", "length", "fontSize", "fontWeight", "boxShadow", "text", "options", "map", "option", "index", "_answers$currentQ$_id", "border", "cursor", "transition", "type", "name", "checked", "onChange", "e", "target", "marginRight", "max", "disabled", "gap", "_", "_answers$exam$questio", "_answers$exam$questio2", "width", "height", "filter", "a", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/SimpleExamTaking.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport api from '../services/api';\n\nconst SimpleExamTaking = () => {\n  const { examId } = useParams();\n  const navigate = useNavigate();\n  const [exam, setExam] = useState(null);\n  const [attemptId, setAttemptId] = useState(null);\n  const [answers, setAnswers] = useState({});\n  const [currentQuestion, setCurrentQuestion] = useState(0);\n  const [timeLeft, setTimeLeft] = useState(0);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [submitting, setSubmitting] = useState(false);\n\n  useEffect(() => {\n    const token = localStorage.getItem('token');\n    if (!token) {\n      navigate('/login');\n      return;\n    }\n    startExam();\n  }, [examId, navigate]);\n\n  useEffect(() => {\n    if (timeLeft > 0) {\n      const timer = setTimeout(() => setTimeLeft(timeLeft - 1), 1000);\n      return () => clearTimeout(timer);\n    } else if (timeLeft === 0 && exam) {\n      handleSubmit();\n    }\n  }, [timeLeft, exam]);\n\n  const startExam = async () => {\n    try {\n      console.log('🚀 Starting exam with ID:', examId);\n      const response = await api.post(`/exams/${examId}/start`);\n      console.log('✅ Start exam response:', response.data);\n\n      setExam(response.data.exam);\n      setAttemptId(response.data.attempt);\n      setTimeLeft(response.data.exam.duration * 60);\n\n      // Initialize answers\n      const initialAnswers = {};\n      response.data.exam.questions.forEach(question => {\n        initialAnswers[question._id] = {\n          question: question._id,\n          selectedOptions: []\n        };\n      });\n      setAnswers(initialAnswers);\n      setLoading(false);\n    } catch (error) {\n      console.error('❌ Error starting exam:', error);\n      setError(`Failed to start exam: ${error.response?.data?.message || error.message}`);\n      setLoading(false);\n    }\n  };\n\n  const handleAnswerChange = (questionId, value) => {\n    setAnswers(prev => ({\n      ...prev,\n      [questionId]: {\n        question: questionId,\n        selectedOptions: [value]\n      }\n    }));\n  };\n\n  const handleSubmit = async () => {\n    if (submitting) return;\n    \n    setSubmitting(true);\n    try {\n      console.log('📤 Submitting exam with attemptId:', attemptId);\n      const answersArray = Object.values(answers);\n      console.log('📝 Answers to submit:', answersArray);\n      \n      const response = await api.post(`/exam-attempts/${attemptId}/submit`, { answers: answersArray });\n      console.log('✅ Submit response:', response.data);\n      \n      navigate(`/results/${attemptId}`);\n    } catch (error) {\n      console.error('❌ Submit error:', error);\n      setError(`Failed to submit exam: ${error.response?.data?.message || error.message}`);\n      setSubmitting(false);\n    }\n  };\n\n  const formatTime = (seconds) => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins}:${secs.toString().padStart(2, '0')}`;\n  };\n\n  if (loading) {\n    return (\n      <div style={{ textAlign: 'center', padding: '50px' }}>\n        <h2>🔄 Loading Exam...</h2>\n        <p>Please wait while we prepare your exam.</p>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div style={{ textAlign: 'center', padding: '50px', color: 'red' }}>\n        <h2>❌ Error</h2>\n        <p>{error}</p>\n        <button onClick={() => navigate('/exams')} style={{ padding: '10px 20px', marginTop: '20px' }}>\n          Back to Exams\n        </button>\n      </div>\n    );\n  }\n\n  if (!exam) {\n    return (\n      <div style={{ textAlign: 'center', padding: '50px' }}>\n        <h2>❌ Exam Not Found</h2>\n        <button onClick={() => navigate('/exams')} style={{ padding: '10px 20px' }}>\n          Back to Exams\n        </button>\n      </div>\n    );\n  }\n\n  const currentQ = exam.questions[currentQuestion];\n\n  return (\n    <div style={{ maxWidth: '800px', margin: '0 auto', padding: '20px' }}>\n      {/* Header */}\n      <div style={{ \n        background: '#007bff', \n        color: 'white', \n        padding: '20px', \n        borderRadius: '10px', \n        marginBottom: '20px',\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center'\n      }}>\n        <div>\n          <h2 style={{ margin: 0 }}>{exam.title}</h2>\n          <p style={{ margin: '5px 0 0 0' }}>Question {currentQuestion + 1} of {exam.questions.length}</p>\n        </div>\n        <div style={{ textAlign: 'right' }}>\n          <div style={{ fontSize: '24px', fontWeight: 'bold' }}>\n            ⏰ {formatTime(timeLeft)}\n          </div>\n          <div style={{ fontSize: '14px' }}>Time Remaining</div>\n        </div>\n      </div>\n\n      {/* Question */}\n      <div style={{ \n        background: 'white', \n        padding: '30px', \n        borderRadius: '10px', \n        boxShadow: '0 2px 10px rgba(0,0,0,0.1)',\n        marginBottom: '20px'\n      }}>\n        <h3 style={{ marginBottom: '20px', fontSize: '20px' }}>\n          {currentQ.text}\n        </h3>\n\n        <div style={{ marginBottom: '20px' }}>\n          {currentQ.options.map((option, index) => (\n            <label key={index} style={{ \n              display: 'block', \n              padding: '15px', \n              margin: '10px 0',\n              background: '#f8f9fa',\n              border: '2px solid #dee2e6',\n              borderRadius: '8px',\n              cursor: 'pointer',\n              transition: 'all 0.3s'\n            }}>\n              <input\n                type=\"radio\"\n                name={`question_${currentQ._id}`}\n                value={option.text}\n                checked={answers[currentQ._id]?.selectedOptions[0] === option.text}\n                onChange={(e) => handleAnswerChange(currentQ._id, e.target.value)}\n                style={{ marginRight: '10px' }}\n              />\n              <span style={{ fontSize: '16px' }}>{option.text}</span>\n            </label>\n          ))}\n        </div>\n      </div>\n\n      {/* Navigation */}\n      <div style={{ \n        display: 'flex', \n        justifyContent: 'space-between', \n        alignItems: 'center',\n        background: 'white',\n        padding: '20px',\n        borderRadius: '10px',\n        boxShadow: '0 2px 10px rgba(0,0,0,0.1)'\n      }}>\n        <button\n          onClick={() => setCurrentQuestion(Math.max(0, currentQuestion - 1))}\n          disabled={currentQuestion === 0}\n          style={{\n            padding: '12px 24px',\n            background: currentQuestion === 0 ? '#ccc' : '#6c757d',\n            color: 'white',\n            border: 'none',\n            borderRadius: '5px',\n            cursor: currentQuestion === 0 ? 'not-allowed' : 'pointer'\n          }}\n        >\n          ← Previous\n        </button>\n\n        <div style={{ display: 'flex', gap: '10px' }}>\n          {exam.questions.map((_, index) => (\n            <button\n              key={index}\n              onClick={() => setCurrentQuestion(index)}\n              style={{\n                width: '40px',\n                height: '40px',\n                borderRadius: '50%',\n                border: 'none',\n                background: index === currentQuestion ? '#007bff' : \n                           answers[exam.questions[index]._id]?.selectedOptions.length > 0 ? '#28a745' : '#dee2e6',\n                color: index === currentQuestion || answers[exam.questions[index]._id]?.selectedOptions.length > 0 ? 'white' : '#6c757d',\n                cursor: 'pointer',\n                fontWeight: 'bold'\n              }}\n            >\n              {index + 1}\n            </button>\n          ))}\n        </div>\n\n        {currentQuestion < exam.questions.length - 1 ? (\n          <button\n            onClick={() => setCurrentQuestion(currentQuestion + 1)}\n            style={{\n              padding: '12px 24px',\n              background: '#007bff',\n              color: 'white',\n              border: 'none',\n              borderRadius: '5px',\n              cursor: 'pointer'\n            }}\n          >\n            Next →\n          </button>\n        ) : (\n          <button\n            onClick={handleSubmit}\n            disabled={submitting}\n            style={{\n              padding: '12px 24px',\n              background: submitting ? '#ccc' : '#28a745',\n              color: 'white',\n              border: 'none',\n              borderRadius: '5px',\n              cursor: submitting ? 'not-allowed' : 'pointer',\n              fontWeight: 'bold'\n            }}\n          >\n            {submitting ? '⏳ Submitting...' : '✅ Submit Exam'}\n          </button>\n        )}\n      </div>\n\n      {/* Progress */}\n      <div style={{ \n        marginTop: '20px', \n        textAlign: 'center',\n        background: 'white',\n        padding: '15px',\n        borderRadius: '10px',\n        boxShadow: '0 2px 10px rgba(0,0,0,0.1)'\n      }}>\n        <p style={{ margin: 0, fontSize: '16px' }}>\n          📊 Progress: {Object.values(answers).filter(a => a.selectedOptions.length > 0).length} / {exam.questions.length} answered\n        </p>\n      </div>\n    </div>\n  );\n};\n\nexport default SimpleExamTaking;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,OAAOC,GAAG,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAM;IAAEC;EAAO,CAAC,GAAGP,SAAS,CAAC,CAAC;EAC9B,MAAMQ,QAAQ,GAAGP,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACQ,IAAI,EAAEC,OAAO,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACa,SAAS,EAAEC,YAAY,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACe,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1C,MAAM,CAACiB,eAAe,EAAEC,kBAAkB,CAAC,GAAGlB,QAAQ,CAAC,CAAC,CAAC;EACzD,MAAM,CAACmB,QAAQ,EAAEC,WAAW,CAAC,GAAGpB,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAACqB,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACuB,KAAK,EAAEC,QAAQ,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACyB,UAAU,EAAEC,aAAa,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAEnDC,SAAS,CAAC,MAAM;IACd,MAAM0B,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAI,CAACF,KAAK,EAAE;MACVjB,QAAQ,CAAC,QAAQ,CAAC;MAClB;IACF;IACAoB,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAACrB,MAAM,EAAEC,QAAQ,CAAC,CAAC;EAEtBT,SAAS,CAAC,MAAM;IACd,IAAIkB,QAAQ,GAAG,CAAC,EAAE;MAChB,MAAMY,KAAK,GAAGC,UAAU,CAAC,MAAMZ,WAAW,CAACD,QAAQ,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC;MAC/D,OAAO,MAAMc,YAAY,CAACF,KAAK,CAAC;IAClC,CAAC,MAAM,IAAIZ,QAAQ,KAAK,CAAC,IAAIR,IAAI,EAAE;MACjCuB,YAAY,CAAC,CAAC;IAChB;EACF,CAAC,EAAE,CAACf,QAAQ,EAAER,IAAI,CAAC,CAAC;EAEpB,MAAMmB,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI;MACFK,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE3B,MAAM,CAAC;MAChD,MAAM4B,QAAQ,GAAG,MAAMjC,GAAG,CAACkC,IAAI,CAAC,UAAU7B,MAAM,QAAQ,CAAC;MACzD0B,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEC,QAAQ,CAACE,IAAI,CAAC;MAEpD3B,OAAO,CAACyB,QAAQ,CAACE,IAAI,CAAC5B,IAAI,CAAC;MAC3BG,YAAY,CAACuB,QAAQ,CAACE,IAAI,CAACC,OAAO,CAAC;MACnCpB,WAAW,CAACiB,QAAQ,CAACE,IAAI,CAAC5B,IAAI,CAAC8B,QAAQ,GAAG,EAAE,CAAC;;MAE7C;MACA,MAAMC,cAAc,GAAG,CAAC,CAAC;MACzBL,QAAQ,CAACE,IAAI,CAAC5B,IAAI,CAACgC,SAAS,CAACC,OAAO,CAACC,QAAQ,IAAI;QAC/CH,cAAc,CAACG,QAAQ,CAACC,GAAG,CAAC,GAAG;UAC7BD,QAAQ,EAAEA,QAAQ,CAACC,GAAG;UACtBC,eAAe,EAAE;QACnB,CAAC;MACH,CAAC,CAAC;MACF/B,UAAU,CAAC0B,cAAc,CAAC;MAC1BpB,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC,OAAOC,KAAK,EAAE;MAAA,IAAAyB,eAAA,EAAAC,oBAAA;MACdd,OAAO,CAACZ,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9CC,QAAQ,CAAC,yBAAyB,EAAAwB,eAAA,GAAAzB,KAAK,CAACc,QAAQ,cAAAW,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBT,IAAI,cAAAU,oBAAA,uBAApBA,oBAAA,CAAsBC,OAAO,KAAI3B,KAAK,CAAC2B,OAAO,EAAE,CAAC;MACnF5B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM6B,kBAAkB,GAAGA,CAACC,UAAU,EAAEC,KAAK,KAAK;IAChDrC,UAAU,CAACsC,IAAI,KAAK;MAClB,GAAGA,IAAI;MACP,CAACF,UAAU,GAAG;QACZP,QAAQ,EAAEO,UAAU;QACpBL,eAAe,EAAE,CAACM,KAAK;MACzB;IACF,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMnB,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAIT,UAAU,EAAE;IAEhBC,aAAa,CAAC,IAAI,CAAC;IACnB,IAAI;MACFS,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEvB,SAAS,CAAC;MAC5D,MAAM0C,YAAY,GAAGC,MAAM,CAACC,MAAM,CAAC1C,OAAO,CAAC;MAC3CoB,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEmB,YAAY,CAAC;MAElD,MAAMlB,QAAQ,GAAG,MAAMjC,GAAG,CAACkC,IAAI,CAAC,kBAAkBzB,SAAS,SAAS,EAAE;QAAEE,OAAO,EAAEwC;MAAa,CAAC,CAAC;MAChGpB,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEC,QAAQ,CAACE,IAAI,CAAC;MAEhD7B,QAAQ,CAAC,YAAYG,SAAS,EAAE,CAAC;IACnC,CAAC,CAAC,OAAOU,KAAK,EAAE;MAAA,IAAAmC,gBAAA,EAAAC,qBAAA;MACdxB,OAAO,CAACZ,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;MACvCC,QAAQ,CAAC,0BAA0B,EAAAkC,gBAAA,GAAAnC,KAAK,CAACc,QAAQ,cAAAqB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBnB,IAAI,cAAAoB,qBAAA,uBAApBA,qBAAA,CAAsBT,OAAO,KAAI3B,KAAK,CAAC2B,OAAO,EAAE,CAAC;MACpFxB,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAMkC,UAAU,GAAIC,OAAO,IAAK;IAC9B,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;IACrC,MAAMI,IAAI,GAAGJ,OAAO,GAAG,EAAE;IACzB,OAAO,GAAGC,IAAI,IAAIG,IAAI,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EACtD,CAAC;EAED,IAAI9C,OAAO,EAAE;IACX,oBACEf,OAAA;MAAK8D,KAAK,EAAE;QAAEC,SAAS,EAAE,QAAQ;QAAEC,OAAO,EAAE;MAAO,CAAE;MAAAC,QAAA,gBACnDjE,OAAA;QAAAiE,QAAA,EAAI;MAAkB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC3BrE,OAAA;QAAAiE,QAAA,EAAG;MAAuC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3C,CAAC;EAEV;EAEA,IAAIpD,KAAK,EAAE;IACT,oBACEjB,OAAA;MAAK8D,KAAK,EAAE;QAAEC,SAAS,EAAE,QAAQ;QAAEC,OAAO,EAAE,MAAM;QAAEM,KAAK,EAAE;MAAM,CAAE;MAAAL,QAAA,gBACjEjE,OAAA;QAAAiE,QAAA,EAAI;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAChBrE,OAAA;QAAAiE,QAAA,EAAIhD;MAAK;QAAAiD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACdrE,OAAA;QAAQuE,OAAO,EAAEA,CAAA,KAAMnE,QAAQ,CAAC,QAAQ,CAAE;QAAC0D,KAAK,EAAE;UAAEE,OAAO,EAAE,WAAW;UAAEQ,SAAS,EAAE;QAAO,CAAE;QAAAP,QAAA,EAAC;MAE/F;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;EAEA,IAAI,CAAChE,IAAI,EAAE;IACT,oBACEL,OAAA;MAAK8D,KAAK,EAAE;QAAEC,SAAS,EAAE,QAAQ;QAAEC,OAAO,EAAE;MAAO,CAAE;MAAAC,QAAA,gBACnDjE,OAAA;QAAAiE,QAAA,EAAI;MAAgB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACzBrE,OAAA;QAAQuE,OAAO,EAAEA,CAAA,KAAMnE,QAAQ,CAAC,QAAQ,CAAE;QAAC0D,KAAK,EAAE;UAAEE,OAAO,EAAE;QAAY,CAAE;QAAAC,QAAA,EAAC;MAE5E;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;EAEA,MAAMI,QAAQ,GAAGpE,IAAI,CAACgC,SAAS,CAAC1B,eAAe,CAAC;EAEhD,oBACEX,OAAA;IAAK8D,KAAK,EAAE;MAAEY,QAAQ,EAAE,OAAO;MAAEC,MAAM,EAAE,QAAQ;MAAEX,OAAO,EAAE;IAAO,CAAE;IAAAC,QAAA,gBAEnEjE,OAAA;MAAK8D,KAAK,EAAE;QACVc,UAAU,EAAE,SAAS;QACrBN,KAAK,EAAE,OAAO;QACdN,OAAO,EAAE,MAAM;QACfa,YAAY,EAAE,MAAM;QACpBC,YAAY,EAAE,MAAM;QACpBC,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,eAAe;QAC/BC,UAAU,EAAE;MACd,CAAE;MAAAhB,QAAA,gBACAjE,OAAA;QAAAiE,QAAA,gBACEjE,OAAA;UAAI8D,KAAK,EAAE;YAAEa,MAAM,EAAE;UAAE,CAAE;UAAAV,QAAA,EAAE5D,IAAI,CAAC6E;QAAK;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC3CrE,OAAA;UAAG8D,KAAK,EAAE;YAAEa,MAAM,EAAE;UAAY,CAAE;UAAAV,QAAA,GAAC,WAAS,EAACtD,eAAe,GAAG,CAAC,EAAC,MAAI,EAACN,IAAI,CAACgC,SAAS,CAAC8C,MAAM;QAAA;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7F,CAAC,eACNrE,OAAA;QAAK8D,KAAK,EAAE;UAAEC,SAAS,EAAE;QAAQ,CAAE;QAAAE,QAAA,gBACjCjE,OAAA;UAAK8D,KAAK,EAAE;YAAEsB,QAAQ,EAAE,MAAM;YAAEC,UAAU,EAAE;UAAO,CAAE;UAAApB,QAAA,GAAC,SAClD,EAACX,UAAU,CAACzC,QAAQ,CAAC;QAAA;UAAAqD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,eACNrE,OAAA;UAAK8D,KAAK,EAAE;YAAEsB,QAAQ,EAAE;UAAO,CAAE;UAAAnB,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNrE,OAAA;MAAK8D,KAAK,EAAE;QACVc,UAAU,EAAE,OAAO;QACnBZ,OAAO,EAAE,MAAM;QACfa,YAAY,EAAE,MAAM;QACpBS,SAAS,EAAE,4BAA4B;QACvCR,YAAY,EAAE;MAChB,CAAE;MAAAb,QAAA,gBACAjE,OAAA;QAAI8D,KAAK,EAAE;UAAEgB,YAAY,EAAE,MAAM;UAAEM,QAAQ,EAAE;QAAO,CAAE;QAAAnB,QAAA,EACnDQ,QAAQ,CAACc;MAAI;QAAArB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC,eAELrE,OAAA;QAAK8D,KAAK,EAAE;UAAEgB,YAAY,EAAE;QAAO,CAAE;QAAAb,QAAA,EAClCQ,QAAQ,CAACe,OAAO,CAACC,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK;UAAA,IAAAC,qBAAA;UAAA,oBAClC5F,OAAA;YAAmB8D,KAAK,EAAE;cACxBiB,OAAO,EAAE,OAAO;cAChBf,OAAO,EAAE,MAAM;cACfW,MAAM,EAAE,QAAQ;cAChBC,UAAU,EAAE,SAAS;cACrBiB,MAAM,EAAE,mBAAmB;cAC3BhB,YAAY,EAAE,KAAK;cACnBiB,MAAM,EAAE,SAAS;cACjBC,UAAU,EAAE;YACd,CAAE;YAAA9B,QAAA,gBACAjE,OAAA;cACEgG,IAAI,EAAC,OAAO;cACZC,IAAI,EAAE,YAAYxB,QAAQ,CAACjC,GAAG,EAAG;cACjCO,KAAK,EAAE2C,MAAM,CAACH,IAAK;cACnBW,OAAO,EAAE,EAAAN,qBAAA,GAAAnF,OAAO,CAACgE,QAAQ,CAACjC,GAAG,CAAC,cAAAoD,qBAAA,uBAArBA,qBAAA,CAAuBnD,eAAe,CAAC,CAAC,CAAC,MAAKiD,MAAM,CAACH,IAAK;cACnEY,QAAQ,EAAGC,CAAC,IAAKvD,kBAAkB,CAAC4B,QAAQ,CAACjC,GAAG,EAAE4D,CAAC,CAACC,MAAM,CAACtD,KAAK,CAAE;cAClEe,KAAK,EAAE;gBAAEwC,WAAW,EAAE;cAAO;YAAE;cAAApC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC,eACFrE,OAAA;cAAM8D,KAAK,EAAE;gBAAEsB,QAAQ,EAAE;cAAO,CAAE;cAAAnB,QAAA,EAAEyB,MAAM,CAACH;YAAI;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA,GAlB7CsB,KAAK;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAmBV,CAAC;QAAA,CACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNrE,OAAA;MAAK8D,KAAK,EAAE;QACViB,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,eAAe;QAC/BC,UAAU,EAAE,QAAQ;QACpBL,UAAU,EAAE,OAAO;QACnBZ,OAAO,EAAE,MAAM;QACfa,YAAY,EAAE,MAAM;QACpBS,SAAS,EAAE;MACb,CAAE;MAAArB,QAAA,gBACAjE,OAAA;QACEuE,OAAO,EAAEA,CAAA,KAAM3D,kBAAkB,CAAC6C,IAAI,CAAC8C,GAAG,CAAC,CAAC,EAAE5F,eAAe,GAAG,CAAC,CAAC,CAAE;QACpE6F,QAAQ,EAAE7F,eAAe,KAAK,CAAE;QAChCmD,KAAK,EAAE;UACLE,OAAO,EAAE,WAAW;UACpBY,UAAU,EAAEjE,eAAe,KAAK,CAAC,GAAG,MAAM,GAAG,SAAS;UACtD2D,KAAK,EAAE,OAAO;UACduB,MAAM,EAAE,MAAM;UACdhB,YAAY,EAAE,KAAK;UACnBiB,MAAM,EAAEnF,eAAe,KAAK,CAAC,GAAG,aAAa,GAAG;QAClD,CAAE;QAAAsD,QAAA,EACH;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAETrE,OAAA;QAAK8D,KAAK,EAAE;UAAEiB,OAAO,EAAE,MAAM;UAAE0B,GAAG,EAAE;QAAO,CAAE;QAAAxC,QAAA,EAC1C5D,IAAI,CAACgC,SAAS,CAACoD,GAAG,CAAC,CAACiB,CAAC,EAAEf,KAAK;UAAA,IAAAgB,qBAAA,EAAAC,sBAAA;UAAA,oBAC3B5G,OAAA;YAEEuE,OAAO,EAAEA,CAAA,KAAM3D,kBAAkB,CAAC+E,KAAK,CAAE;YACzC7B,KAAK,EAAE;cACL+C,KAAK,EAAE,MAAM;cACbC,MAAM,EAAE,MAAM;cACdjC,YAAY,EAAE,KAAK;cACnBgB,MAAM,EAAE,MAAM;cACdjB,UAAU,EAAEe,KAAK,KAAKhF,eAAe,GAAG,SAAS,GACtC,EAAAgG,qBAAA,GAAAlG,OAAO,CAACJ,IAAI,CAACgC,SAAS,CAACsD,KAAK,CAAC,CAACnD,GAAG,CAAC,cAAAmE,qBAAA,uBAAlCA,qBAAA,CAAoClE,eAAe,CAAC0C,MAAM,IAAG,CAAC,GAAG,SAAS,GAAG,SAAS;cACjGb,KAAK,EAAEqB,KAAK,KAAKhF,eAAe,IAAI,EAAAiG,sBAAA,GAAAnG,OAAO,CAACJ,IAAI,CAACgC,SAAS,CAACsD,KAAK,CAAC,CAACnD,GAAG,CAAC,cAAAoE,sBAAA,uBAAlCA,sBAAA,CAAoCnE,eAAe,CAAC0C,MAAM,IAAG,CAAC,GAAG,OAAO,GAAG,SAAS;cACxHW,MAAM,EAAE,SAAS;cACjBT,UAAU,EAAE;YACd,CAAE;YAAApB,QAAA,EAED0B,KAAK,GAAG;UAAC,GAdLA,KAAK;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAeJ,CAAC;QAAA,CACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,EAEL1D,eAAe,GAAGN,IAAI,CAACgC,SAAS,CAAC8C,MAAM,GAAG,CAAC,gBAC1CnF,OAAA;QACEuE,OAAO,EAAEA,CAAA,KAAM3D,kBAAkB,CAACD,eAAe,GAAG,CAAC,CAAE;QACvDmD,KAAK,EAAE;UACLE,OAAO,EAAE,WAAW;UACpBY,UAAU,EAAE,SAAS;UACrBN,KAAK,EAAE,OAAO;UACduB,MAAM,EAAE,MAAM;UACdhB,YAAY,EAAE,KAAK;UACnBiB,MAAM,EAAE;QACV,CAAE;QAAA7B,QAAA,EACH;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,gBAETrE,OAAA;QACEuE,OAAO,EAAE3C,YAAa;QACtB4E,QAAQ,EAAErF,UAAW;QACrB2C,KAAK,EAAE;UACLE,OAAO,EAAE,WAAW;UACpBY,UAAU,EAAEzD,UAAU,GAAG,MAAM,GAAG,SAAS;UAC3CmD,KAAK,EAAE,OAAO;UACduB,MAAM,EAAE,MAAM;UACdhB,YAAY,EAAE,KAAK;UACnBiB,MAAM,EAAE3E,UAAU,GAAG,aAAa,GAAG,SAAS;UAC9CkE,UAAU,EAAE;QACd,CAAE;QAAApB,QAAA,EAED9C,UAAU,GAAG,iBAAiB,GAAG;MAAe;QAAA+C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C,CACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGNrE,OAAA;MAAK8D,KAAK,EAAE;QACVU,SAAS,EAAE,MAAM;QACjBT,SAAS,EAAE,QAAQ;QACnBa,UAAU,EAAE,OAAO;QACnBZ,OAAO,EAAE,MAAM;QACfa,YAAY,EAAE,MAAM;QACpBS,SAAS,EAAE;MACb,CAAE;MAAArB,QAAA,eACAjE,OAAA;QAAG8D,KAAK,EAAE;UAAEa,MAAM,EAAE,CAAC;UAAES,QAAQ,EAAE;QAAO,CAAE;QAAAnB,QAAA,GAAC,yBAC5B,EAACf,MAAM,CAACC,MAAM,CAAC1C,OAAO,CAAC,CAACsG,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACvE,eAAe,CAAC0C,MAAM,GAAG,CAAC,CAAC,CAACA,MAAM,EAAC,KAAG,EAAC9E,IAAI,CAACgC,SAAS,CAAC8C,MAAM,EAAC,WAClH;MAAA;QAAAjB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACnE,EAAA,CA7RID,gBAAgB;EAAA,QACDL,SAAS,EACXC,WAAW;AAAA;AAAAoH,EAAA,GAFxBhH,gBAAgB;AA+RtB,eAAeA,gBAAgB;AAAC,IAAAgH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}