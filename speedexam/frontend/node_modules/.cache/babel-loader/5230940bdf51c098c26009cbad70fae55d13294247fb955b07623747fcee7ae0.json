{"ast": null, "code": "'use strict';\n\nvar Cancel = require('./Cancel');\n\n/**\n * A `CancelToken` is an object that can be used to request cancellation of an operation.\n *\n * @class\n * @param {Function} executor The executor function.\n */\nfunction CancelToken(executor) {\n  if (typeof executor !== 'function') {\n    throw new TypeError('executor must be a function.');\n  }\n  var resolvePromise;\n  this.promise = new Promise(function promiseExecutor(resolve) {\n    resolvePromise = resolve;\n  });\n  var token = this;\n\n  // eslint-disable-next-line func-names\n  this.promise.then(function (cancel) {\n    if (!token._listeners) return;\n    var i;\n    var l = token._listeners.length;\n    for (i = 0; i < l; i++) {\n      token._listeners[i](cancel);\n    }\n    token._listeners = null;\n  });\n\n  // eslint-disable-next-line func-names\n  this.promise.then = function (onfulfilled) {\n    var _resolve;\n    // eslint-disable-next-line func-names\n    var promise = new Promise(function (resolve) {\n      token.subscribe(resolve);\n      _resolve = resolve;\n    }).then(onfulfilled);\n    promise.cancel = function reject() {\n      token.unsubscribe(_resolve);\n    };\n    return promise;\n  };\n  executor(function cancel(message) {\n    if (token.reason) {\n      // Cancellation has already been requested\n      return;\n    }\n    token.reason = new Cancel(message);\n    resolvePromise(token.reason);\n  });\n}\n\n/**\n * Throws a `Cancel` if cancellation has been requested.\n */\nCancelToken.prototype.throwIfRequested = function throwIfRequested() {\n  if (this.reason) {\n    throw this.reason;\n  }\n};\n\n/**\n * Subscribe to the cancel signal\n */\n\nCancelToken.prototype.subscribe = function subscribe(listener) {\n  if (this.reason) {\n    listener(this.reason);\n    return;\n  }\n  if (this._listeners) {\n    this._listeners.push(listener);\n  } else {\n    this._listeners = [listener];\n  }\n};\n\n/**\n * Unsubscribe from the cancel signal\n */\n\nCancelToken.prototype.unsubscribe = function unsubscribe(listener) {\n  if (!this._listeners) {\n    return;\n  }\n  var index = this._listeners.indexOf(listener);\n  if (index !== -1) {\n    this._listeners.splice(index, 1);\n  }\n};\n\n/**\n * Returns an object that contains a new `CancelToken` and a function that, when called,\n * cancels the `CancelToken`.\n */\nCancelToken.source = function source() {\n  var cancel;\n  var token = new CancelToken(function executor(c) {\n    cancel = c;\n  });\n  return {\n    token: token,\n    cancel: cancel\n  };\n};\nmodule.exports = CancelToken;", "map": {"version": 3, "names": ["Cancel", "require", "CancelToken", "executor", "TypeError", "resolvePromise", "promise", "Promise", "promiseExecutor", "resolve", "token", "then", "cancel", "_listeners", "i", "l", "length", "onfulfilled", "_resolve", "subscribe", "reject", "unsubscribe", "message", "reason", "prototype", "throwIfRequested", "listener", "push", "index", "indexOf", "splice", "source", "c", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/node_modules/axios/lib/cancel/CancelToken.js"], "sourcesContent": ["'use strict';\n\nvar Cancel = require('./Cancel');\n\n/**\n * A `CancelToken` is an object that can be used to request cancellation of an operation.\n *\n * @class\n * @param {Function} executor The executor function.\n */\nfunction CancelToken(executor) {\n  if (typeof executor !== 'function') {\n    throw new TypeError('executor must be a function.');\n  }\n\n  var resolvePromise;\n\n  this.promise = new Promise(function promiseExecutor(resolve) {\n    resolvePromise = resolve;\n  });\n\n  var token = this;\n\n  // eslint-disable-next-line func-names\n  this.promise.then(function(cancel) {\n    if (!token._listeners) return;\n\n    var i;\n    var l = token._listeners.length;\n\n    for (i = 0; i < l; i++) {\n      token._listeners[i](cancel);\n    }\n    token._listeners = null;\n  });\n\n  // eslint-disable-next-line func-names\n  this.promise.then = function(onfulfilled) {\n    var _resolve;\n    // eslint-disable-next-line func-names\n    var promise = new Promise(function(resolve) {\n      token.subscribe(resolve);\n      _resolve = resolve;\n    }).then(onfulfilled);\n\n    promise.cancel = function reject() {\n      token.unsubscribe(_resolve);\n    };\n\n    return promise;\n  };\n\n  executor(function cancel(message) {\n    if (token.reason) {\n      // Cancellation has already been requested\n      return;\n    }\n\n    token.reason = new Cancel(message);\n    resolvePromise(token.reason);\n  });\n}\n\n/**\n * Throws a `Cancel` if cancellation has been requested.\n */\nCancelToken.prototype.throwIfRequested = function throwIfRequested() {\n  if (this.reason) {\n    throw this.reason;\n  }\n};\n\n/**\n * Subscribe to the cancel signal\n */\n\nCancelToken.prototype.subscribe = function subscribe(listener) {\n  if (this.reason) {\n    listener(this.reason);\n    return;\n  }\n\n  if (this._listeners) {\n    this._listeners.push(listener);\n  } else {\n    this._listeners = [listener];\n  }\n};\n\n/**\n * Unsubscribe from the cancel signal\n */\n\nCancelToken.prototype.unsubscribe = function unsubscribe(listener) {\n  if (!this._listeners) {\n    return;\n  }\n  var index = this._listeners.indexOf(listener);\n  if (index !== -1) {\n    this._listeners.splice(index, 1);\n  }\n};\n\n/**\n * Returns an object that contains a new `CancelToken` and a function that, when called,\n * cancels the `CancelToken`.\n */\nCancelToken.source = function source() {\n  var cancel;\n  var token = new CancelToken(function executor(c) {\n    cancel = c;\n  });\n  return {\n    token: token,\n    cancel: cancel\n  };\n};\n\nmodule.exports = CancelToken;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,MAAM,GAAGC,OAAO,CAAC,UAAU,CAAC;;AAEhC;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,WAAWA,CAACC,QAAQ,EAAE;EAC7B,IAAI,OAAOA,QAAQ,KAAK,UAAU,EAAE;IAClC,MAAM,IAAIC,SAAS,CAAC,8BAA8B,CAAC;EACrD;EAEA,IAAIC,cAAc;EAElB,IAAI,CAACC,OAAO,GAAG,IAAIC,OAAO,CAAC,SAASC,eAAeA,CAACC,OAAO,EAAE;IAC3DJ,cAAc,GAAGI,OAAO;EAC1B,CAAC,CAAC;EAEF,IAAIC,KAAK,GAAG,IAAI;;EAEhB;EACA,IAAI,CAACJ,OAAO,CAACK,IAAI,CAAC,UAASC,MAAM,EAAE;IACjC,IAAI,CAACF,KAAK,CAACG,UAAU,EAAE;IAEvB,IAAIC,CAAC;IACL,IAAIC,CAAC,GAAGL,KAAK,CAACG,UAAU,CAACG,MAAM;IAE/B,KAAKF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;MACtBJ,KAAK,CAACG,UAAU,CAACC,CAAC,CAAC,CAACF,MAAM,CAAC;IAC7B;IACAF,KAAK,CAACG,UAAU,GAAG,IAAI;EACzB,CAAC,CAAC;;EAEF;EACA,IAAI,CAACP,OAAO,CAACK,IAAI,GAAG,UAASM,WAAW,EAAE;IACxC,IAAIC,QAAQ;IACZ;IACA,IAAIZ,OAAO,GAAG,IAAIC,OAAO,CAAC,UAASE,OAAO,EAAE;MAC1CC,KAAK,CAACS,SAAS,CAACV,OAAO,CAAC;MACxBS,QAAQ,GAAGT,OAAO;IACpB,CAAC,CAAC,CAACE,IAAI,CAACM,WAAW,CAAC;IAEpBX,OAAO,CAACM,MAAM,GAAG,SAASQ,MAAMA,CAAA,EAAG;MACjCV,KAAK,CAACW,WAAW,CAACH,QAAQ,CAAC;IAC7B,CAAC;IAED,OAAOZ,OAAO;EAChB,CAAC;EAEDH,QAAQ,CAAC,SAASS,MAAMA,CAACU,OAAO,EAAE;IAChC,IAAIZ,KAAK,CAACa,MAAM,EAAE;MAChB;MACA;IACF;IAEAb,KAAK,CAACa,MAAM,GAAG,IAAIvB,MAAM,CAACsB,OAAO,CAAC;IAClCjB,cAAc,CAACK,KAAK,CAACa,MAAM,CAAC;EAC9B,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACArB,WAAW,CAACsB,SAAS,CAACC,gBAAgB,GAAG,SAASA,gBAAgBA,CAAA,EAAG;EACnE,IAAI,IAAI,CAACF,MAAM,EAAE;IACf,MAAM,IAAI,CAACA,MAAM;EACnB;AACF,CAAC;;AAED;AACA;AACA;;AAEArB,WAAW,CAACsB,SAAS,CAACL,SAAS,GAAG,SAASA,SAASA,CAACO,QAAQ,EAAE;EAC7D,IAAI,IAAI,CAACH,MAAM,EAAE;IACfG,QAAQ,CAAC,IAAI,CAACH,MAAM,CAAC;IACrB;EACF;EAEA,IAAI,IAAI,CAACV,UAAU,EAAE;IACnB,IAAI,CAACA,UAAU,CAACc,IAAI,CAACD,QAAQ,CAAC;EAChC,CAAC,MAAM;IACL,IAAI,CAACb,UAAU,GAAG,CAACa,QAAQ,CAAC;EAC9B;AACF,CAAC;;AAED;AACA;AACA;;AAEAxB,WAAW,CAACsB,SAAS,CAACH,WAAW,GAAG,SAASA,WAAWA,CAACK,QAAQ,EAAE;EACjE,IAAI,CAAC,IAAI,CAACb,UAAU,EAAE;IACpB;EACF;EACA,IAAIe,KAAK,GAAG,IAAI,CAACf,UAAU,CAACgB,OAAO,CAACH,QAAQ,CAAC;EAC7C,IAAIE,KAAK,KAAK,CAAC,CAAC,EAAE;IAChB,IAAI,CAACf,UAAU,CAACiB,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;EAClC;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA1B,WAAW,CAAC6B,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EACrC,IAAInB,MAAM;EACV,IAAIF,KAAK,GAAG,IAAIR,WAAW,CAAC,SAASC,QAAQA,CAAC6B,CAAC,EAAE;IAC/CpB,MAAM,GAAGoB,CAAC;EACZ,CAAC,CAAC;EACF,OAAO;IACLtB,KAAK,EAAEA,KAAK;IACZE,MAAM,EAAEA;EACV,CAAC;AACH,CAAC;AAEDqB,MAAM,CAACC,OAAO,GAAGhC,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}