{"ast": null, "code": "'use strict';\n\nvar utils = require('./../utils');\nvar buildURL = require('../helpers/buildURL');\nvar InterceptorManager = require('./InterceptorManager');\nvar dispatchRequest = require('./dispatchRequest');\nvar mergeConfig = require('./mergeConfig');\nvar validator = require('../helpers/validator');\nvar validators = validator.validators;\n/**\n * Create a new instance of Axios\n *\n * @param {Object} instanceConfig The default config for the instance\n */\nfunction Axios(instanceConfig) {\n  this.defaults = instanceConfig;\n  this.interceptors = {\n    request: new InterceptorManager(),\n    response: new InterceptorManager()\n  };\n}\n\n/**\n * Dispatch a request\n *\n * @param {Object} config The config specific for this request (merged with this.defaults)\n */\nAxios.prototype.request = function request(config) {\n  /*eslint no-param-reassign:0*/\n  // Allow for axios('example/url'[, config]) a la fetch API\n  if (typeof config === 'string') {\n    config = arguments[1] || {};\n    config.url = arguments[0];\n  } else {\n    config = config || {};\n  }\n  config = mergeConfig(this.defaults, config);\n\n  // Set config.method\n  if (config.method) {\n    config.method = config.method.toLowerCase();\n  } else if (this.defaults.method) {\n    config.method = this.defaults.method.toLowerCase();\n  } else {\n    config.method = 'get';\n  }\n  var transitional = config.transitional;\n  if (transitional !== undefined) {\n    validator.assertOptions(transitional, {\n      silentJSONParsing: validators.transitional(validators.boolean),\n      forcedJSONParsing: validators.transitional(validators.boolean),\n      clarifyTimeoutError: validators.transitional(validators.boolean)\n    }, false);\n  }\n\n  // filter out skipped interceptors\n  var requestInterceptorChain = [];\n  var synchronousRequestInterceptors = true;\n  this.interceptors.request.forEach(function unshiftRequestInterceptors(interceptor) {\n    if (typeof interceptor.runWhen === 'function' && interceptor.runWhen(config) === false) {\n      return;\n    }\n    synchronousRequestInterceptors = synchronousRequestInterceptors && interceptor.synchronous;\n    requestInterceptorChain.unshift(interceptor.fulfilled, interceptor.rejected);\n  });\n  var responseInterceptorChain = [];\n  this.interceptors.response.forEach(function pushResponseInterceptors(interceptor) {\n    responseInterceptorChain.push(interceptor.fulfilled, interceptor.rejected);\n  });\n  var promise;\n  if (!synchronousRequestInterceptors) {\n    var chain = [dispatchRequest, undefined];\n    Array.prototype.unshift.apply(chain, requestInterceptorChain);\n    chain = chain.concat(responseInterceptorChain);\n    promise = Promise.resolve(config);\n    while (chain.length) {\n      promise = promise.then(chain.shift(), chain.shift());\n    }\n    return promise;\n  }\n  var newConfig = config;\n  while (requestInterceptorChain.length) {\n    var onFulfilled = requestInterceptorChain.shift();\n    var onRejected = requestInterceptorChain.shift();\n    try {\n      newConfig = onFulfilled(newConfig);\n    } catch (error) {\n      onRejected(error);\n      break;\n    }\n  }\n  try {\n    promise = dispatchRequest(newConfig);\n  } catch (error) {\n    return Promise.reject(error);\n  }\n  while (responseInterceptorChain.length) {\n    promise = promise.then(responseInterceptorChain.shift(), responseInterceptorChain.shift());\n  }\n  return promise;\n};\nAxios.prototype.getUri = function getUri(config) {\n  config = mergeConfig(this.defaults, config);\n  return buildURL(config.url, config.params, config.paramsSerializer).replace(/^\\?/, '');\n};\n\n// Provide aliases for supported request methods\nutils.forEach(['delete', 'get', 'head', 'options'], function forEachMethodNoData(method) {\n  /*eslint func-names:0*/\n  Axios.prototype[method] = function (url, config) {\n    return this.request(mergeConfig(config || {}, {\n      method: method,\n      url: url,\n      data: (config || {}).data\n    }));\n  };\n});\nutils.forEach(['post', 'put', 'patch'], function forEachMethodWithData(method) {\n  /*eslint func-names:0*/\n  Axios.prototype[method] = function (url, data, config) {\n    return this.request(mergeConfig(config || {}, {\n      method: method,\n      url: url,\n      data: data\n    }));\n  };\n});\nmodule.exports = Axios;", "map": {"version": 3, "names": ["utils", "require", "buildURL", "InterceptorManager", "dispatchRequest", "mergeConfig", "validator", "validators", "A<PERSON>os", "instanceConfig", "defaults", "interceptors", "request", "response", "prototype", "config", "arguments", "url", "method", "toLowerCase", "transitional", "undefined", "assertOptions", "silentJSONParsing", "boolean", "forcedJSONParsing", "clarifyTimeoutError", "requestInterceptorChain", "synchronousRequestInterceptors", "for<PERSON>ach", "unshiftRequestInterceptors", "interceptor", "runWhen", "synchronous", "unshift", "fulfilled", "rejected", "responseInterceptorChain", "pushResponseInterceptors", "push", "promise", "chain", "Array", "apply", "concat", "Promise", "resolve", "length", "then", "shift", "newConfig", "onFulfilled", "onRejected", "error", "reject", "get<PERSON><PERSON>", "params", "paramsSerializer", "replace", "forEachMethodNoData", "data", "forEachMethodWithData", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/node_modules/axios/lib/core/Axios.js"], "sourcesContent": ["'use strict';\n\nvar utils = require('./../utils');\nvar buildURL = require('../helpers/buildURL');\nvar InterceptorManager = require('./InterceptorManager');\nvar dispatchRequest = require('./dispatchRequest');\nvar mergeConfig = require('./mergeConfig');\nvar validator = require('../helpers/validator');\n\nvar validators = validator.validators;\n/**\n * Create a new instance of Axios\n *\n * @param {Object} instanceConfig The default config for the instance\n */\nfunction Axios(instanceConfig) {\n  this.defaults = instanceConfig;\n  this.interceptors = {\n    request: new InterceptorManager(),\n    response: new InterceptorManager()\n  };\n}\n\n/**\n * Dispatch a request\n *\n * @param {Object} config The config specific for this request (merged with this.defaults)\n */\nAxios.prototype.request = function request(config) {\n  /*eslint no-param-reassign:0*/\n  // Allow for axios('example/url'[, config]) a la fetch API\n  if (typeof config === 'string') {\n    config = arguments[1] || {};\n    config.url = arguments[0];\n  } else {\n    config = config || {};\n  }\n\n  config = mergeConfig(this.defaults, config);\n\n  // Set config.method\n  if (config.method) {\n    config.method = config.method.toLowerCase();\n  } else if (this.defaults.method) {\n    config.method = this.defaults.method.toLowerCase();\n  } else {\n    config.method = 'get';\n  }\n\n  var transitional = config.transitional;\n\n  if (transitional !== undefined) {\n    validator.assertOptions(transitional, {\n      silentJSONParsing: validators.transitional(validators.boolean),\n      forcedJSONParsing: validators.transitional(validators.boolean),\n      clarifyTimeoutError: validators.transitional(validators.boolean)\n    }, false);\n  }\n\n  // filter out skipped interceptors\n  var requestInterceptorChain = [];\n  var synchronousRequestInterceptors = true;\n  this.interceptors.request.forEach(function unshiftRequestInterceptors(interceptor) {\n    if (typeof interceptor.runWhen === 'function' && interceptor.runWhen(config) === false) {\n      return;\n    }\n\n    synchronousRequestInterceptors = synchronousRequestInterceptors && interceptor.synchronous;\n\n    requestInterceptorChain.unshift(interceptor.fulfilled, interceptor.rejected);\n  });\n\n  var responseInterceptorChain = [];\n  this.interceptors.response.forEach(function pushResponseInterceptors(interceptor) {\n    responseInterceptorChain.push(interceptor.fulfilled, interceptor.rejected);\n  });\n\n  var promise;\n\n  if (!synchronousRequestInterceptors) {\n    var chain = [dispatchRequest, undefined];\n\n    Array.prototype.unshift.apply(chain, requestInterceptorChain);\n    chain = chain.concat(responseInterceptorChain);\n\n    promise = Promise.resolve(config);\n    while (chain.length) {\n      promise = promise.then(chain.shift(), chain.shift());\n    }\n\n    return promise;\n  }\n\n\n  var newConfig = config;\n  while (requestInterceptorChain.length) {\n    var onFulfilled = requestInterceptorChain.shift();\n    var onRejected = requestInterceptorChain.shift();\n    try {\n      newConfig = onFulfilled(newConfig);\n    } catch (error) {\n      onRejected(error);\n      break;\n    }\n  }\n\n  try {\n    promise = dispatchRequest(newConfig);\n  } catch (error) {\n    return Promise.reject(error);\n  }\n\n  while (responseInterceptorChain.length) {\n    promise = promise.then(responseInterceptorChain.shift(), responseInterceptorChain.shift());\n  }\n\n  return promise;\n};\n\nAxios.prototype.getUri = function getUri(config) {\n  config = mergeConfig(this.defaults, config);\n  return buildURL(config.url, config.params, config.paramsSerializer).replace(/^\\?/, '');\n};\n\n// Provide aliases for supported request methods\nutils.forEach(['delete', 'get', 'head', 'options'], function forEachMethodNoData(method) {\n  /*eslint func-names:0*/\n  Axios.prototype[method] = function(url, config) {\n    return this.request(mergeConfig(config || {}, {\n      method: method,\n      url: url,\n      data: (config || {}).data\n    }));\n  };\n});\n\nutils.forEach(['post', 'put', 'patch'], function forEachMethodWithData(method) {\n  /*eslint func-names:0*/\n  Axios.prototype[method] = function(url, data, config) {\n    return this.request(mergeConfig(config || {}, {\n      method: method,\n      url: url,\n      data: data\n    }));\n  };\n});\n\nmodule.exports = Axios;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,KAAK,GAAGC,OAAO,CAAC,YAAY,CAAC;AACjC,IAAIC,QAAQ,GAAGD,OAAO,CAAC,qBAAqB,CAAC;AAC7C,IAAIE,kBAAkB,GAAGF,OAAO,CAAC,sBAAsB,CAAC;AACxD,IAAIG,eAAe,GAAGH,OAAO,CAAC,mBAAmB,CAAC;AAClD,IAAII,WAAW,GAAGJ,OAAO,CAAC,eAAe,CAAC;AAC1C,IAAIK,SAAS,GAAGL,OAAO,CAAC,sBAAsB,CAAC;AAE/C,IAAIM,UAAU,GAAGD,SAAS,CAACC,UAAU;AACrC;AACA;AACA;AACA;AACA;AACA,SAASC,KAAKA,CAACC,cAAc,EAAE;EAC7B,IAAI,CAACC,QAAQ,GAAGD,cAAc;EAC9B,IAAI,CAACE,YAAY,GAAG;IAClBC,OAAO,EAAE,IAAIT,kBAAkB,CAAC,CAAC;IACjCU,QAAQ,EAAE,IAAIV,kBAAkB,CAAC;EACnC,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACAK,KAAK,CAACM,SAAS,CAACF,OAAO,GAAG,SAASA,OAAOA,CAACG,MAAM,EAAE;EACjD;EACA;EACA,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;IAC9BA,MAAM,GAAGC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IAC3BD,MAAM,CAACE,GAAG,GAAGD,SAAS,CAAC,CAAC,CAAC;EAC3B,CAAC,MAAM;IACLD,MAAM,GAAGA,MAAM,IAAI,CAAC,CAAC;EACvB;EAEAA,MAAM,GAAGV,WAAW,CAAC,IAAI,CAACK,QAAQ,EAAEK,MAAM,CAAC;;EAE3C;EACA,IAAIA,MAAM,CAACG,MAAM,EAAE;IACjBH,MAAM,CAACG,MAAM,GAAGH,MAAM,CAACG,MAAM,CAACC,WAAW,CAAC,CAAC;EAC7C,CAAC,MAAM,IAAI,IAAI,CAACT,QAAQ,CAACQ,MAAM,EAAE;IAC/BH,MAAM,CAACG,MAAM,GAAG,IAAI,CAACR,QAAQ,CAACQ,MAAM,CAACC,WAAW,CAAC,CAAC;EACpD,CAAC,MAAM;IACLJ,MAAM,CAACG,MAAM,GAAG,KAAK;EACvB;EAEA,IAAIE,YAAY,GAAGL,MAAM,CAACK,YAAY;EAEtC,IAAIA,YAAY,KAAKC,SAAS,EAAE;IAC9Bf,SAAS,CAACgB,aAAa,CAACF,YAAY,EAAE;MACpCG,iBAAiB,EAAEhB,UAAU,CAACa,YAAY,CAACb,UAAU,CAACiB,OAAO,CAAC;MAC9DC,iBAAiB,EAAElB,UAAU,CAACa,YAAY,CAACb,UAAU,CAACiB,OAAO,CAAC;MAC9DE,mBAAmB,EAAEnB,UAAU,CAACa,YAAY,CAACb,UAAU,CAACiB,OAAO;IACjE,CAAC,EAAE,KAAK,CAAC;EACX;;EAEA;EACA,IAAIG,uBAAuB,GAAG,EAAE;EAChC,IAAIC,8BAA8B,GAAG,IAAI;EACzC,IAAI,CAACjB,YAAY,CAACC,OAAO,CAACiB,OAAO,CAAC,SAASC,0BAA0BA,CAACC,WAAW,EAAE;IACjF,IAAI,OAAOA,WAAW,CAACC,OAAO,KAAK,UAAU,IAAID,WAAW,CAACC,OAAO,CAACjB,MAAM,CAAC,KAAK,KAAK,EAAE;MACtF;IACF;IAEAa,8BAA8B,GAAGA,8BAA8B,IAAIG,WAAW,CAACE,WAAW;IAE1FN,uBAAuB,CAACO,OAAO,CAACH,WAAW,CAACI,SAAS,EAAEJ,WAAW,CAACK,QAAQ,CAAC;EAC9E,CAAC,CAAC;EAEF,IAAIC,wBAAwB,GAAG,EAAE;EACjC,IAAI,CAAC1B,YAAY,CAACE,QAAQ,CAACgB,OAAO,CAAC,SAASS,wBAAwBA,CAACP,WAAW,EAAE;IAChFM,wBAAwB,CAACE,IAAI,CAACR,WAAW,CAACI,SAAS,EAAEJ,WAAW,CAACK,QAAQ,CAAC;EAC5E,CAAC,CAAC;EAEF,IAAII,OAAO;EAEX,IAAI,CAACZ,8BAA8B,EAAE;IACnC,IAAIa,KAAK,GAAG,CAACrC,eAAe,EAAEiB,SAAS,CAAC;IAExCqB,KAAK,CAAC5B,SAAS,CAACoB,OAAO,CAACS,KAAK,CAACF,KAAK,EAAEd,uBAAuB,CAAC;IAC7Dc,KAAK,GAAGA,KAAK,CAACG,MAAM,CAACP,wBAAwB,CAAC;IAE9CG,OAAO,GAAGK,OAAO,CAACC,OAAO,CAAC/B,MAAM,CAAC;IACjC,OAAO0B,KAAK,CAACM,MAAM,EAAE;MACnBP,OAAO,GAAGA,OAAO,CAACQ,IAAI,CAACP,KAAK,CAACQ,KAAK,CAAC,CAAC,EAAER,KAAK,CAACQ,KAAK,CAAC,CAAC,CAAC;IACtD;IAEA,OAAOT,OAAO;EAChB;EAGA,IAAIU,SAAS,GAAGnC,MAAM;EACtB,OAAOY,uBAAuB,CAACoB,MAAM,EAAE;IACrC,IAAII,WAAW,GAAGxB,uBAAuB,CAACsB,KAAK,CAAC,CAAC;IACjD,IAAIG,UAAU,GAAGzB,uBAAuB,CAACsB,KAAK,CAAC,CAAC;IAChD,IAAI;MACFC,SAAS,GAAGC,WAAW,CAACD,SAAS,CAAC;IACpC,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdD,UAAU,CAACC,KAAK,CAAC;MACjB;IACF;EACF;EAEA,IAAI;IACFb,OAAO,GAAGpC,eAAe,CAAC8C,SAAS,CAAC;EACtC,CAAC,CAAC,OAAOG,KAAK,EAAE;IACd,OAAOR,OAAO,CAACS,MAAM,CAACD,KAAK,CAAC;EAC9B;EAEA,OAAOhB,wBAAwB,CAACU,MAAM,EAAE;IACtCP,OAAO,GAAGA,OAAO,CAACQ,IAAI,CAACX,wBAAwB,CAACY,KAAK,CAAC,CAAC,EAAEZ,wBAAwB,CAACY,KAAK,CAAC,CAAC,CAAC;EAC5F;EAEA,OAAOT,OAAO;AAChB,CAAC;AAEDhC,KAAK,CAACM,SAAS,CAACyC,MAAM,GAAG,SAASA,MAAMA,CAACxC,MAAM,EAAE;EAC/CA,MAAM,GAAGV,WAAW,CAAC,IAAI,CAACK,QAAQ,EAAEK,MAAM,CAAC;EAC3C,OAAOb,QAAQ,CAACa,MAAM,CAACE,GAAG,EAAEF,MAAM,CAACyC,MAAM,EAAEzC,MAAM,CAAC0C,gBAAgB,CAAC,CAACC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;AACxF,CAAC;;AAED;AACA1D,KAAK,CAAC6B,OAAO,CAAC,CAAC,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE,SAAS8B,mBAAmBA,CAACzC,MAAM,EAAE;EACvF;EACAV,KAAK,CAACM,SAAS,CAACI,MAAM,CAAC,GAAG,UAASD,GAAG,EAAEF,MAAM,EAAE;IAC9C,OAAO,IAAI,CAACH,OAAO,CAACP,WAAW,CAACU,MAAM,IAAI,CAAC,CAAC,EAAE;MAC5CG,MAAM,EAAEA,MAAM;MACdD,GAAG,EAAEA,GAAG;MACR2C,IAAI,EAAE,CAAC7C,MAAM,IAAI,CAAC,CAAC,EAAE6C;IACvB,CAAC,CAAC,CAAC;EACL,CAAC;AACH,CAAC,CAAC;AAEF5D,KAAK,CAAC6B,OAAO,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,EAAE,SAASgC,qBAAqBA,CAAC3C,MAAM,EAAE;EAC7E;EACAV,KAAK,CAACM,SAAS,CAACI,MAAM,CAAC,GAAG,UAASD,GAAG,EAAE2C,IAAI,EAAE7C,MAAM,EAAE;IACpD,OAAO,IAAI,CAACH,OAAO,CAACP,WAAW,CAACU,MAAM,IAAI,CAAC,CAAC,EAAE;MAC5CG,MAAM,EAAEA,MAAM;MACdD,GAAG,EAAEA,GAAG;MACR2C,IAAI,EAAEA;IACR,CAAC,CAAC,CAAC;EACL,CAAC;AACH,CAAC,CAAC;AAEFE,MAAM,CAACC,OAAO,GAAGvD,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}