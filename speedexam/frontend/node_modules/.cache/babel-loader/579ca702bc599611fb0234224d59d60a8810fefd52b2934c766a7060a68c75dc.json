{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/ExamTaking.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport api from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ExamTaking = () => {\n  _s();\n  const {\n    examId\n  } = useParams();\n  const navigate = useNavigate();\n  const [exam, setExam] = useState(null);\n  const [attemptId, setAttemptId] = useState(null);\n  const [answers, setAnswers] = useState({});\n  const [currentQuestion, setCurrentQuestion] = useState(0);\n  const [timeLeft, setTimeLeft] = useState(0);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [submitting, setSubmitting] = useState(false);\n  useEffect(() => {\n    const token = localStorage.getItem('token');\n    if (!token) {\n      navigate('/login');\n      return;\n    }\n    startExam();\n  }, [examId, navigate]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  useEffect(() => {\n    if (timeLeft > 0) {\n      const timer = setTimeout(() => setTimeLeft(timeLeft - 1), 1000);\n      return () => clearTimeout(timer);\n    } else if (timeLeft === 0 && exam) {\n      handleSubmit();\n    }\n  }, [timeLeft, exam]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  const startExam = async () => {\n    try {\n      console.log('Starting exam with ID:', examId);\n      const response = await api.post(`/exams/${examId}/start`);\n      console.log('Start exam response:', response.data);\n      setExam(response.data.exam);\n      setAttemptId(response.data.attempt);\n      setTimeLeft(response.data.exam.duration * 60); // Convert minutes to seconds\n\n      // Initialize answers\n      const initialAnswers = {};\n      response.data.exam.questions.forEach(question => {\n        initialAnswers[question._id] = {\n          question: question._id,\n          selectedOptions: [],\n          answer: ''\n        };\n      });\n      setAnswers(initialAnswers);\n      console.log('Exam started successfully');\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('Error starting exam:', error);\n      setError(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Failed to start exam');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleAnswerChange = (questionId, value, isMultiple = false) => {\n    setAnswers(prev => ({\n      ...prev,\n      [questionId]: {\n        ...prev[questionId],\n        selectedOptions: isMultiple ? prev[questionId].selectedOptions.includes(value) ? prev[questionId].selectedOptions.filter(opt => opt !== value) : [...prev[questionId].selectedOptions, value] : [value]\n      }\n    }));\n  };\n  const handleSubmit = async () => {\n    if (submitting) return;\n    setSubmitting(true);\n    try {\n      const answersArray = Object.values(answers);\n      await api.post(`/exam-attempts/${attemptId}/submit`, {\n        answers: answersArray\n      });\n      navigate(`/results/${attemptId}`);\n    } catch (error) {\n      setError('Failed to submit exam');\n      setSubmitting(false);\n    }\n  };\n  const formatTime = seconds => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins}:${secs.toString().padStart(2, '0')}`;\n  };\n  if (loading) return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"loading\",\n    children: \"Starting exam...\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 99,\n    columnNumber: 23\n  }, this);\n  if (error) return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"error\",\n    children: error\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 100,\n    columnNumber: 21\n  }, this);\n  if (!exam) return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"error\",\n    children: \"Exam not found\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 101,\n    columnNumber: 21\n  }, this);\n  const question = exam.questions[currentQuestion];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"timer\",\n      children: [\"Time Left: \", formatTime(timeLeft)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 107,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n      children: exam.title\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 111,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: [\"Question \", currentQuestion + 1, \" of \", exam.questions.length]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 112,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"question-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"question-text\",\n        children: question.text\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"options\",\n        children: question.options.map((option, index) => {\n          var _answers$question$_id;\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"option\",\n            children: /*#__PURE__*/_jsxDEV(\"label\", {\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: question.type === 'MULTI_ANSWER' ? 'checkbox' : 'radio',\n                name: `question_${question._id}`,\n                value: option.text,\n                checked: (_answers$question$_id = answers[question._id]) === null || _answers$question$_id === void 0 ? void 0 : _answers$question$_id.selectedOptions.includes(option.text),\n                onChange: e => handleAnswerChange(question._id, option.text, question.type === 'MULTI_ANSWER')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 121,\n                columnNumber: 17\n              }, this), option.text]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 15\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 13\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"navigation\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn btn-secondary\",\n        onClick: () => setCurrentQuestion(Math.max(0, currentQuestion - 1)),\n        disabled: currentQuestion === 0,\n        children: \"Previous\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 9\n      }, this), currentQuestion < exam.questions.length - 1 ? /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn\",\n        onClick: () => setCurrentQuestion(currentQuestion + 1),\n        children: \"Next\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn\",\n        onClick: handleSubmit,\n        disabled: submitting,\n        children: submitting ? 'Submitting...' : 'Submit Exam'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 139,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 106,\n    columnNumber: 5\n  }, this);\n};\n_s(ExamTaking, \"QklB5P3SQZq5oYTBHAuKel33V8A=\", false, function () {\n  return [useParams, useNavigate];\n});\n_c = ExamTaking;\nexport default ExamTaking;\nvar _c;\n$RefreshReg$(_c, \"ExamTaking\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useNavigate", "api", "jsxDEV", "_jsxDEV", "ExamTaking", "_s", "examId", "navigate", "exam", "setExam", "attemptId", "setAttemptId", "answers", "setAnswers", "currentQuestion", "setCurrentQuestion", "timeLeft", "setTimeLeft", "loading", "setLoading", "error", "setError", "submitting", "setSubmitting", "token", "localStorage", "getItem", "startExam", "timer", "setTimeout", "clearTimeout", "handleSubmit", "console", "log", "response", "post", "data", "attempt", "duration", "initialAnswers", "questions", "for<PERSON>ach", "question", "_id", "selectedOptions", "answer", "_error$response", "_error$response$data", "message", "handleAnswerChange", "questionId", "value", "isMultiple", "prev", "includes", "filter", "opt", "answersArray", "Object", "values", "formatTime", "seconds", "mins", "Math", "floor", "secs", "toString", "padStart", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "title", "length", "text", "options", "map", "option", "index", "_answers$question$_id", "type", "name", "checked", "onChange", "e", "onClick", "max", "disabled", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/ExamTaking.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport api from '../services/api';\n\nconst ExamTaking = () => {\n  const { examId } = useParams();\n  const navigate = useNavigate();\n  const [exam, setExam] = useState(null);\n  const [attemptId, setAttemptId] = useState(null);\n  const [answers, setAnswers] = useState({});\n  const [currentQuestion, setCurrentQuestion] = useState(0);\n  const [timeLeft, setTimeLeft] = useState(0);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [submitting, setSubmitting] = useState(false);\n\n  useEffect(() => {\n    const token = localStorage.getItem('token');\n    if (!token) {\n      navigate('/login');\n      return;\n    }\n\n    startExam();\n  }, [examId, navigate]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  useEffect(() => {\n    if (timeLeft > 0) {\n      const timer = setTimeout(() => setTimeLeft(timeLeft - 1), 1000);\n      return () => clearTimeout(timer);\n    } else if (timeLeft === 0 && exam) {\n      handleSubmit();\n    }\n  }, [timeLeft, exam]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  const startExam = async () => {\n    try {\n      console.log('Starting exam with ID:', examId);\n      const response = await api.post(`/exams/${examId}/start`);\n      console.log('Start exam response:', response.data);\n\n      setExam(response.data.exam);\n      setAttemptId(response.data.attempt);\n      setTimeLeft(response.data.exam.duration * 60); // Convert minutes to seconds\n\n      // Initialize answers\n      const initialAnswers = {};\n      response.data.exam.questions.forEach(question => {\n        initialAnswers[question._id] = {\n          question: question._id,\n          selectedOptions: [],\n          answer: ''\n        };\n      });\n      setAnswers(initialAnswers);\n      console.log('Exam started successfully');\n    } catch (error) {\n      console.error('Error starting exam:', error);\n      setError(error.response?.data?.message || 'Failed to start exam');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleAnswerChange = (questionId, value, isMultiple = false) => {\n    setAnswers(prev => ({\n      ...prev,\n      [questionId]: {\n        ...prev[questionId],\n        selectedOptions: isMultiple \n          ? (prev[questionId].selectedOptions.includes(value)\n              ? prev[questionId].selectedOptions.filter(opt => opt !== value)\n              : [...prev[questionId].selectedOptions, value])\n          : [value]\n      }\n    }));\n  };\n\n  const handleSubmit = async () => {\n    if (submitting) return;\n    \n    setSubmitting(true);\n    try {\n      const answersArray = Object.values(answers);\n      await api.post(`/exam-attempts/${attemptId}/submit`, { answers: answersArray });\n      navigate(`/results/${attemptId}`);\n    } catch (error) {\n      setError('Failed to submit exam');\n      setSubmitting(false);\n    }\n  };\n\n  const formatTime = (seconds) => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins}:${secs.toString().padStart(2, '0')}`;\n  };\n\n  if (loading) return <div className=\"loading\">Starting exam...</div>;\n  if (error) return <div className=\"error\">{error}</div>;\n  if (!exam) return <div className=\"error\">Exam not found</div>;\n\n  const question = exam.questions[currentQuestion];\n\n  return (\n    <div className=\"container\">\n      <div className=\"timer\">\n        Time Left: {formatTime(timeLeft)}\n      </div>\n      \n      <h2>{exam.title}</h2>\n      <p>Question {currentQuestion + 1} of {exam.questions.length}</p>\n      \n      <div className=\"question-container\">\n        <div className=\"question-text\">{question.text}</div>\n        \n        <div className=\"options\">\n          {question.options.map((option, index) => (\n            <div key={index} className=\"option\">\n              <label>\n                <input\n                  type={question.type === 'MULTI_ANSWER' ? 'checkbox' : 'radio'}\n                  name={`question_${question._id}`}\n                  value={option.text}\n                  checked={answers[question._id]?.selectedOptions.includes(option.text)}\n                  onChange={(e) => handleAnswerChange(\n                    question._id, \n                    option.text, \n                    question.type === 'MULTI_ANSWER'\n                  )}\n                />\n                {option.text}\n              </label>\n            </div>\n          ))}\n        </div>\n      </div>\n      \n      <div className=\"navigation\">\n        <button \n          className=\"btn btn-secondary\" \n          onClick={() => setCurrentQuestion(Math.max(0, currentQuestion - 1))}\n          disabled={currentQuestion === 0}\n        >\n          Previous\n        </button>\n        \n        {currentQuestion < exam.questions.length - 1 ? (\n          <button \n            className=\"btn\" \n            onClick={() => setCurrentQuestion(currentQuestion + 1)}\n          >\n            Next\n          </button>\n        ) : (\n          <button \n            className=\"btn\" \n            onClick={handleSubmit}\n            disabled={submitting}\n          >\n            {submitting ? 'Submitting...' : 'Submit Exam'}\n          </button>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default ExamTaking;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,OAAOC,GAAG,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvB,MAAM;IAAEC;EAAO,CAAC,GAAGP,SAAS,CAAC,CAAC;EAC9B,MAAMQ,QAAQ,GAAGP,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACQ,IAAI,EAAEC,OAAO,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACa,SAAS,EAAEC,YAAY,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACe,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1C,MAAM,CAACiB,eAAe,EAAEC,kBAAkB,CAAC,GAAGlB,QAAQ,CAAC,CAAC,CAAC;EACzD,MAAM,CAACmB,QAAQ,EAAEC,WAAW,CAAC,GAAGpB,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAACqB,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACuB,KAAK,EAAEC,QAAQ,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACyB,UAAU,EAAEC,aAAa,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAEnDC,SAAS,CAAC,MAAM;IACd,MAAM0B,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAI,CAACF,KAAK,EAAE;MACVjB,QAAQ,CAAC,QAAQ,CAAC;MAClB;IACF;IAEAoB,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAACrB,MAAM,EAAEC,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAExBT,SAAS,CAAC,MAAM;IACd,IAAIkB,QAAQ,GAAG,CAAC,EAAE;MAChB,MAAMY,KAAK,GAAGC,UAAU,CAAC,MAAMZ,WAAW,CAACD,QAAQ,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC;MAC/D,OAAO,MAAMc,YAAY,CAACF,KAAK,CAAC;IAClC,CAAC,MAAM,IAAIZ,QAAQ,KAAK,CAAC,IAAIR,IAAI,EAAE;MACjCuB,YAAY,CAAC,CAAC;IAChB;EACF,CAAC,EAAE,CAACf,QAAQ,EAAER,IAAI,CAAC,CAAC,CAAC,CAAC;;EAEtB,MAAMmB,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI;MACFK,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE3B,MAAM,CAAC;MAC7C,MAAM4B,QAAQ,GAAG,MAAMjC,GAAG,CAACkC,IAAI,CAAC,UAAU7B,MAAM,QAAQ,CAAC;MACzD0B,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEC,QAAQ,CAACE,IAAI,CAAC;MAElD3B,OAAO,CAACyB,QAAQ,CAACE,IAAI,CAAC5B,IAAI,CAAC;MAC3BG,YAAY,CAACuB,QAAQ,CAACE,IAAI,CAACC,OAAO,CAAC;MACnCpB,WAAW,CAACiB,QAAQ,CAACE,IAAI,CAAC5B,IAAI,CAAC8B,QAAQ,GAAG,EAAE,CAAC,CAAC,CAAC;;MAE/C;MACA,MAAMC,cAAc,GAAG,CAAC,CAAC;MACzBL,QAAQ,CAACE,IAAI,CAAC5B,IAAI,CAACgC,SAAS,CAACC,OAAO,CAACC,QAAQ,IAAI;QAC/CH,cAAc,CAACG,QAAQ,CAACC,GAAG,CAAC,GAAG;UAC7BD,QAAQ,EAAEA,QAAQ,CAACC,GAAG;UACtBC,eAAe,EAAE,EAAE;UACnBC,MAAM,EAAE;QACV,CAAC;MACH,CAAC,CAAC;MACFhC,UAAU,CAAC0B,cAAc,CAAC;MAC1BP,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;IAC1C,CAAC,CAAC,OAAOb,KAAK,EAAE;MAAA,IAAA0B,eAAA,EAAAC,oBAAA;MACdf,OAAO,CAACZ,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5CC,QAAQ,CAAC,EAAAyB,eAAA,GAAA1B,KAAK,CAACc,QAAQ,cAAAY,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBV,IAAI,cAAAW,oBAAA,uBAApBA,oBAAA,CAAsBC,OAAO,KAAI,sBAAsB,CAAC;IACnE,CAAC,SAAS;MACR7B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM8B,kBAAkB,GAAGA,CAACC,UAAU,EAAEC,KAAK,EAAEC,UAAU,GAAG,KAAK,KAAK;IACpEvC,UAAU,CAACwC,IAAI,KAAK;MAClB,GAAGA,IAAI;MACP,CAACH,UAAU,GAAG;QACZ,GAAGG,IAAI,CAACH,UAAU,CAAC;QACnBN,eAAe,EAAEQ,UAAU,GACtBC,IAAI,CAACH,UAAU,CAAC,CAACN,eAAe,CAACU,QAAQ,CAACH,KAAK,CAAC,GAC7CE,IAAI,CAACH,UAAU,CAAC,CAACN,eAAe,CAACW,MAAM,CAACC,GAAG,IAAIA,GAAG,KAAKL,KAAK,CAAC,GAC7D,CAAC,GAAGE,IAAI,CAACH,UAAU,CAAC,CAACN,eAAe,EAAEO,KAAK,CAAC,GAChD,CAACA,KAAK;MACZ;IACF,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMpB,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAIT,UAAU,EAAE;IAEhBC,aAAa,CAAC,IAAI,CAAC;IACnB,IAAI;MACF,MAAMkC,YAAY,GAAGC,MAAM,CAACC,MAAM,CAAC/C,OAAO,CAAC;MAC3C,MAAMX,GAAG,CAACkC,IAAI,CAAC,kBAAkBzB,SAAS,SAAS,EAAE;QAAEE,OAAO,EAAE6C;MAAa,CAAC,CAAC;MAC/ElD,QAAQ,CAAC,YAAYG,SAAS,EAAE,CAAC;IACnC,CAAC,CAAC,OAAOU,KAAK,EAAE;MACdC,QAAQ,CAAC,uBAAuB,CAAC;MACjCE,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAMqC,UAAU,GAAIC,OAAO,IAAK;IAC9B,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;IACrC,MAAMI,IAAI,GAAGJ,OAAO,GAAG,EAAE;IACzB,OAAO,GAAGC,IAAI,IAAIG,IAAI,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EACtD,CAAC;EAED,IAAIjD,OAAO,EAAE,oBAAOf,OAAA;IAAKiE,SAAS,EAAC,SAAS;IAAAC,QAAA,EAAC;EAAgB;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;EACnE,IAAIrD,KAAK,EAAE,oBAAOjB,OAAA;IAAKiE,SAAS,EAAC,OAAO;IAAAC,QAAA,EAAEjD;EAAK;IAAAkD,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EACtD,IAAI,CAACjE,IAAI,EAAE,oBAAOL,OAAA;IAAKiE,SAAS,EAAC,OAAO;IAAAC,QAAA,EAAC;EAAc;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;EAE7D,MAAM/B,QAAQ,GAAGlC,IAAI,CAACgC,SAAS,CAAC1B,eAAe,CAAC;EAEhD,oBACEX,OAAA;IAAKiE,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxBlE,OAAA;MAAKiE,SAAS,EAAC,OAAO;MAAAC,QAAA,GAAC,aACV,EAACT,UAAU,CAAC5C,QAAQ,CAAC;IAAA;MAAAsD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7B,CAAC,eAENtE,OAAA;MAAAkE,QAAA,EAAK7D,IAAI,CAACkE;IAAK;MAAAJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eACrBtE,OAAA;MAAAkE,QAAA,GAAG,WAAS,EAACvD,eAAe,GAAG,CAAC,EAAC,MAAI,EAACN,IAAI,CAACgC,SAAS,CAACmC,MAAM;IAAA;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAEhEtE,OAAA;MAAKiE,SAAS,EAAC,oBAAoB;MAAAC,QAAA,gBACjClE,OAAA;QAAKiE,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAE3B,QAAQ,CAACkC;MAAI;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAEpDtE,OAAA;QAAKiE,SAAS,EAAC,SAAS;QAAAC,QAAA,EACrB3B,QAAQ,CAACmC,OAAO,CAACC,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK;UAAA,IAAAC,qBAAA;UAAA,oBAClC9E,OAAA;YAAiBiE,SAAS,EAAC,QAAQ;YAAAC,QAAA,eACjClE,OAAA;cAAAkE,QAAA,gBACElE,OAAA;gBACE+E,IAAI,EAAExC,QAAQ,CAACwC,IAAI,KAAK,cAAc,GAAG,UAAU,GAAG,OAAQ;gBAC9DC,IAAI,EAAE,YAAYzC,QAAQ,CAACC,GAAG,EAAG;gBACjCQ,KAAK,EAAE4B,MAAM,CAACH,IAAK;gBACnBQ,OAAO,GAAAH,qBAAA,GAAErE,OAAO,CAAC8B,QAAQ,CAACC,GAAG,CAAC,cAAAsC,qBAAA,uBAArBA,qBAAA,CAAuBrC,eAAe,CAACU,QAAQ,CAACyB,MAAM,CAACH,IAAI,CAAE;gBACtES,QAAQ,EAAGC,CAAC,IAAKrC,kBAAkB,CACjCP,QAAQ,CAACC,GAAG,EACZoC,MAAM,CAACH,IAAI,EACXlC,QAAQ,CAACwC,IAAI,KAAK,cACpB;cAAE;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EACDM,MAAM,CAACH,IAAI;YAAA;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP;UAAC,GAdAO,KAAK;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAeV,CAAC;QAAA,CACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENtE,OAAA;MAAKiE,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACzBlE,OAAA;QACEiE,SAAS,EAAC,mBAAmB;QAC7BmB,OAAO,EAAEA,CAAA,KAAMxE,kBAAkB,CAACgD,IAAI,CAACyB,GAAG,CAAC,CAAC,EAAE1E,eAAe,GAAG,CAAC,CAAC,CAAE;QACpE2E,QAAQ,EAAE3E,eAAe,KAAK,CAAE;QAAAuD,QAAA,EACjC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,EAER3D,eAAe,GAAGN,IAAI,CAACgC,SAAS,CAACmC,MAAM,GAAG,CAAC,gBAC1CxE,OAAA;QACEiE,SAAS,EAAC,KAAK;QACfmB,OAAO,EAAEA,CAAA,KAAMxE,kBAAkB,CAACD,eAAe,GAAG,CAAC,CAAE;QAAAuD,QAAA,EACxD;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,gBAETtE,OAAA;QACEiE,SAAS,EAAC,KAAK;QACfmB,OAAO,EAAExD,YAAa;QACtB0D,QAAQ,EAAEnE,UAAW;QAAA+C,QAAA,EAEpB/C,UAAU,GAAG,eAAe,GAAG;MAAa;QAAAgD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACpE,EAAA,CAlKID,UAAU;EAAA,QACKL,SAAS,EACXC,WAAW;AAAA;AAAA0F,EAAA,GAFxBtF,UAAU;AAoKhB,eAAeA,UAAU;AAAC,IAAAsF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}