{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/ExamTaking.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport api from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ExamTaking = () => {\n  _s();\n  var _exam$subject2, _answers$question$_id2;\n  const {\n    examId\n  } = useParams();\n  const navigate = useNavigate();\n  const [exam, setExam] = useState(null);\n  const [attemptId, setAttemptId] = useState(null);\n  const [answers, setAnswers] = useState({});\n  const [currentQuestion, setCurrentQuestion] = useState(0);\n  const [timeLeft, setTimeLeft] = useState(0);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [submitting, setSubmitting] = useState(false);\n  const [examStartTime, setExamStartTime] = useState(null);\n  const [questionTimes, setQuestionTimes] = useState({});\n  const [showInstructions, setShowInstructions] = useState(true);\n  const [isFullscreen, setIsFullscreen] = useState(false);\n  const [warningCount, setWarningCount] = useState(0);\n  const [questionStartTime, setQuestionStartTime] = useState(null);\n  useEffect(() => {\n    const token = localStorage.getItem('token');\n    if (!token) {\n      navigate('/login');\n      return;\n    }\n    startExam();\n  }, [examId, navigate]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  useEffect(() => {\n    if (timeLeft > 0) {\n      const timer = setTimeout(() => setTimeLeft(timeLeft - 1), 1000);\n      return () => clearTimeout(timer);\n    } else if (timeLeft === 0 && exam) {\n      handleSubmit();\n    }\n  }, [timeLeft, exam]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  const startExam = async () => {\n    try {\n      console.log('Starting exam with ID:', examId);\n      const response = await api.post(`/exams/${examId}/start`);\n      console.log('Start exam response:', response.data);\n      setExam(response.data.exam);\n      setAttemptId(response.data.attempt);\n      setTimeLeft(response.data.exam.duration * 60); // Convert minutes to seconds\n      setExamStartTime(Date.now());\n      setQuestionStartTime(Date.now());\n\n      // Initialize answers\n      const initialAnswers = {};\n      response.data.exam.questions.forEach(question => {\n        initialAnswers[question._id] = {\n          question: question._id,\n          selectedOptions: [],\n          answer: ''\n        };\n      });\n      setAnswers(initialAnswers);\n\n      // Initialize question times\n      const initialTimes = {};\n      response.data.exam.questions.forEach(question => {\n        initialTimes[question._id] = 0;\n      });\n      setQuestionTimes(initialTimes);\n      console.log('Exam started successfully');\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('Error starting exam:', error);\n      setError(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Failed to start exam');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleAnswerChange = (questionId, value, isMultiple = false) => {\n    setAnswers(prev => ({\n      ...prev,\n      [questionId]: {\n        ...prev[questionId],\n        selectedOptions: isMultiple ? prev[questionId].selectedOptions.includes(value) ? prev[questionId].selectedOptions.filter(opt => opt !== value) : [...prev[questionId].selectedOptions, value] : [value]\n      }\n    }));\n  };\n  const handleSubmit = async () => {\n    if (submitting) return;\n    setSubmitting(true);\n    try {\n      const answersArray = Object.values(answers);\n      await api.post(`/exam-attempts/${attemptId}/submit`, {\n        answers: answersArray\n      });\n      navigate(`/results/${attemptId}`);\n    } catch (error) {\n      setError('Failed to submit exam');\n      setSubmitting(false);\n    }\n  };\n  const formatTime = seconds => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins}:${secs.toString().padStart(2, '0')}`;\n  };\n  const trackQuestionTime = questionId => {\n    if (questionStartTime && questionId) {\n      const timeSpent = Math.floor((Date.now() - questionStartTime) / 1000);\n      setQuestionTimes(prev => ({\n        ...prev,\n        [questionId]: (prev[questionId] || 0) + timeSpent\n      }));\n    }\n  };\n  const navigateToQuestion = questionIndex => {\n    if (exam && exam.questions[currentQuestion]) {\n      trackQuestionTime(exam.questions[currentQuestion]._id);\n    }\n    setCurrentQuestion(questionIndex);\n    setQuestionStartTime(Date.now());\n  };\n  const toggleFullscreen = () => {\n    if (!isFullscreen) {\n      var _document$documentEle, _document$documentEle2;\n      (_document$documentEle = (_document$documentEle2 = document.documentElement).requestFullscreen) === null || _document$documentEle === void 0 ? void 0 : _document$documentEle.call(_document$documentEle2);\n      setIsFullscreen(true);\n    } else {\n      var _document$exitFullscr, _document;\n      (_document$exitFullscr = (_document = document).exitFullscreen) === null || _document$exitFullscr === void 0 ? void 0 : _document$exitFullscr.call(_document);\n      setIsFullscreen(false);\n    }\n  };\n  const handleTabSwitch = () => {\n    setWarningCount(prev => prev + 1);\n    if (warningCount >= 2) {\n      alert('Warning: Multiple tab switches detected. This exam will be auto-submitted for security.');\n      handleSubmit();\n    } else {\n      alert(`Warning ${warningCount + 1}/3: Please stay on this tab during the exam.`);\n    }\n  };\n\n  // Add event listeners for tab switching detection\n  useEffect(() => {\n    const handleVisibilityChange = () => {\n      if (document.hidden && exam && !submitting) {\n        handleTabSwitch();\n      }\n    };\n    document.addEventListener('visibilitychange', handleVisibilityChange);\n    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);\n  }, [exam, submitting, warningCount]);\n  const startExamProper = () => {\n    setShowInstructions(false);\n    setQuestionStartTime(Date.now());\n  };\n  if (loading) return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"loading\",\n    children: \"Starting exam...\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 170,\n    columnNumber: 23\n  }, this);\n  if (error) return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"error\",\n    children: error\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 171,\n    columnNumber: 21\n  }, this);\n  if (!exam) return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"error\",\n    children: \"Exam not found\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 172,\n    columnNumber: 21\n  }, this);\n\n  // Show instructions screen first\n  if (showInstructions) {\n    var _exam$subject;\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"exam-instructions-container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"instructions-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"\\uD83D\\uDCCB Exam Instructions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"exam-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: exam.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"exam-details\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"detail-icon\",\n                children: \"\\uD83D\\uDCDA\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Subject: \", (_exam$subject = exam.subject) === null || _exam$subject === void 0 ? void 0 : _exam$subject.name]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 186,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"detail-icon\",\n                children: \"\\u2753\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Questions: \", exam.questions.length]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"detail-icon\",\n                children: \"\\u23F0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Duration: \", exam.duration, \" minutes\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 194,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"detail-icon\",\n                children: \"\\uD83D\\uDCCA\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Total Points: \", exam.questions.reduce((sum, q) => sum + (q.points || 1), 0)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"general-instructions\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\uD83D\\uDCDD General Instructions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Read each question carefully before answering\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"You can navigate between questions using the question numbers\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Your progress is automatically saved\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Make sure you have a stable internet connection\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Do not refresh the page or close the browser\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Avoid switching tabs or applications during the exam\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Submit your exam before the time runs out\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 11\n        }, this), exam.instructions && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"specific-instructions\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\uD83D\\uDCCB Specific Instructions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: exam.instructions\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"exam-controls\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: toggleFullscreen,\n            className: \"btn btn-secondary\",\n            children: \"\\uD83D\\uDDA5\\uFE0F Enter Fullscreen\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: startExamProper,\n            className: \"btn btn-success btn-large\",\n            children: \"\\uD83D\\uDE80 Start Exam\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"warning-note\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"\\u26A0\\uFE0F Once you start the exam, the timer will begin and cannot be paused.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 177,\n      columnNumber: 7\n    }, this);\n  }\n  const question = exam.questions[currentQuestion];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `exam-taking-container ${isFullscreen ? 'fullscreen' : ''}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"exam-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"exam-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: exam.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [\"Question \", currentQuestion + 1, \" of \", exam.questions.length]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 246,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"exam-controls\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"timer\",\n          children: [\"\\u23F0 \", formatTime(timeLeft)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: toggleFullscreen,\n          className: \"btn btn-sm\",\n          children: isFullscreen ? '🗗' : '🖥️'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 251,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 245,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"exam-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: exam.title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 263,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Subject:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 12\n        }, this), \" \", (_exam$subject2 = exam.subject) === null || _exam$subject2 === void 0 ? void 0 : _exam$subject2.name]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 264,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [\"Question \", currentQuestion + 1, \" of \", exam.questions.length]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 265,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 262,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"question-nav\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"nav-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"\\uD83D\\uDCDD Questions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"progress-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"answered-count\",\n            children: [Object.values(answers).filter(a => a.selectedOptions.length > 0).length, \" answered\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"total-count\",\n            children: [\"of \", exam.questions.length]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 270,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"nav-grid\",\n        children: exam.questions.map((_, index) => {\n          var _answers$exam$questio;\n          const isAnswered = ((_answers$exam$questio = answers[exam.questions[index]._id]) === null || _answers$exam$questio === void 0 ? void 0 : _answers$exam$questio.selectedOptions.length) > 0;\n          const timeSpent = questionTimes[exam.questions[index]._id] || 0;\n          return /*#__PURE__*/_jsxDEV(\"button\", {\n            className: `nav-btn ${index === currentQuestion ? 'active' : ''} ${isAnswered ? 'answered' : ''}`,\n            onClick: () => navigateToQuestion(index),\n            title: `Question ${index + 1}${isAnswered ? ' (Answered)' : ''} - Time: ${Math.floor(timeSpent / 60)}:${(timeSpent % 60).toString().padStart(2, '0')}`,\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"question-number\",\n              children: index + 1\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 17\n            }, this), isAnswered && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"answered-indicator\",\n              children: \"\\u2713\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 32\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"time-indicator\",\n              children: [Math.floor(timeSpent / 60), \"m\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 15\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"nav-legend\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"legend-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"legend-color current\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Current\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 303,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"legend-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"legend-color answered\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 308,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Answered\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 309,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 307,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"legend-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"legend-color unanswered\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 312,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Unanswered\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 313,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 311,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 302,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 269,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"question-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"question-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: [\"Question \", currentQuestion + 1]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 321,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"question-type\",\n          children: question.type === 'MCQ' ? 'Multiple Choice' : question.type === 'TRUE_FALSE' ? 'True/False' : 'Multiple Answer'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 322,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 320,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"question-text\",\n        children: question.text\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 329,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"options\",\n        children: question.options.map((option, index) => {\n          var _answers$question$_id;\n          const isSelected = (_answers$question$_id = answers[question._id]) === null || _answers$question$_id === void 0 ? void 0 : _answers$question$_id.selectedOptions.includes(option.text);\n          const optionLetter = String.fromCharCode(65 + index);\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `option ${isSelected ? 'selected' : ''}`,\n            children: /*#__PURE__*/_jsxDEV(\"label\", {\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: question.type === 'MULTI_ANSWER' ? 'checkbox' : 'radio',\n                name: `question_${question._id}`,\n                value: option.text,\n                checked: isSelected,\n                onChange: e => handleAnswerChange(question._id, option.text, question.type === 'MULTI_ANSWER')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 339,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"option-letter\",\n                children: [optionLetter, \".\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 350,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"option-text\",\n                children: option.text\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 351,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 338,\n              columnNumber: 17\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 337,\n            columnNumber: 15\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 331,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"answer-status\",\n        children: ((_answers$question$_id2 = answers[question._id]) === null || _answers$question$_id2 === void 0 ? void 0 : _answers$question$_id2.selectedOptions.length) > 0 ? /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"answered\",\n          children: \"\\u2713 Answered\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 361,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"unanswered\",\n          children: \"\\u26A0 Not answered\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 363,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 359,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 319,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"navigation\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn btn-secondary\",\n        onClick: () => setCurrentQuestion(Math.max(0, currentQuestion - 1)),\n        disabled: currentQuestion === 0,\n        children: \"\\u2190 Previous\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 370,\n        columnNumber: 9\n      }, this), currentQuestion < exam.questions.length - 1 ? /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn\",\n        onClick: () => setCurrentQuestion(currentQuestion + 1),\n        children: \"Next \\u2192\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 379,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn btn-success\",\n        onClick: handleSubmit,\n        disabled: submitting,\n        children: submitting ? 'Submitting...' : 'Submit Exam'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 386,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 369,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"exam-summary\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        children: \"Exam Progress\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 398,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [\"Answered: \", Object.values(answers).filter(a => a.selectedOptions.length > 0).length, \" / \", exam.questions.length]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 399,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"progress-bar\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"progress\",\n          style: {\n            width: `${Object.values(answers).filter(a => a.selectedOptions.length > 0).length / exam.questions.length * 100}%`\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 403,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 402,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 397,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 243,\n    columnNumber: 5\n  }, this);\n};\n_s(ExamTaking, \"mn9lSx9B9gFKUXu2XCdVINe+1Cg=\", false, function () {\n  return [useParams, useNavigate];\n});\n_c = ExamTaking;\nexport default ExamTaking;\nvar _c;\n$RefreshReg$(_c, \"ExamTaking\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useNavigate", "api", "jsxDEV", "_jsxDEV", "ExamTaking", "_s", "_exam$subject2", "_answers$question$_id2", "examId", "navigate", "exam", "setExam", "attemptId", "setAttemptId", "answers", "setAnswers", "currentQuestion", "setCurrentQuestion", "timeLeft", "setTimeLeft", "loading", "setLoading", "error", "setError", "submitting", "setSubmitting", "examStartTime", "setExamStartTime", "questionTimes", "setQuestionTimes", "showInstructions", "setShowInstructions", "isFullscreen", "setIsFullscreen", "warningCount", "setWarningCount", "questionStartTime", "setQuestionStartTime", "token", "localStorage", "getItem", "startExam", "timer", "setTimeout", "clearTimeout", "handleSubmit", "console", "log", "response", "post", "data", "attempt", "duration", "Date", "now", "initialAnswers", "questions", "for<PERSON>ach", "question", "_id", "selectedOptions", "answer", "initialTimes", "_error$response", "_error$response$data", "message", "handleAnswerChange", "questionId", "value", "isMultiple", "prev", "includes", "filter", "opt", "answersArray", "Object", "values", "formatTime", "seconds", "mins", "Math", "floor", "secs", "toString", "padStart", "trackQuestionTime", "timeSpent", "navigateToQuestion", "questionIndex", "toggleFullscreen", "_document$documentEle", "_document$documentEle2", "document", "documentElement", "requestFullscreen", "call", "_document$exitFullscr", "_document", "exitFullscreen", "handleTabSwitch", "alert", "handleVisibilityChange", "hidden", "addEventListener", "removeEventListener", "startExamProper", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_exam$subject", "title", "subject", "name", "length", "reduce", "sum", "q", "points", "instructions", "onClick", "a", "map", "_", "index", "_answers$exam$questio", "isAnswered", "type", "text", "options", "option", "_answers$question$_id", "isSelected", "optionLetter", "String", "fromCharCode", "checked", "onChange", "e", "max", "disabled", "style", "width", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/ExamTaking.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport api from '../services/api';\n\nconst ExamTaking = () => {\n  const { examId } = useParams();\n  const navigate = useNavigate();\n  const [exam, setExam] = useState(null);\n  const [attemptId, setAttemptId] = useState(null);\n  const [answers, setAnswers] = useState({});\n  const [currentQuestion, setCurrentQuestion] = useState(0);\n  const [timeLeft, setTimeLeft] = useState(0);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [submitting, setSubmitting] = useState(false);\n  const [examStartTime, setExamStartTime] = useState(null);\n  const [questionTimes, setQuestionTimes] = useState({});\n  const [showInstructions, setShowInstructions] = useState(true);\n  const [isFullscreen, setIsFullscreen] = useState(false);\n  const [warningCount, setWarningCount] = useState(0);\n  const [questionStartTime, setQuestionStartTime] = useState(null);\n\n  useEffect(() => {\n    const token = localStorage.getItem('token');\n    if (!token) {\n      navigate('/login');\n      return;\n    }\n\n    startExam();\n  }, [examId, navigate]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  useEffect(() => {\n    if (timeLeft > 0) {\n      const timer = setTimeout(() => setTimeLeft(timeLeft - 1), 1000);\n      return () => clearTimeout(timer);\n    } else if (timeLeft === 0 && exam) {\n      handleSubmit();\n    }\n  }, [timeLeft, exam]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  const startExam = async () => {\n    try {\n      console.log('Starting exam with ID:', examId);\n      const response = await api.post(`/exams/${examId}/start`);\n      console.log('Start exam response:', response.data);\n\n      setExam(response.data.exam);\n      setAttemptId(response.data.attempt);\n      setTimeLeft(response.data.exam.duration * 60); // Convert minutes to seconds\n      setExamStartTime(Date.now());\n      setQuestionStartTime(Date.now());\n\n      // Initialize answers\n      const initialAnswers = {};\n      response.data.exam.questions.forEach(question => {\n        initialAnswers[question._id] = {\n          question: question._id,\n          selectedOptions: [],\n          answer: ''\n        };\n      });\n      setAnswers(initialAnswers);\n\n      // Initialize question times\n      const initialTimes = {};\n      response.data.exam.questions.forEach(question => {\n        initialTimes[question._id] = 0;\n      });\n      setQuestionTimes(initialTimes);\n\n      console.log('Exam started successfully');\n    } catch (error) {\n      console.error('Error starting exam:', error);\n      setError(error.response?.data?.message || 'Failed to start exam');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleAnswerChange = (questionId, value, isMultiple = false) => {\n    setAnswers(prev => ({\n      ...prev,\n      [questionId]: {\n        ...prev[questionId],\n        selectedOptions: isMultiple \n          ? (prev[questionId].selectedOptions.includes(value)\n              ? prev[questionId].selectedOptions.filter(opt => opt !== value)\n              : [...prev[questionId].selectedOptions, value])\n          : [value]\n      }\n    }));\n  };\n\n  const handleSubmit = async () => {\n    if (submitting) return;\n    \n    setSubmitting(true);\n    try {\n      const answersArray = Object.values(answers);\n      await api.post(`/exam-attempts/${attemptId}/submit`, { answers: answersArray });\n      navigate(`/results/${attemptId}`);\n    } catch (error) {\n      setError('Failed to submit exam');\n      setSubmitting(false);\n    }\n  };\n\n  const formatTime = (seconds) => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins}:${secs.toString().padStart(2, '0')}`;\n  };\n\n  const trackQuestionTime = (questionId) => {\n    if (questionStartTime && questionId) {\n      const timeSpent = Math.floor((Date.now() - questionStartTime) / 1000);\n      setQuestionTimes(prev => ({\n        ...prev,\n        [questionId]: (prev[questionId] || 0) + timeSpent\n      }));\n    }\n  };\n\n  const navigateToQuestion = (questionIndex) => {\n    if (exam && exam.questions[currentQuestion]) {\n      trackQuestionTime(exam.questions[currentQuestion]._id);\n    }\n    setCurrentQuestion(questionIndex);\n    setQuestionStartTime(Date.now());\n  };\n\n  const toggleFullscreen = () => {\n    if (!isFullscreen) {\n      document.documentElement.requestFullscreen?.();\n      setIsFullscreen(true);\n    } else {\n      document.exitFullscreen?.();\n      setIsFullscreen(false);\n    }\n  };\n\n  const handleTabSwitch = () => {\n    setWarningCount(prev => prev + 1);\n    if (warningCount >= 2) {\n      alert('Warning: Multiple tab switches detected. This exam will be auto-submitted for security.');\n      handleSubmit();\n    } else {\n      alert(`Warning ${warningCount + 1}/3: Please stay on this tab during the exam.`);\n    }\n  };\n\n  // Add event listeners for tab switching detection\n  useEffect(() => {\n    const handleVisibilityChange = () => {\n      if (document.hidden && exam && !submitting) {\n        handleTabSwitch();\n      }\n    };\n\n    document.addEventListener('visibilitychange', handleVisibilityChange);\n    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);\n  }, [exam, submitting, warningCount]);\n\n  const startExamProper = () => {\n    setShowInstructions(false);\n    setQuestionStartTime(Date.now());\n  };\n\n  if (loading) return <div className=\"loading\">Starting exam...</div>;\n  if (error) return <div className=\"error\">{error}</div>;\n  if (!exam) return <div className=\"error\">Exam not found</div>;\n\n  // Show instructions screen first\n  if (showInstructions) {\n    return (\n      <div className=\"exam-instructions-container\">\n        <div className=\"instructions-card\">\n          <h1>📋 Exam Instructions</h1>\n\n          <div className=\"exam-info\">\n            <h2>{exam.title}</h2>\n            <div className=\"exam-details\">\n              <div className=\"detail-item\">\n                <span className=\"detail-icon\">📚</span>\n                <span>Subject: {exam.subject?.name}</span>\n              </div>\n              <div className=\"detail-item\">\n                <span className=\"detail-icon\">❓</span>\n                <span>Questions: {exam.questions.length}</span>\n              </div>\n              <div className=\"detail-item\">\n                <span className=\"detail-icon\">⏰</span>\n                <span>Duration: {exam.duration} minutes</span>\n              </div>\n              <div className=\"detail-item\">\n                <span className=\"detail-icon\">📊</span>\n                <span>Total Points: {exam.questions.reduce((sum, q) => sum + (q.points || 1), 0)}</span>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"general-instructions\">\n            <h3>📝 General Instructions</h3>\n            <ul>\n              <li>Read each question carefully before answering</li>\n              <li>You can navigate between questions using the question numbers</li>\n              <li>Your progress is automatically saved</li>\n              <li>Make sure you have a stable internet connection</li>\n              <li>Do not refresh the page or close the browser</li>\n              <li>Avoid switching tabs or applications during the exam</li>\n              <li>Submit your exam before the time runs out</li>\n            </ul>\n          </div>\n\n          {exam.instructions && (\n            <div className=\"specific-instructions\">\n              <h3>📋 Specific Instructions</h3>\n              <p>{exam.instructions}</p>\n            </div>\n          )}\n\n          <div className=\"exam-controls\">\n            <button onClick={toggleFullscreen} className=\"btn btn-secondary\">\n              🖥️ Enter Fullscreen\n            </button>\n            <button onClick={startExamProper} className=\"btn btn-success btn-large\">\n              🚀 Start Exam\n            </button>\n          </div>\n\n          <div className=\"warning-note\">\n            <p>⚠️ Once you start the exam, the timer will begin and cannot be paused.</p>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  const question = exam.questions[currentQuestion];\n\n  return (\n    <div className={`exam-taking-container ${isFullscreen ? 'fullscreen' : ''}`}>\n      {/* Exam Header */}\n      <div className=\"exam-header\">\n        <div className=\"exam-info\">\n          <h2>{exam.title}</h2>\n          <p>Question {currentQuestion + 1} of {exam.questions.length}</p>\n        </div>\n\n        <div className=\"exam-controls\">\n          <div className=\"timer\">\n            ⏰ {formatTime(timeLeft)}\n          </div>\n          <button onClick={toggleFullscreen} className=\"btn btn-sm\">\n            {isFullscreen ? '🗗' : '🖥️'}\n          </button>\n        </div>\n      </div>\n\n      {/* Exam Header */}\n      <div className=\"exam-header\">\n        <h2>{exam.title}</h2>\n        <p><strong>Subject:</strong> {exam.subject?.name}</p>\n        <p>Question {currentQuestion + 1} of {exam.questions.length}</p>\n      </div>\n\n      {/* Enhanced Question Navigation */}\n      <div className=\"question-nav\">\n        <div className=\"nav-header\">\n          <h3>📝 Questions</h3>\n          <div className=\"progress-info\">\n            <span className=\"answered-count\">\n              {Object.values(answers).filter(a => a.selectedOptions.length > 0).length} answered\n            </span>\n            <span className=\"total-count\">of {exam.questions.length}</span>\n          </div>\n        </div>\n\n        <div className=\"nav-grid\">\n          {exam.questions.map((_, index) => {\n            const isAnswered = answers[exam.questions[index]._id]?.selectedOptions.length > 0;\n            const timeSpent = questionTimes[exam.questions[index]._id] || 0;\n\n            return (\n              <button\n                key={index}\n                className={`nav-btn ${index === currentQuestion ? 'active' : ''} ${\n                  isAnswered ? 'answered' : ''\n                }`}\n                onClick={() => navigateToQuestion(index)}\n                title={`Question ${index + 1}${isAnswered ? ' (Answered)' : ''} - Time: ${Math.floor(timeSpent / 60)}:${(timeSpent % 60).toString().padStart(2, '0')}`}\n              >\n                <span className=\"question-number\">{index + 1}</span>\n                {isAnswered && <span className=\"answered-indicator\">✓</span>}\n                <span className=\"time-indicator\">{Math.floor(timeSpent / 60)}m</span>\n              </button>\n            );\n          })}\n        </div>\n\n        <div className=\"nav-legend\">\n          <div className=\"legend-item\">\n            <div className=\"legend-color current\"></div>\n            <span>Current</span>\n          </div>\n          <div className=\"legend-item\">\n            <div className=\"legend-color answered\"></div>\n            <span>Answered</span>\n          </div>\n          <div className=\"legend-item\">\n            <div className=\"legend-color unanswered\"></div>\n            <span>Unanswered</span>\n          </div>\n        </div>\n      </div>\n\n      {/* Question */}\n      <div className=\"question-container\">\n        <div className=\"question-header\">\n          <h3>Question {currentQuestion + 1}</h3>\n          <span className=\"question-type\">\n            {question.type === 'MCQ' ? 'Multiple Choice' :\n             question.type === 'TRUE_FALSE' ? 'True/False' :\n             'Multiple Answer'}\n          </span>\n        </div>\n\n        <div className=\"question-text\">{question.text}</div>\n\n        <div className=\"options\">\n          {question.options.map((option, index) => {\n            const isSelected = answers[question._id]?.selectedOptions.includes(option.text);\n            const optionLetter = String.fromCharCode(65 + index);\n\n            return (\n              <div key={index} className={`option ${isSelected ? 'selected' : ''}`}>\n                <label>\n                  <input\n                    type={question.type === 'MULTI_ANSWER' ? 'checkbox' : 'radio'}\n                    name={`question_${question._id}`}\n                    value={option.text}\n                    checked={isSelected}\n                    onChange={(e) => handleAnswerChange(\n                      question._id,\n                      option.text,\n                      question.type === 'MULTI_ANSWER'\n                    )}\n                  />\n                  <span className=\"option-letter\">{optionLetter}.</span>\n                  <span className=\"option-text\">{option.text}</span>\n                </label>\n              </div>\n            );\n          })}\n        </div>\n\n        {/* Answer Status */}\n        <div className=\"answer-status\">\n          {answers[question._id]?.selectedOptions.length > 0 ? (\n            <span className=\"answered\">✓ Answered</span>\n          ) : (\n            <span className=\"unanswered\">⚠ Not answered</span>\n          )}\n        </div>\n      </div>\n\n      {/* Navigation */}\n      <div className=\"navigation\">\n        <button\n          className=\"btn btn-secondary\"\n          onClick={() => setCurrentQuestion(Math.max(0, currentQuestion - 1))}\n          disabled={currentQuestion === 0}\n        >\n          ← Previous\n        </button>\n\n        {currentQuestion < exam.questions.length - 1 ? (\n          <button\n            className=\"btn\"\n            onClick={() => setCurrentQuestion(currentQuestion + 1)}\n          >\n            Next →\n          </button>\n        ) : (\n          <button\n            className=\"btn btn-success\"\n            onClick={handleSubmit}\n            disabled={submitting}\n          >\n            {submitting ? 'Submitting...' : 'Submit Exam'}\n          </button>\n        )}\n      </div>\n\n      {/* Exam Summary */}\n      <div className=\"exam-summary\">\n        <h4>Exam Progress</h4>\n        <p>\n          Answered: {Object.values(answers).filter(a => a.selectedOptions.length > 0).length} / {exam.questions.length}\n        </p>\n        <div className=\"progress-bar\">\n          <div\n            className=\"progress\"\n            style={{\n              width: `${(Object.values(answers).filter(a => a.selectedOptions.length > 0).length / exam.questions.length) * 100}%`\n            }}\n          ></div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ExamTaking;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,OAAOC,GAAG,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,cAAA,EAAAC,sBAAA;EACvB,MAAM;IAAEC;EAAO,CAAC,GAAGT,SAAS,CAAC,CAAC;EAC9B,MAAMU,QAAQ,GAAGT,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACU,IAAI,EAAEC,OAAO,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACe,SAAS,EAAEC,YAAY,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1C,MAAM,CAACmB,eAAe,EAAEC,kBAAkB,CAAC,GAAGpB,QAAQ,CAAC,CAAC,CAAC;EACzD,MAAM,CAACqB,QAAQ,EAAEC,WAAW,CAAC,GAAGtB,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAACuB,OAAO,EAAEC,UAAU,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACyB,KAAK,EAAEC,QAAQ,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC2B,UAAU,EAAEC,aAAa,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC6B,aAAa,EAAEC,gBAAgB,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAAC+B,aAAa,EAAEC,gBAAgB,CAAC,GAAGhC,QAAQ,CAAC,CAAC,CAAC,CAAC;EACtD,MAAM,CAACiC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACmC,YAAY,EAAEC,eAAe,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACqC,YAAY,EAAEC,eAAe,CAAC,GAAGtC,QAAQ,CAAC,CAAC,CAAC;EACnD,MAAM,CAACuC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGxC,QAAQ,CAAC,IAAI,CAAC;EAEhEC,SAAS,CAAC,MAAM;IACd,MAAMwC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAI,CAACF,KAAK,EAAE;MACV7B,QAAQ,CAAC,QAAQ,CAAC;MAClB;IACF;IAEAgC,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAACjC,MAAM,EAAEC,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAExBX,SAAS,CAAC,MAAM;IACd,IAAIoB,QAAQ,GAAG,CAAC,EAAE;MAChB,MAAMwB,KAAK,GAAGC,UAAU,CAAC,MAAMxB,WAAW,CAACD,QAAQ,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC;MAC/D,OAAO,MAAM0B,YAAY,CAACF,KAAK,CAAC;IAClC,CAAC,MAAM,IAAIxB,QAAQ,KAAK,CAAC,IAAIR,IAAI,EAAE;MACjCmC,YAAY,CAAC,CAAC;IAChB;EACF,CAAC,EAAE,CAAC3B,QAAQ,EAAER,IAAI,CAAC,CAAC,CAAC,CAAC;;EAEtB,MAAM+B,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI;MACFK,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEvC,MAAM,CAAC;MAC7C,MAAMwC,QAAQ,GAAG,MAAM/C,GAAG,CAACgD,IAAI,CAAC,UAAUzC,MAAM,QAAQ,CAAC;MACzDsC,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEC,QAAQ,CAACE,IAAI,CAAC;MAElDvC,OAAO,CAACqC,QAAQ,CAACE,IAAI,CAACxC,IAAI,CAAC;MAC3BG,YAAY,CAACmC,QAAQ,CAACE,IAAI,CAACC,OAAO,CAAC;MACnChC,WAAW,CAAC6B,QAAQ,CAACE,IAAI,CAACxC,IAAI,CAAC0C,QAAQ,GAAG,EAAE,CAAC,CAAC,CAAC;MAC/CzB,gBAAgB,CAAC0B,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;MAC5BjB,oBAAoB,CAACgB,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;;MAEhC;MACA,MAAMC,cAAc,GAAG,CAAC,CAAC;MACzBP,QAAQ,CAACE,IAAI,CAACxC,IAAI,CAAC8C,SAAS,CAACC,OAAO,CAACC,QAAQ,IAAI;QAC/CH,cAAc,CAACG,QAAQ,CAACC,GAAG,CAAC,GAAG;UAC7BD,QAAQ,EAAEA,QAAQ,CAACC,GAAG;UACtBC,eAAe,EAAE,EAAE;UACnBC,MAAM,EAAE;QACV,CAAC;MACH,CAAC,CAAC;MACF9C,UAAU,CAACwC,cAAc,CAAC;;MAE1B;MACA,MAAMO,YAAY,GAAG,CAAC,CAAC;MACvBd,QAAQ,CAACE,IAAI,CAACxC,IAAI,CAAC8C,SAAS,CAACC,OAAO,CAACC,QAAQ,IAAI;QAC/CI,YAAY,CAACJ,QAAQ,CAACC,GAAG,CAAC,GAAG,CAAC;MAChC,CAAC,CAAC;MACF9B,gBAAgB,CAACiC,YAAY,CAAC;MAE9BhB,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;IAC1C,CAAC,CAAC,OAAOzB,KAAK,EAAE;MAAA,IAAAyC,eAAA,EAAAC,oBAAA;MACdlB,OAAO,CAACxB,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5CC,QAAQ,CAAC,EAAAwC,eAAA,GAAAzC,KAAK,CAAC0B,QAAQ,cAAAe,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBb,IAAI,cAAAc,oBAAA,uBAApBA,oBAAA,CAAsBC,OAAO,KAAI,sBAAsB,CAAC;IACnE,CAAC,SAAS;MACR5C,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM6C,kBAAkB,GAAGA,CAACC,UAAU,EAAEC,KAAK,EAAEC,UAAU,GAAG,KAAK,KAAK;IACpEtD,UAAU,CAACuD,IAAI,KAAK;MAClB,GAAGA,IAAI;MACP,CAACH,UAAU,GAAG;QACZ,GAAGG,IAAI,CAACH,UAAU,CAAC;QACnBP,eAAe,EAAES,UAAU,GACtBC,IAAI,CAACH,UAAU,CAAC,CAACP,eAAe,CAACW,QAAQ,CAACH,KAAK,CAAC,GAC7CE,IAAI,CAACH,UAAU,CAAC,CAACP,eAAe,CAACY,MAAM,CAACC,GAAG,IAAIA,GAAG,KAAKL,KAAK,CAAC,GAC7D,CAAC,GAAGE,IAAI,CAACH,UAAU,CAAC,CAACP,eAAe,EAAEQ,KAAK,CAAC,GAChD,CAACA,KAAK;MACZ;IACF,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMvB,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAIrB,UAAU,EAAE;IAEhBC,aAAa,CAAC,IAAI,CAAC;IACnB,IAAI;MACF,MAAMiD,YAAY,GAAGC,MAAM,CAACC,MAAM,CAAC9D,OAAO,CAAC;MAC3C,MAAMb,GAAG,CAACgD,IAAI,CAAC,kBAAkBrC,SAAS,SAAS,EAAE;QAAEE,OAAO,EAAE4D;MAAa,CAAC,CAAC;MAC/EjE,QAAQ,CAAC,YAAYG,SAAS,EAAE,CAAC;IACnC,CAAC,CAAC,OAAOU,KAAK,EAAE;MACdC,QAAQ,CAAC,uBAAuB,CAAC;MACjCE,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAMoD,UAAU,GAAIC,OAAO,IAAK;IAC9B,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;IACrC,MAAMI,IAAI,GAAGJ,OAAO,GAAG,EAAE;IACzB,OAAO,GAAGC,IAAI,IAAIG,IAAI,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EACtD,CAAC;EAED,MAAMC,iBAAiB,GAAIlB,UAAU,IAAK;IACxC,IAAI/B,iBAAiB,IAAI+B,UAAU,EAAE;MACnC,MAAMmB,SAAS,GAAGN,IAAI,CAACC,KAAK,CAAC,CAAC5B,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGlB,iBAAiB,IAAI,IAAI,CAAC;MACrEP,gBAAgB,CAACyC,IAAI,KAAK;QACxB,GAAGA,IAAI;QACP,CAACH,UAAU,GAAG,CAACG,IAAI,CAACH,UAAU,CAAC,IAAI,CAAC,IAAImB;MAC1C,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAMC,kBAAkB,GAAIC,aAAa,IAAK;IAC5C,IAAI9E,IAAI,IAAIA,IAAI,CAAC8C,SAAS,CAACxC,eAAe,CAAC,EAAE;MAC3CqE,iBAAiB,CAAC3E,IAAI,CAAC8C,SAAS,CAACxC,eAAe,CAAC,CAAC2C,GAAG,CAAC;IACxD;IACA1C,kBAAkB,CAACuE,aAAa,CAAC;IACjCnD,oBAAoB,CAACgB,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;EAClC,CAAC;EAED,MAAMmC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI,CAACzD,YAAY,EAAE;MAAA,IAAA0D,qBAAA,EAAAC,sBAAA;MACjB,CAAAD,qBAAA,IAAAC,sBAAA,GAAAC,QAAQ,CAACC,eAAe,EAACC,iBAAiB,cAAAJ,qBAAA,uBAA1CA,qBAAA,CAAAK,IAAA,CAAAJ,sBAA6C,CAAC;MAC9C1D,eAAe,CAAC,IAAI,CAAC;IACvB,CAAC,MAAM;MAAA,IAAA+D,qBAAA,EAAAC,SAAA;MACL,CAAAD,qBAAA,IAAAC,SAAA,GAAAL,QAAQ,EAACM,cAAc,cAAAF,qBAAA,uBAAvBA,qBAAA,CAAAD,IAAA,CAAAE,SAA0B,CAAC;MAC3BhE,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,MAAMkE,eAAe,GAAGA,CAAA,KAAM;IAC5BhE,eAAe,CAACmC,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IACjC,IAAIpC,YAAY,IAAI,CAAC,EAAE;MACrBkE,KAAK,CAAC,yFAAyF,CAAC;MAChGvD,YAAY,CAAC,CAAC;IAChB,CAAC,MAAM;MACLuD,KAAK,CAAC,WAAWlE,YAAY,GAAG,CAAC,8CAA8C,CAAC;IAClF;EACF,CAAC;;EAED;EACApC,SAAS,CAAC,MAAM;IACd,MAAMuG,sBAAsB,GAAGA,CAAA,KAAM;MACnC,IAAIT,QAAQ,CAACU,MAAM,IAAI5F,IAAI,IAAI,CAACc,UAAU,EAAE;QAC1C2E,eAAe,CAAC,CAAC;MACnB;IACF,CAAC;IAEDP,QAAQ,CAACW,gBAAgB,CAAC,kBAAkB,EAAEF,sBAAsB,CAAC;IACrE,OAAO,MAAMT,QAAQ,CAACY,mBAAmB,CAAC,kBAAkB,EAAEH,sBAAsB,CAAC;EACvF,CAAC,EAAE,CAAC3F,IAAI,EAAEc,UAAU,EAAEU,YAAY,CAAC,CAAC;EAEpC,MAAMuE,eAAe,GAAGA,CAAA,KAAM;IAC5B1E,mBAAmB,CAAC,KAAK,CAAC;IAC1BM,oBAAoB,CAACgB,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;EAClC,CAAC;EAED,IAAIlC,OAAO,EAAE,oBAAOjB,OAAA;IAAKuG,SAAS,EAAC,SAAS;IAAAC,QAAA,EAAC;EAAgB;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;EACnE,IAAIzF,KAAK,EAAE,oBAAOnB,OAAA;IAAKuG,SAAS,EAAC,OAAO;IAAAC,QAAA,EAAErF;EAAK;IAAAsF,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EACtD,IAAI,CAACrG,IAAI,EAAE,oBAAOP,OAAA;IAAKuG,SAAS,EAAC,OAAO;IAAAC,QAAA,EAAC;EAAc;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;;EAE7D;EACA,IAAIjF,gBAAgB,EAAE;IAAA,IAAAkF,aAAA;IACpB,oBACE7G,OAAA;MAAKuG,SAAS,EAAC,6BAA6B;MAAAC,QAAA,eAC1CxG,OAAA;QAAKuG,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCxG,OAAA;UAAAwG,QAAA,EAAI;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAE7B5G,OAAA;UAAKuG,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBxG,OAAA;YAAAwG,QAAA,EAAKjG,IAAI,CAACuG;UAAK;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACrB5G,OAAA;YAAKuG,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BxG,OAAA;cAAKuG,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BxG,OAAA;gBAAMuG,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACvC5G,OAAA;gBAAAwG,QAAA,GAAM,WAAS,GAAAK,aAAA,GAACtG,IAAI,CAACwG,OAAO,cAAAF,aAAA,uBAAZA,aAAA,CAAcG,IAAI;cAAA;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC,eACN5G,OAAA;cAAKuG,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BxG,OAAA;gBAAMuG,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACtC5G,OAAA;gBAAAwG,QAAA,GAAM,aAAW,EAACjG,IAAI,CAAC8C,SAAS,CAAC4D,MAAM;cAAA;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC,eACN5G,OAAA;cAAKuG,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BxG,OAAA;gBAAMuG,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACtC5G,OAAA;gBAAAwG,QAAA,GAAM,YAAU,EAACjG,IAAI,CAAC0C,QAAQ,EAAC,UAAQ;cAAA;gBAAAwD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC,eACN5G,OAAA;cAAKuG,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BxG,OAAA;gBAAMuG,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACvC5G,OAAA;gBAAAwG,QAAA,GAAM,gBAAc,EAACjG,IAAI,CAAC8C,SAAS,CAAC6D,MAAM,CAAC,CAACC,GAAG,EAAEC,CAAC,KAAKD,GAAG,IAAIC,CAAC,CAACC,MAAM,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;cAAA;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN5G,OAAA;UAAKuG,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBACnCxG,OAAA;YAAAwG,QAAA,EAAI;UAAuB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChC5G,OAAA;YAAAwG,QAAA,gBACExG,OAAA;cAAAwG,QAAA,EAAI;YAA6C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtD5G,OAAA;cAAAwG,QAAA,EAAI;YAA6D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtE5G,OAAA;cAAAwG,QAAA,EAAI;YAAoC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7C5G,OAAA;cAAAwG,QAAA,EAAI;YAA+C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxD5G,OAAA;cAAAwG,QAAA,EAAI;YAA4C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrD5G,OAAA;cAAAwG,QAAA,EAAI;YAAoD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7D5G,OAAA;cAAAwG,QAAA,EAAI;YAAyC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,EAELrG,IAAI,CAAC+G,YAAY,iBAChBtH,OAAA;UAAKuG,SAAS,EAAC,uBAAuB;UAAAC,QAAA,gBACpCxG,OAAA;YAAAwG,QAAA,EAAI;UAAwB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjC5G,OAAA;YAAAwG,QAAA,EAAIjG,IAAI,CAAC+G;UAAY;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CACN,eAED5G,OAAA;UAAKuG,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BxG,OAAA;YAAQuH,OAAO,EAAEjC,gBAAiB;YAACiB,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAAC;UAEjE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT5G,OAAA;YAAQuH,OAAO,EAAEjB,eAAgB;YAACC,SAAS,EAAC,2BAA2B;YAAAC,QAAA,EAAC;UAExE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAEN5G,OAAA;UAAKuG,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3BxG,OAAA;YAAAwG,QAAA,EAAG;UAAsE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1E,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,MAAMrD,QAAQ,GAAGhD,IAAI,CAAC8C,SAAS,CAACxC,eAAe,CAAC;EAEhD,oBACEb,OAAA;IAAKuG,SAAS,EAAE,yBAAyB1E,YAAY,GAAG,YAAY,GAAG,EAAE,EAAG;IAAA2E,QAAA,gBAE1ExG,OAAA;MAAKuG,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BxG,OAAA;QAAKuG,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBxG,OAAA;UAAAwG,QAAA,EAAKjG,IAAI,CAACuG;QAAK;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACrB5G,OAAA;UAAAwG,QAAA,GAAG,WAAS,EAAC3F,eAAe,GAAG,CAAC,EAAC,MAAI,EAACN,IAAI,CAAC8C,SAAS,CAAC4D,MAAM;QAAA;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7D,CAAC,eAEN5G,OAAA;QAAKuG,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BxG,OAAA;UAAKuG,SAAS,EAAC,OAAO;UAAAC,QAAA,GAAC,SACnB,EAAC9B,UAAU,CAAC3D,QAAQ,CAAC;QAAA;UAAA0F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,eACN5G,OAAA;UAAQuH,OAAO,EAAEjC,gBAAiB;UAACiB,SAAS,EAAC,YAAY;UAAAC,QAAA,EACtD3E,YAAY,GAAG,IAAI,GAAG;QAAK;UAAA4E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN5G,OAAA;MAAKuG,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BxG,OAAA;QAAAwG,QAAA,EAAKjG,IAAI,CAACuG;MAAK;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACrB5G,OAAA;QAAAwG,QAAA,gBAAGxG,OAAA;UAAAwG,QAAA,EAAQ;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,KAAC,GAAAzG,cAAA,GAACI,IAAI,CAACwG,OAAO,cAAA5G,cAAA,uBAAZA,cAAA,CAAc6G,IAAI;MAAA;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACrD5G,OAAA;QAAAwG,QAAA,GAAG,WAAS,EAAC3F,eAAe,GAAG,CAAC,EAAC,MAAI,EAACN,IAAI,CAAC8C,SAAS,CAAC4D,MAAM;MAAA;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7D,CAAC,eAGN5G,OAAA;MAAKuG,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC3BxG,OAAA;QAAKuG,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBxG,OAAA;UAAAwG,QAAA,EAAI;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrB5G,OAAA;UAAKuG,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BxG,OAAA;YAAMuG,SAAS,EAAC,gBAAgB;YAAAC,QAAA,GAC7BhC,MAAM,CAACC,MAAM,CAAC9D,OAAO,CAAC,CAAC0D,MAAM,CAACmD,CAAC,IAAIA,CAAC,CAAC/D,eAAe,CAACwD,MAAM,GAAG,CAAC,CAAC,CAACA,MAAM,EAAC,WAC3E;UAAA;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACP5G,OAAA;YAAMuG,SAAS,EAAC,aAAa;YAAAC,QAAA,GAAC,KAAG,EAACjG,IAAI,CAAC8C,SAAS,CAAC4D,MAAM;UAAA;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN5G,OAAA;QAAKuG,SAAS,EAAC,UAAU;QAAAC,QAAA,EACtBjG,IAAI,CAAC8C,SAAS,CAACoE,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK,KAAK;UAAA,IAAAC,qBAAA;UAChC,MAAMC,UAAU,GAAG,EAAAD,qBAAA,GAAAjH,OAAO,CAACJ,IAAI,CAAC8C,SAAS,CAACsE,KAAK,CAAC,CAACnE,GAAG,CAAC,cAAAoE,qBAAA,uBAAlCA,qBAAA,CAAoCnE,eAAe,CAACwD,MAAM,IAAG,CAAC;UACjF,MAAM9B,SAAS,GAAG1D,aAAa,CAAClB,IAAI,CAAC8C,SAAS,CAACsE,KAAK,CAAC,CAACnE,GAAG,CAAC,IAAI,CAAC;UAE/D,oBACExD,OAAA;YAEEuG,SAAS,EAAE,WAAWoB,KAAK,KAAK9G,eAAe,GAAG,QAAQ,GAAG,EAAE,IAC7DgH,UAAU,GAAG,UAAU,GAAG,EAAE,EAC3B;YACHN,OAAO,EAAEA,CAAA,KAAMnC,kBAAkB,CAACuC,KAAK,CAAE;YACzCb,KAAK,EAAE,YAAYa,KAAK,GAAG,CAAC,GAAGE,UAAU,GAAG,aAAa,GAAG,EAAE,YAAYhD,IAAI,CAACC,KAAK,CAACK,SAAS,GAAG,EAAE,CAAC,IAAI,CAACA,SAAS,GAAG,EAAE,EAAEH,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAG;YAAAuB,QAAA,gBAEvJxG,OAAA;cAAMuG,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAEmB,KAAK,GAAG;YAAC;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EACnDiB,UAAU,iBAAI7H,OAAA;cAAMuG,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC5D5G,OAAA;cAAMuG,SAAS,EAAC,gBAAgB;cAAAC,QAAA,GAAE3B,IAAI,CAACC,KAAK,CAACK,SAAS,GAAG,EAAE,CAAC,EAAC,GAAC;YAAA;cAAAsB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA,GAThEe,KAAK;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAUJ,CAAC;QAEb,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEN5G,OAAA;QAAKuG,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBxG,OAAA;UAAKuG,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BxG,OAAA;YAAKuG,SAAS,EAAC;UAAsB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC5C5G,OAAA;YAAAwG,QAAA,EAAM;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,eACN5G,OAAA;UAAKuG,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BxG,OAAA;YAAKuG,SAAS,EAAC;UAAuB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7C5G,OAAA;YAAAwG,QAAA,EAAM;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC,eACN5G,OAAA;UAAKuG,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BxG,OAAA;YAAKuG,SAAS,EAAC;UAAyB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC/C5G,OAAA;YAAAwG,QAAA,EAAM;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN5G,OAAA;MAAKuG,SAAS,EAAC,oBAAoB;MAAAC,QAAA,gBACjCxG,OAAA;QAAKuG,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BxG,OAAA;UAAAwG,QAAA,GAAI,WAAS,EAAC3F,eAAe,GAAG,CAAC;QAAA;UAAA4F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACvC5G,OAAA;UAAMuG,SAAS,EAAC,eAAe;UAAAC,QAAA,EAC5BjD,QAAQ,CAACuE,IAAI,KAAK,KAAK,GAAG,iBAAiB,GAC3CvE,QAAQ,CAACuE,IAAI,KAAK,YAAY,GAAG,YAAY,GAC7C;QAAiB;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAEN5G,OAAA;QAAKuG,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAEjD,QAAQ,CAACwE;MAAI;QAAAtB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAEpD5G,OAAA;QAAKuG,SAAS,EAAC,SAAS;QAAAC,QAAA,EACrBjD,QAAQ,CAACyE,OAAO,CAACP,GAAG,CAAC,CAACQ,MAAM,EAAEN,KAAK,KAAK;UAAA,IAAAO,qBAAA;UACvC,MAAMC,UAAU,IAAAD,qBAAA,GAAGvH,OAAO,CAAC4C,QAAQ,CAACC,GAAG,CAAC,cAAA0E,qBAAA,uBAArBA,qBAAA,CAAuBzE,eAAe,CAACW,QAAQ,CAAC6D,MAAM,CAACF,IAAI,CAAC;UAC/E,MAAMK,YAAY,GAAGC,MAAM,CAACC,YAAY,CAAC,EAAE,GAAGX,KAAK,CAAC;UAEpD,oBACE3H,OAAA;YAAiBuG,SAAS,EAAE,UAAU4B,UAAU,GAAG,UAAU,GAAG,EAAE,EAAG;YAAA3B,QAAA,eACnExG,OAAA;cAAAwG,QAAA,gBACExG,OAAA;gBACE8H,IAAI,EAAEvE,QAAQ,CAACuE,IAAI,KAAK,cAAc,GAAG,UAAU,GAAG,OAAQ;gBAC9Dd,IAAI,EAAE,YAAYzD,QAAQ,CAACC,GAAG,EAAG;gBACjCS,KAAK,EAAEgE,MAAM,CAACF,IAAK;gBACnBQ,OAAO,EAAEJ,UAAW;gBACpBK,QAAQ,EAAGC,CAAC,IAAK1E,kBAAkB,CACjCR,QAAQ,CAACC,GAAG,EACZyE,MAAM,CAACF,IAAI,EACXxE,QAAQ,CAACuE,IAAI,KAAK,cACpB;cAAE;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACF5G,OAAA;gBAAMuG,SAAS,EAAC,eAAe;gBAAAC,QAAA,GAAE4B,YAAY,EAAC,GAAC;cAAA;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACtD5G,OAAA;gBAAMuG,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAEyB,MAAM,CAACF;cAAI;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C;UAAC,GAfAe,KAAK;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgBV,CAAC;QAEV,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGN5G,OAAA;QAAKuG,SAAS,EAAC,eAAe;QAAAC,QAAA,EAC3B,EAAApG,sBAAA,GAAAO,OAAO,CAAC4C,QAAQ,CAACC,GAAG,CAAC,cAAApD,sBAAA,uBAArBA,sBAAA,CAAuBqD,eAAe,CAACwD,MAAM,IAAG,CAAC,gBAChDjH,OAAA;UAAMuG,SAAS,EAAC,UAAU;UAAAC,QAAA,EAAC;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,gBAE5C5G,OAAA;UAAMuG,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAClD;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN5G,OAAA;MAAKuG,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACzBxG,OAAA;QACEuG,SAAS,EAAC,mBAAmB;QAC7BgB,OAAO,EAAEA,CAAA,KAAMzG,kBAAkB,CAAC+D,IAAI,CAAC6D,GAAG,CAAC,CAAC,EAAE7H,eAAe,GAAG,CAAC,CAAC,CAAE;QACpE8H,QAAQ,EAAE9H,eAAe,KAAK,CAAE;QAAA2F,QAAA,EACjC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,EAER/F,eAAe,GAAGN,IAAI,CAAC8C,SAAS,CAAC4D,MAAM,GAAG,CAAC,gBAC1CjH,OAAA;QACEuG,SAAS,EAAC,KAAK;QACfgB,OAAO,EAAEA,CAAA,KAAMzG,kBAAkB,CAACD,eAAe,GAAG,CAAC,CAAE;QAAA2F,QAAA,EACxD;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,gBAET5G,OAAA;QACEuG,SAAS,EAAC,iBAAiB;QAC3BgB,OAAO,EAAE7E,YAAa;QACtBiG,QAAQ,EAAEtH,UAAW;QAAAmF,QAAA,EAEpBnF,UAAU,GAAG,eAAe,GAAG;MAAa;QAAAoF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGN5G,OAAA;MAAKuG,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC3BxG,OAAA;QAAAwG,QAAA,EAAI;MAAa;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACtB5G,OAAA;QAAAwG,QAAA,GAAG,YACS,EAAChC,MAAM,CAACC,MAAM,CAAC9D,OAAO,CAAC,CAAC0D,MAAM,CAACmD,CAAC,IAAIA,CAAC,CAAC/D,eAAe,CAACwD,MAAM,GAAG,CAAC,CAAC,CAACA,MAAM,EAAC,KAAG,EAAC1G,IAAI,CAAC8C,SAAS,CAAC4D,MAAM;MAAA;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3G,CAAC,eACJ5G,OAAA;QAAKuG,SAAS,EAAC,cAAc;QAAAC,QAAA,eAC3BxG,OAAA;UACEuG,SAAS,EAAC,UAAU;UACpBqC,KAAK,EAAE;YACLC,KAAK,EAAE,GAAIrE,MAAM,CAACC,MAAM,CAAC9D,OAAO,CAAC,CAAC0D,MAAM,CAACmD,CAAC,IAAIA,CAAC,CAAC/D,eAAe,CAACwD,MAAM,GAAG,CAAC,CAAC,CAACA,MAAM,GAAG1G,IAAI,CAAC8C,SAAS,CAAC4D,MAAM,GAAI,GAAG;UACnH;QAAE;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC1G,EAAA,CAxZID,UAAU;EAAA,QACKL,SAAS,EACXC,WAAW;AAAA;AAAAiJ,EAAA,GAFxB7I,UAAU;AA0ZhB,eAAeA,UAAU;AAAC,IAAA6I,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}