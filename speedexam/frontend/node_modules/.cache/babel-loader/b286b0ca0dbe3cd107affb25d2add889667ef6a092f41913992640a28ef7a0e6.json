{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/ExamTaking.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport api from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ExamTaking = () => {\n  _s();\n  var _exam$subject2, _answers$question$_id2;\n  const {\n    examId\n  } = useParams();\n  const navigate = useNavigate();\n  const [exam, setExam] = useState(null);\n  const [attemptId, setAttemptId] = useState(null);\n  const [answers, setAnswers] = useState({});\n  const [currentQuestion, setCurrentQuestion] = useState(0);\n  const [timeLeft, setTimeLeft] = useState(0);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [submitting, setSubmitting] = useState(false);\n  const [examStartTime, setExamStartTime] = useState(null);\n  const [questionTimes, setQuestionTimes] = useState({});\n  const [showInstructions, setShowInstructions] = useState(true);\n  const [isFullscreen, setIsFullscreen] = useState(false);\n  const [warningCount, setWarningCount] = useState(0);\n  const [questionStartTime, setQuestionStartTime] = useState(null);\n  useEffect(() => {\n    const token = localStorage.getItem('token');\n    if (!token) {\n      navigate('/login');\n      return;\n    }\n    startExam();\n  }, [examId, navigate]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  useEffect(() => {\n    if (timeLeft > 0) {\n      const timer = setTimeout(() => setTimeLeft(timeLeft - 1), 1000);\n      return () => clearTimeout(timer);\n    } else if (timeLeft === 0 && exam) {\n      handleSubmit();\n    }\n  }, [timeLeft, exam]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  const startExam = async () => {\n    try {\n      console.log('Starting exam with ID:', examId);\n      const response = await api.post(`/exams/${examId}/start`);\n      console.log('Start exam response:', response.data);\n      setExam(response.data.exam);\n      setAttemptId(response.data.attempt);\n      setTimeLeft(response.data.exam.duration * 60); // Convert minutes to seconds\n      setExamStartTime(Date.now());\n      setQuestionStartTime(Date.now());\n\n      // Initialize answers\n      const initialAnswers = {};\n      response.data.exam.questions.forEach(question => {\n        initialAnswers[question._id] = {\n          question: question._id,\n          selectedOptions: [],\n          answer: ''\n        };\n      });\n      setAnswers(initialAnswers);\n\n      // Initialize question times\n      const initialTimes = {};\n      response.data.exam.questions.forEach(question => {\n        initialTimes[question._id] = 0;\n      });\n      setQuestionTimes(initialTimes);\n      console.log('Exam started successfully');\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('Error starting exam:', error);\n      setError(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Failed to start exam');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleAnswerChange = (questionId, value, isMultiple = false) => {\n    setAnswers(prev => ({\n      ...prev,\n      [questionId]: {\n        ...prev[questionId],\n        selectedOptions: isMultiple ? prev[questionId].selectedOptions.includes(value) ? prev[questionId].selectedOptions.filter(opt => opt !== value) : [...prev[questionId].selectedOptions, value] : [value]\n      }\n    }));\n  };\n  const handleSubmit = async () => {\n    if (submitting) return;\n    setSubmitting(true);\n    try {\n      console.log('Submitting exam with attemptId:', attemptId);\n      console.log('Answers to submit:', answers);\n      const answersArray = Object.values(answers);\n      console.log('Formatted answers array:', answersArray);\n      const response = await api.post(`/exam-attempts/${attemptId}/submit`, {\n        answers: answersArray\n      });\n      console.log('Submit response:', response.data);\n      navigate(`/results/${attemptId}`);\n    } catch (error) {\n      var _error$response2, _error$response3, _error$response3$data;\n      console.error('Submit error:', error);\n      console.error('Error response:', (_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.data);\n      setError(`Failed to submit exam: ${((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.message) || error.message}`);\n      setSubmitting(false);\n    }\n  };\n  const formatTime = seconds => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins}:${secs.toString().padStart(2, '0')}`;\n  };\n  const trackQuestionTime = questionId => {\n    if (questionStartTime && questionId) {\n      const timeSpent = Math.floor((Date.now() - questionStartTime) / 1000);\n      setQuestionTimes(prev => ({\n        ...prev,\n        [questionId]: (prev[questionId] || 0) + timeSpent\n      }));\n    }\n  };\n  const navigateToQuestion = questionIndex => {\n    if (exam && exam.questions[currentQuestion]) {\n      trackQuestionTime(exam.questions[currentQuestion]._id);\n    }\n    setCurrentQuestion(questionIndex);\n    setQuestionStartTime(Date.now());\n  };\n  const toggleFullscreen = () => {\n    if (!isFullscreen) {\n      var _document$documentEle, _document$documentEle2;\n      (_document$documentEle = (_document$documentEle2 = document.documentElement).requestFullscreen) === null || _document$documentEle === void 0 ? void 0 : _document$documentEle.call(_document$documentEle2);\n      setIsFullscreen(true);\n    } else {\n      var _document$exitFullscr, _document;\n      (_document$exitFullscr = (_document = document).exitFullscreen) === null || _document$exitFullscr === void 0 ? void 0 : _document$exitFullscr.call(_document);\n      setIsFullscreen(false);\n    }\n  };\n  const handleTabSwitch = () => {\n    setWarningCount(prev => prev + 1);\n    if (warningCount >= 2) {\n      alert('Warning: Multiple tab switches detected. This exam will be auto-submitted for security.');\n      handleSubmit();\n    } else {\n      alert(`Warning ${warningCount + 1}/3: Please stay on this tab during the exam.`);\n    }\n  };\n\n  // Add event listeners for tab switching detection\n  useEffect(() => {\n    const handleVisibilityChange = () => {\n      if (document.hidden && exam && !submitting) {\n        handleTabSwitch();\n      }\n    };\n    document.addEventListener('visibilitychange', handleVisibilityChange);\n    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);\n  }, [exam, submitting, warningCount]);\n  const startExamProper = () => {\n    setShowInstructions(false);\n    setQuestionStartTime(Date.now());\n  };\n  if (loading) return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"loading\",\n    children: \"Starting exam...\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 179,\n    columnNumber: 23\n  }, this);\n  if (error) return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"error\",\n    children: error\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 180,\n    columnNumber: 21\n  }, this);\n  if (!exam) return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"error\",\n    children: \"Exam not found\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 181,\n    columnNumber: 21\n  }, this);\n\n  // Show instructions screen first\n  if (showInstructions) {\n    var _exam$subject;\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"exam-instructions-container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"instructions-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"\\uD83D\\uDCCB Exam Instructions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"exam-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: exam.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"exam-details\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"detail-icon\",\n                children: \"\\uD83D\\uDCDA\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 194,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Subject: \", (_exam$subject = exam.subject) === null || _exam$subject === void 0 ? void 0 : _exam$subject.name]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"detail-icon\",\n                children: \"\\u2753\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Questions: \", exam.questions.length]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 199,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"detail-icon\",\n                children: \"\\u23F0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Duration: \", exam.duration, \" minutes\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 203,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"detail-icon\",\n                children: \"\\uD83D\\uDCCA\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Total Points: \", exam.questions.reduce((sum, q) => sum + (q.points || 1), 0)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"general-instructions\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\uD83D\\uDCDD General Instructions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Read each question carefully before answering\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"You can navigate between questions using the question numbers\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Your progress is automatically saved\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Make sure you have a stable internet connection\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Do not refresh the page or close the browser\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Avoid switching tabs or applications during the exam\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Submit your exam before the time runs out\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 11\n        }, this), exam.instructions && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"specific-instructions\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\uD83D\\uDCCB Specific Instructions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: exam.instructions\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"exam-controls\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: toggleFullscreen,\n            className: \"btn btn-secondary\",\n            children: \"\\uD83D\\uDDA5\\uFE0F Enter Fullscreen\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: startExamProper,\n            className: \"btn btn-success btn-large\",\n            children: \"\\uD83D\\uDE80 Start Exam\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"warning-note\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"\\u26A0\\uFE0F Once you start the exam, the timer will begin and cannot be paused.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 186,\n      columnNumber: 7\n    }, this);\n  }\n  const question = exam.questions[currentQuestion];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `exam-taking-container ${isFullscreen ? 'fullscreen' : ''}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"exam-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"exam-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: exam.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [\"Question \", currentQuestion + 1, \" of \", exam.questions.length]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 255,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"exam-controls\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"timer\",\n          children: [\"\\u23F0 \", formatTime(timeLeft)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 261,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: toggleFullscreen,\n          className: \"btn btn-sm\",\n          children: isFullscreen ? '🗗' : '🖥️'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 260,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 254,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"exam-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: exam.title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 272,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Subject:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 12\n        }, this), \" \", (_exam$subject2 = exam.subject) === null || _exam$subject2 === void 0 ? void 0 : _exam$subject2.name]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 273,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [\"Question \", currentQuestion + 1, \" of \", exam.questions.length]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 274,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 271,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"question-nav\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"nav-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"\\uD83D\\uDCDD Questions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"progress-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"answered-count\",\n            children: [Object.values(answers).filter(a => a.selectedOptions.length > 0).length, \" answered\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 282,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"total-count\",\n            children: [\"of \", exam.questions.length]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 279,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"nav-grid\",\n        children: exam.questions.map((_, index) => {\n          var _answers$exam$questio;\n          const isAnswered = ((_answers$exam$questio = answers[exam.questions[index]._id]) === null || _answers$exam$questio === void 0 ? void 0 : _answers$exam$questio.selectedOptions.length) > 0;\n          const timeSpent = questionTimes[exam.questions[index]._id] || 0;\n          return /*#__PURE__*/_jsxDEV(\"button\", {\n            className: `nav-btn ${index === currentQuestion ? 'active' : ''} ${isAnswered ? 'answered' : ''}`,\n            onClick: () => navigateToQuestion(index),\n            title: `Question ${index + 1}${isAnswered ? ' (Answered)' : ''} - Time: ${Math.floor(timeSpent / 60)}:${(timeSpent % 60).toString().padStart(2, '0')}`,\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"question-number\",\n              children: index + 1\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 303,\n              columnNumber: 17\n            }, this), isAnswered && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"answered-indicator\",\n              children: \"\\u2713\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 304,\n              columnNumber: 32\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"time-indicator\",\n              children: [Math.floor(timeSpent / 60), \"m\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 305,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 15\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 289,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"nav-legend\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"legend-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"legend-color current\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 313,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Current\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 314,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 312,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"legend-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"legend-color answered\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 317,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Answered\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 318,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 316,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"legend-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"legend-color unanswered\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Unanswered\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 322,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 320,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 311,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 278,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"question-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"question-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: [\"Question \", currentQuestion + 1]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"question-type\",\n          children: question.type === 'MCQ' ? 'Multiple Choice' : question.type === 'TRUE_FALSE' ? 'True/False' : 'Multiple Answer'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 331,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 329,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"question-text\",\n        children: question.text\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 338,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"options\",\n        children: question.options.map((option, index) => {\n          var _answers$question$_id;\n          const isSelected = (_answers$question$_id = answers[question._id]) === null || _answers$question$_id === void 0 ? void 0 : _answers$question$_id.selectedOptions.includes(option.text);\n          const optionLetter = String.fromCharCode(65 + index);\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `option ${isSelected ? 'selected' : ''}`,\n            children: /*#__PURE__*/_jsxDEV(\"label\", {\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: question.type === 'MULTI_ANSWER' ? 'checkbox' : 'radio',\n                name: `question_${question._id}`,\n                value: option.text,\n                checked: isSelected,\n                onChange: e => handleAnswerChange(question._id, option.text, question.type === 'MULTI_ANSWER')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 348,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"option-letter\",\n                children: [optionLetter, \".\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 359,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"option-text\",\n                children: option.text\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 360,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 347,\n              columnNumber: 17\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 346,\n            columnNumber: 15\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 340,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"answer-status\",\n        children: ((_answers$question$_id2 = answers[question._id]) === null || _answers$question$_id2 === void 0 ? void 0 : _answers$question$_id2.selectedOptions.length) > 0 ? /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"answered\",\n          children: \"\\u2713 Answered\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 370,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"unanswered\",\n          children: \"\\u26A0 Not answered\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 372,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 368,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 328,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"navigation\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn btn-secondary\",\n        onClick: () => navigateToQuestion(Math.max(0, currentQuestion - 1)),\n        disabled: currentQuestion === 0,\n        children: \"\\u2190 Previous\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 379,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"nav-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"question-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"current-q\",\n            children: [\"Q\", currentQuestion + 1]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 389,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"total-q\",\n            children: [\"of \", exam.questions.length]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 390,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 388,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"time-spent\",\n          children: [\"Time on this question: \", Math.floor((questionTimes[question._id] || 0) / 60), \":\", ((questionTimes[question._id] || 0) % 60).toString().padStart(2, '0')]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 392,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 387,\n        columnNumber: 9\n      }, this), currentQuestion < exam.questions.length - 1 ? /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn btn-primary\",\n        onClick: () => navigateToQuestion(currentQuestion + 1),\n        children: \"Next \\u2192\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 398,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn btn-success\",\n        onClick: handleSubmit,\n        disabled: submitting,\n        children: submitting ? 'Submitting...' : 'Submit Exam'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 405,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 378,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"exam-summary\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        children: \"Exam Progress\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 417,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [\"Answered: \", Object.values(answers).filter(a => a.selectedOptions.length > 0).length, \" / \", exam.questions.length]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 418,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"progress-bar\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"progress\",\n          style: {\n            width: `${Object.values(answers).filter(a => a.selectedOptions.length > 0).length / exam.questions.length * 100}%`\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 422,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 421,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 416,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 252,\n    columnNumber: 5\n  }, this);\n};\n_s(ExamTaking, \"mn9lSx9B9gFKUXu2XCdVINe+1Cg=\", false, function () {\n  return [useParams, useNavigate];\n});\n_c = ExamTaking;\nexport default ExamTaking;\nvar _c;\n$RefreshReg$(_c, \"ExamTaking\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useNavigate", "api", "jsxDEV", "_jsxDEV", "ExamTaking", "_s", "_exam$subject2", "_answers$question$_id2", "examId", "navigate", "exam", "setExam", "attemptId", "setAttemptId", "answers", "setAnswers", "currentQuestion", "setCurrentQuestion", "timeLeft", "setTimeLeft", "loading", "setLoading", "error", "setError", "submitting", "setSubmitting", "examStartTime", "setExamStartTime", "questionTimes", "setQuestionTimes", "showInstructions", "setShowInstructions", "isFullscreen", "setIsFullscreen", "warningCount", "setWarningCount", "questionStartTime", "setQuestionStartTime", "token", "localStorage", "getItem", "startExam", "timer", "setTimeout", "clearTimeout", "handleSubmit", "console", "log", "response", "post", "data", "attempt", "duration", "Date", "now", "initialAnswers", "questions", "for<PERSON>ach", "question", "_id", "selectedOptions", "answer", "initialTimes", "_error$response", "_error$response$data", "message", "handleAnswerChange", "questionId", "value", "isMultiple", "prev", "includes", "filter", "opt", "answersArray", "Object", "values", "_error$response2", "_error$response3", "_error$response3$data", "formatTime", "seconds", "mins", "Math", "floor", "secs", "toString", "padStart", "trackQuestionTime", "timeSpent", "navigateToQuestion", "questionIndex", "toggleFullscreen", "_document$documentEle", "_document$documentEle2", "document", "documentElement", "requestFullscreen", "call", "_document$exitFullscr", "_document", "exitFullscreen", "handleTabSwitch", "alert", "handleVisibilityChange", "hidden", "addEventListener", "removeEventListener", "startExamProper", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_exam$subject", "title", "subject", "name", "length", "reduce", "sum", "q", "points", "instructions", "onClick", "a", "map", "_", "index", "_answers$exam$questio", "isAnswered", "type", "text", "options", "option", "_answers$question$_id", "isSelected", "optionLetter", "String", "fromCharCode", "checked", "onChange", "e", "max", "disabled", "style", "width", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/ExamTaking.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport api from '../services/api';\n\nconst ExamTaking = () => {\n  const { examId } = useParams();\n  const navigate = useNavigate();\n  const [exam, setExam] = useState(null);\n  const [attemptId, setAttemptId] = useState(null);\n  const [answers, setAnswers] = useState({});\n  const [currentQuestion, setCurrentQuestion] = useState(0);\n  const [timeLeft, setTimeLeft] = useState(0);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [submitting, setSubmitting] = useState(false);\n  const [examStartTime, setExamStartTime] = useState(null);\n  const [questionTimes, setQuestionTimes] = useState({});\n  const [showInstructions, setShowInstructions] = useState(true);\n  const [isFullscreen, setIsFullscreen] = useState(false);\n  const [warningCount, setWarningCount] = useState(0);\n  const [questionStartTime, setQuestionStartTime] = useState(null);\n\n  useEffect(() => {\n    const token = localStorage.getItem('token');\n    if (!token) {\n      navigate('/login');\n      return;\n    }\n\n    startExam();\n  }, [examId, navigate]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  useEffect(() => {\n    if (timeLeft > 0) {\n      const timer = setTimeout(() => setTimeLeft(timeLeft - 1), 1000);\n      return () => clearTimeout(timer);\n    } else if (timeLeft === 0 && exam) {\n      handleSubmit();\n    }\n  }, [timeLeft, exam]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  const startExam = async () => {\n    try {\n      console.log('Starting exam with ID:', examId);\n      const response = await api.post(`/exams/${examId}/start`);\n      console.log('Start exam response:', response.data);\n\n      setExam(response.data.exam);\n      setAttemptId(response.data.attempt);\n      setTimeLeft(response.data.exam.duration * 60); // Convert minutes to seconds\n      setExamStartTime(Date.now());\n      setQuestionStartTime(Date.now());\n\n      // Initialize answers\n      const initialAnswers = {};\n      response.data.exam.questions.forEach(question => {\n        initialAnswers[question._id] = {\n          question: question._id,\n          selectedOptions: [],\n          answer: ''\n        };\n      });\n      setAnswers(initialAnswers);\n\n      // Initialize question times\n      const initialTimes = {};\n      response.data.exam.questions.forEach(question => {\n        initialTimes[question._id] = 0;\n      });\n      setQuestionTimes(initialTimes);\n\n      console.log('Exam started successfully');\n    } catch (error) {\n      console.error('Error starting exam:', error);\n      setError(error.response?.data?.message || 'Failed to start exam');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleAnswerChange = (questionId, value, isMultiple = false) => {\n    setAnswers(prev => ({\n      ...prev,\n      [questionId]: {\n        ...prev[questionId],\n        selectedOptions: isMultiple \n          ? (prev[questionId].selectedOptions.includes(value)\n              ? prev[questionId].selectedOptions.filter(opt => opt !== value)\n              : [...prev[questionId].selectedOptions, value])\n          : [value]\n      }\n    }));\n  };\n\n  const handleSubmit = async () => {\n    if (submitting) return;\n\n    setSubmitting(true);\n    try {\n      console.log('Submitting exam with attemptId:', attemptId);\n      console.log('Answers to submit:', answers);\n\n      const answersArray = Object.values(answers);\n      console.log('Formatted answers array:', answersArray);\n\n      const response = await api.post(`/exam-attempts/${attemptId}/submit`, { answers: answersArray });\n      console.log('Submit response:', response.data);\n\n      navigate(`/results/${attemptId}`);\n    } catch (error) {\n      console.error('Submit error:', error);\n      console.error('Error response:', error.response?.data);\n      setError(`Failed to submit exam: ${error.response?.data?.message || error.message}`);\n      setSubmitting(false);\n    }\n  };\n\n  const formatTime = (seconds) => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins}:${secs.toString().padStart(2, '0')}`;\n  };\n\n  const trackQuestionTime = (questionId) => {\n    if (questionStartTime && questionId) {\n      const timeSpent = Math.floor((Date.now() - questionStartTime) / 1000);\n      setQuestionTimes(prev => ({\n        ...prev,\n        [questionId]: (prev[questionId] || 0) + timeSpent\n      }));\n    }\n  };\n\n  const navigateToQuestion = (questionIndex) => {\n    if (exam && exam.questions[currentQuestion]) {\n      trackQuestionTime(exam.questions[currentQuestion]._id);\n    }\n    setCurrentQuestion(questionIndex);\n    setQuestionStartTime(Date.now());\n  };\n\n  const toggleFullscreen = () => {\n    if (!isFullscreen) {\n      document.documentElement.requestFullscreen?.();\n      setIsFullscreen(true);\n    } else {\n      document.exitFullscreen?.();\n      setIsFullscreen(false);\n    }\n  };\n\n  const handleTabSwitch = () => {\n    setWarningCount(prev => prev + 1);\n    if (warningCount >= 2) {\n      alert('Warning: Multiple tab switches detected. This exam will be auto-submitted for security.');\n      handleSubmit();\n    } else {\n      alert(`Warning ${warningCount + 1}/3: Please stay on this tab during the exam.`);\n    }\n  };\n\n  // Add event listeners for tab switching detection\n  useEffect(() => {\n    const handleVisibilityChange = () => {\n      if (document.hidden && exam && !submitting) {\n        handleTabSwitch();\n      }\n    };\n\n    document.addEventListener('visibilitychange', handleVisibilityChange);\n    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);\n  }, [exam, submitting, warningCount]);\n\n  const startExamProper = () => {\n    setShowInstructions(false);\n    setQuestionStartTime(Date.now());\n  };\n\n  if (loading) return <div className=\"loading\">Starting exam...</div>;\n  if (error) return <div className=\"error\">{error}</div>;\n  if (!exam) return <div className=\"error\">Exam not found</div>;\n\n  // Show instructions screen first\n  if (showInstructions) {\n    return (\n      <div className=\"exam-instructions-container\">\n        <div className=\"instructions-card\">\n          <h1>📋 Exam Instructions</h1>\n\n          <div className=\"exam-info\">\n            <h2>{exam.title}</h2>\n            <div className=\"exam-details\">\n              <div className=\"detail-item\">\n                <span className=\"detail-icon\">📚</span>\n                <span>Subject: {exam.subject?.name}</span>\n              </div>\n              <div className=\"detail-item\">\n                <span className=\"detail-icon\">❓</span>\n                <span>Questions: {exam.questions.length}</span>\n              </div>\n              <div className=\"detail-item\">\n                <span className=\"detail-icon\">⏰</span>\n                <span>Duration: {exam.duration} minutes</span>\n              </div>\n              <div className=\"detail-item\">\n                <span className=\"detail-icon\">📊</span>\n                <span>Total Points: {exam.questions.reduce((sum, q) => sum + (q.points || 1), 0)}</span>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"general-instructions\">\n            <h3>📝 General Instructions</h3>\n            <ul>\n              <li>Read each question carefully before answering</li>\n              <li>You can navigate between questions using the question numbers</li>\n              <li>Your progress is automatically saved</li>\n              <li>Make sure you have a stable internet connection</li>\n              <li>Do not refresh the page or close the browser</li>\n              <li>Avoid switching tabs or applications during the exam</li>\n              <li>Submit your exam before the time runs out</li>\n            </ul>\n          </div>\n\n          {exam.instructions && (\n            <div className=\"specific-instructions\">\n              <h3>📋 Specific Instructions</h3>\n              <p>{exam.instructions}</p>\n            </div>\n          )}\n\n          <div className=\"exam-controls\">\n            <button onClick={toggleFullscreen} className=\"btn btn-secondary\">\n              🖥️ Enter Fullscreen\n            </button>\n            <button onClick={startExamProper} className=\"btn btn-success btn-large\">\n              🚀 Start Exam\n            </button>\n          </div>\n\n          <div className=\"warning-note\">\n            <p>⚠️ Once you start the exam, the timer will begin and cannot be paused.</p>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  const question = exam.questions[currentQuestion];\n\n  return (\n    <div className={`exam-taking-container ${isFullscreen ? 'fullscreen' : ''}`}>\n      {/* Exam Header */}\n      <div className=\"exam-header\">\n        <div className=\"exam-info\">\n          <h2>{exam.title}</h2>\n          <p>Question {currentQuestion + 1} of {exam.questions.length}</p>\n        </div>\n\n        <div className=\"exam-controls\">\n          <div className=\"timer\">\n            ⏰ {formatTime(timeLeft)}\n          </div>\n          <button onClick={toggleFullscreen} className=\"btn btn-sm\">\n            {isFullscreen ? '🗗' : '🖥️'}\n          </button>\n        </div>\n      </div>\n\n      {/* Exam Header */}\n      <div className=\"exam-header\">\n        <h2>{exam.title}</h2>\n        <p><strong>Subject:</strong> {exam.subject?.name}</p>\n        <p>Question {currentQuestion + 1} of {exam.questions.length}</p>\n      </div>\n\n      {/* Enhanced Question Navigation */}\n      <div className=\"question-nav\">\n        <div className=\"nav-header\">\n          <h3>📝 Questions</h3>\n          <div className=\"progress-info\">\n            <span className=\"answered-count\">\n              {Object.values(answers).filter(a => a.selectedOptions.length > 0).length} answered\n            </span>\n            <span className=\"total-count\">of {exam.questions.length}</span>\n          </div>\n        </div>\n\n        <div className=\"nav-grid\">\n          {exam.questions.map((_, index) => {\n            const isAnswered = answers[exam.questions[index]._id]?.selectedOptions.length > 0;\n            const timeSpent = questionTimes[exam.questions[index]._id] || 0;\n\n            return (\n              <button\n                key={index}\n                className={`nav-btn ${index === currentQuestion ? 'active' : ''} ${\n                  isAnswered ? 'answered' : ''\n                }`}\n                onClick={() => navigateToQuestion(index)}\n                title={`Question ${index + 1}${isAnswered ? ' (Answered)' : ''} - Time: ${Math.floor(timeSpent / 60)}:${(timeSpent % 60).toString().padStart(2, '0')}`}\n              >\n                <span className=\"question-number\">{index + 1}</span>\n                {isAnswered && <span className=\"answered-indicator\">✓</span>}\n                <span className=\"time-indicator\">{Math.floor(timeSpent / 60)}m</span>\n              </button>\n            );\n          })}\n        </div>\n\n        <div className=\"nav-legend\">\n          <div className=\"legend-item\">\n            <div className=\"legend-color current\"></div>\n            <span>Current</span>\n          </div>\n          <div className=\"legend-item\">\n            <div className=\"legend-color answered\"></div>\n            <span>Answered</span>\n          </div>\n          <div className=\"legend-item\">\n            <div className=\"legend-color unanswered\"></div>\n            <span>Unanswered</span>\n          </div>\n        </div>\n      </div>\n\n      {/* Question */}\n      <div className=\"question-container\">\n        <div className=\"question-header\">\n          <h3>Question {currentQuestion + 1}</h3>\n          <span className=\"question-type\">\n            {question.type === 'MCQ' ? 'Multiple Choice' :\n             question.type === 'TRUE_FALSE' ? 'True/False' :\n             'Multiple Answer'}\n          </span>\n        </div>\n\n        <div className=\"question-text\">{question.text}</div>\n\n        <div className=\"options\">\n          {question.options.map((option, index) => {\n            const isSelected = answers[question._id]?.selectedOptions.includes(option.text);\n            const optionLetter = String.fromCharCode(65 + index);\n\n            return (\n              <div key={index} className={`option ${isSelected ? 'selected' : ''}`}>\n                <label>\n                  <input\n                    type={question.type === 'MULTI_ANSWER' ? 'checkbox' : 'radio'}\n                    name={`question_${question._id}`}\n                    value={option.text}\n                    checked={isSelected}\n                    onChange={(e) => handleAnswerChange(\n                      question._id,\n                      option.text,\n                      question.type === 'MULTI_ANSWER'\n                    )}\n                  />\n                  <span className=\"option-letter\">{optionLetter}.</span>\n                  <span className=\"option-text\">{option.text}</span>\n                </label>\n              </div>\n            );\n          })}\n        </div>\n\n        {/* Answer Status */}\n        <div className=\"answer-status\">\n          {answers[question._id]?.selectedOptions.length > 0 ? (\n            <span className=\"answered\">✓ Answered</span>\n          ) : (\n            <span className=\"unanswered\">⚠ Not answered</span>\n          )}\n        </div>\n      </div>\n\n      {/* Enhanced Navigation */}\n      <div className=\"navigation\">\n        <button\n          className=\"btn btn-secondary\"\n          onClick={() => navigateToQuestion(Math.max(0, currentQuestion - 1))}\n          disabled={currentQuestion === 0}\n        >\n          ← Previous\n        </button>\n\n        <div className=\"nav-center\">\n          <div className=\"question-info\">\n            <span className=\"current-q\">Q{currentQuestion + 1}</span>\n            <span className=\"total-q\">of {exam.questions.length}</span>\n          </div>\n          <div className=\"time-spent\">\n            Time on this question: {Math.floor((questionTimes[question._id] || 0) / 60)}:{((questionTimes[question._id] || 0) % 60).toString().padStart(2, '0')}\n          </div>\n        </div>\n\n        {currentQuestion < exam.questions.length - 1 ? (\n          <button\n            className=\"btn btn-primary\"\n            onClick={() => navigateToQuestion(currentQuestion + 1)}\n          >\n            Next →\n          </button>\n        ) : (\n          <button\n            className=\"btn btn-success\"\n            onClick={handleSubmit}\n            disabled={submitting}\n          >\n            {submitting ? 'Submitting...' : 'Submit Exam'}\n          </button>\n        )}\n      </div>\n\n      {/* Exam Summary */}\n      <div className=\"exam-summary\">\n        <h4>Exam Progress</h4>\n        <p>\n          Answered: {Object.values(answers).filter(a => a.selectedOptions.length > 0).length} / {exam.questions.length}\n        </p>\n        <div className=\"progress-bar\">\n          <div\n            className=\"progress\"\n            style={{\n              width: `${(Object.values(answers).filter(a => a.selectedOptions.length > 0).length / exam.questions.length) * 100}%`\n            }}\n          ></div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ExamTaking;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,OAAOC,GAAG,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,cAAA,EAAAC,sBAAA;EACvB,MAAM;IAAEC;EAAO,CAAC,GAAGT,SAAS,CAAC,CAAC;EAC9B,MAAMU,QAAQ,GAAGT,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACU,IAAI,EAAEC,OAAO,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACe,SAAS,EAAEC,YAAY,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1C,MAAM,CAACmB,eAAe,EAAEC,kBAAkB,CAAC,GAAGpB,QAAQ,CAAC,CAAC,CAAC;EACzD,MAAM,CAACqB,QAAQ,EAAEC,WAAW,CAAC,GAAGtB,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAACuB,OAAO,EAAEC,UAAU,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACyB,KAAK,EAAEC,QAAQ,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC2B,UAAU,EAAEC,aAAa,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC6B,aAAa,EAAEC,gBAAgB,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAAC+B,aAAa,EAAEC,gBAAgB,CAAC,GAAGhC,QAAQ,CAAC,CAAC,CAAC,CAAC;EACtD,MAAM,CAACiC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACmC,YAAY,EAAEC,eAAe,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACqC,YAAY,EAAEC,eAAe,CAAC,GAAGtC,QAAQ,CAAC,CAAC,CAAC;EACnD,MAAM,CAACuC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGxC,QAAQ,CAAC,IAAI,CAAC;EAEhEC,SAAS,CAAC,MAAM;IACd,MAAMwC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAI,CAACF,KAAK,EAAE;MACV7B,QAAQ,CAAC,QAAQ,CAAC;MAClB;IACF;IAEAgC,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAACjC,MAAM,EAAEC,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAExBX,SAAS,CAAC,MAAM;IACd,IAAIoB,QAAQ,GAAG,CAAC,EAAE;MAChB,MAAMwB,KAAK,GAAGC,UAAU,CAAC,MAAMxB,WAAW,CAACD,QAAQ,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC;MAC/D,OAAO,MAAM0B,YAAY,CAACF,KAAK,CAAC;IAClC,CAAC,MAAM,IAAIxB,QAAQ,KAAK,CAAC,IAAIR,IAAI,EAAE;MACjCmC,YAAY,CAAC,CAAC;IAChB;EACF,CAAC,EAAE,CAAC3B,QAAQ,EAAER,IAAI,CAAC,CAAC,CAAC,CAAC;;EAEtB,MAAM+B,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI;MACFK,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEvC,MAAM,CAAC;MAC7C,MAAMwC,QAAQ,GAAG,MAAM/C,GAAG,CAACgD,IAAI,CAAC,UAAUzC,MAAM,QAAQ,CAAC;MACzDsC,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEC,QAAQ,CAACE,IAAI,CAAC;MAElDvC,OAAO,CAACqC,QAAQ,CAACE,IAAI,CAACxC,IAAI,CAAC;MAC3BG,YAAY,CAACmC,QAAQ,CAACE,IAAI,CAACC,OAAO,CAAC;MACnChC,WAAW,CAAC6B,QAAQ,CAACE,IAAI,CAACxC,IAAI,CAAC0C,QAAQ,GAAG,EAAE,CAAC,CAAC,CAAC;MAC/CzB,gBAAgB,CAAC0B,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;MAC5BjB,oBAAoB,CAACgB,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;;MAEhC;MACA,MAAMC,cAAc,GAAG,CAAC,CAAC;MACzBP,QAAQ,CAACE,IAAI,CAACxC,IAAI,CAAC8C,SAAS,CAACC,OAAO,CAACC,QAAQ,IAAI;QAC/CH,cAAc,CAACG,QAAQ,CAACC,GAAG,CAAC,GAAG;UAC7BD,QAAQ,EAAEA,QAAQ,CAACC,GAAG;UACtBC,eAAe,EAAE,EAAE;UACnBC,MAAM,EAAE;QACV,CAAC;MACH,CAAC,CAAC;MACF9C,UAAU,CAACwC,cAAc,CAAC;;MAE1B;MACA,MAAMO,YAAY,GAAG,CAAC,CAAC;MACvBd,QAAQ,CAACE,IAAI,CAACxC,IAAI,CAAC8C,SAAS,CAACC,OAAO,CAACC,QAAQ,IAAI;QAC/CI,YAAY,CAACJ,QAAQ,CAACC,GAAG,CAAC,GAAG,CAAC;MAChC,CAAC,CAAC;MACF9B,gBAAgB,CAACiC,YAAY,CAAC;MAE9BhB,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;IAC1C,CAAC,CAAC,OAAOzB,KAAK,EAAE;MAAA,IAAAyC,eAAA,EAAAC,oBAAA;MACdlB,OAAO,CAACxB,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5CC,QAAQ,CAAC,EAAAwC,eAAA,GAAAzC,KAAK,CAAC0B,QAAQ,cAAAe,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBb,IAAI,cAAAc,oBAAA,uBAApBA,oBAAA,CAAsBC,OAAO,KAAI,sBAAsB,CAAC;IACnE,CAAC,SAAS;MACR5C,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM6C,kBAAkB,GAAGA,CAACC,UAAU,EAAEC,KAAK,EAAEC,UAAU,GAAG,KAAK,KAAK;IACpEtD,UAAU,CAACuD,IAAI,KAAK;MAClB,GAAGA,IAAI;MACP,CAACH,UAAU,GAAG;QACZ,GAAGG,IAAI,CAACH,UAAU,CAAC;QACnBP,eAAe,EAAES,UAAU,GACtBC,IAAI,CAACH,UAAU,CAAC,CAACP,eAAe,CAACW,QAAQ,CAACH,KAAK,CAAC,GAC7CE,IAAI,CAACH,UAAU,CAAC,CAACP,eAAe,CAACY,MAAM,CAACC,GAAG,IAAIA,GAAG,KAAKL,KAAK,CAAC,GAC7D,CAAC,GAAGE,IAAI,CAACH,UAAU,CAAC,CAACP,eAAe,EAAEQ,KAAK,CAAC,GAChD,CAACA,KAAK;MACZ;IACF,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMvB,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAIrB,UAAU,EAAE;IAEhBC,aAAa,CAAC,IAAI,CAAC;IACnB,IAAI;MACFqB,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEnC,SAAS,CAAC;MACzDkC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEjC,OAAO,CAAC;MAE1C,MAAM4D,YAAY,GAAGC,MAAM,CAACC,MAAM,CAAC9D,OAAO,CAAC;MAC3CgC,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE2B,YAAY,CAAC;MAErD,MAAM1B,QAAQ,GAAG,MAAM/C,GAAG,CAACgD,IAAI,CAAC,kBAAkBrC,SAAS,SAAS,EAAE;QAAEE,OAAO,EAAE4D;MAAa,CAAC,CAAC;MAChG5B,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEC,QAAQ,CAACE,IAAI,CAAC;MAE9CzC,QAAQ,CAAC,YAAYG,SAAS,EAAE,CAAC;IACnC,CAAC,CAAC,OAAOU,KAAK,EAAE;MAAA,IAAAuD,gBAAA,EAAAC,gBAAA,EAAAC,qBAAA;MACdjC,OAAO,CAACxB,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrCwB,OAAO,CAACxB,KAAK,CAAC,iBAAiB,GAAAuD,gBAAA,GAAEvD,KAAK,CAAC0B,QAAQ,cAAA6B,gBAAA,uBAAdA,gBAAA,CAAgB3B,IAAI,CAAC;MACtD3B,QAAQ,CAAC,0BAA0B,EAAAuD,gBAAA,GAAAxD,KAAK,CAAC0B,QAAQ,cAAA8B,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB5B,IAAI,cAAA6B,qBAAA,uBAApBA,qBAAA,CAAsBd,OAAO,KAAI3C,KAAK,CAAC2C,OAAO,EAAE,CAAC;MACpFxC,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAMuD,UAAU,GAAIC,OAAO,IAAK;IAC9B,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;IACrC,MAAMI,IAAI,GAAGJ,OAAO,GAAG,EAAE;IACzB,OAAO,GAAGC,IAAI,IAAIG,IAAI,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EACtD,CAAC;EAED,MAAMC,iBAAiB,GAAIrB,UAAU,IAAK;IACxC,IAAI/B,iBAAiB,IAAI+B,UAAU,EAAE;MACnC,MAAMsB,SAAS,GAAGN,IAAI,CAACC,KAAK,CAAC,CAAC/B,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGlB,iBAAiB,IAAI,IAAI,CAAC;MACrEP,gBAAgB,CAACyC,IAAI,KAAK;QACxB,GAAGA,IAAI;QACP,CAACH,UAAU,GAAG,CAACG,IAAI,CAACH,UAAU,CAAC,IAAI,CAAC,IAAIsB;MAC1C,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAMC,kBAAkB,GAAIC,aAAa,IAAK;IAC5C,IAAIjF,IAAI,IAAIA,IAAI,CAAC8C,SAAS,CAACxC,eAAe,CAAC,EAAE;MAC3CwE,iBAAiB,CAAC9E,IAAI,CAAC8C,SAAS,CAACxC,eAAe,CAAC,CAAC2C,GAAG,CAAC;IACxD;IACA1C,kBAAkB,CAAC0E,aAAa,CAAC;IACjCtD,oBAAoB,CAACgB,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;EAClC,CAAC;EAED,MAAMsC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI,CAAC5D,YAAY,EAAE;MAAA,IAAA6D,qBAAA,EAAAC,sBAAA;MACjB,CAAAD,qBAAA,IAAAC,sBAAA,GAAAC,QAAQ,CAACC,eAAe,EAACC,iBAAiB,cAAAJ,qBAAA,uBAA1CA,qBAAA,CAAAK,IAAA,CAAAJ,sBAA6C,CAAC;MAC9C7D,eAAe,CAAC,IAAI,CAAC;IACvB,CAAC,MAAM;MAAA,IAAAkE,qBAAA,EAAAC,SAAA;MACL,CAAAD,qBAAA,IAAAC,SAAA,GAAAL,QAAQ,EAACM,cAAc,cAAAF,qBAAA,uBAAvBA,qBAAA,CAAAD,IAAA,CAAAE,SAA0B,CAAC;MAC3BnE,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,MAAMqE,eAAe,GAAGA,CAAA,KAAM;IAC5BnE,eAAe,CAACmC,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IACjC,IAAIpC,YAAY,IAAI,CAAC,EAAE;MACrBqE,KAAK,CAAC,yFAAyF,CAAC;MAChG1D,YAAY,CAAC,CAAC;IAChB,CAAC,MAAM;MACL0D,KAAK,CAAC,WAAWrE,YAAY,GAAG,CAAC,8CAA8C,CAAC;IAClF;EACF,CAAC;;EAED;EACApC,SAAS,CAAC,MAAM;IACd,MAAM0G,sBAAsB,GAAGA,CAAA,KAAM;MACnC,IAAIT,QAAQ,CAACU,MAAM,IAAI/F,IAAI,IAAI,CAACc,UAAU,EAAE;QAC1C8E,eAAe,CAAC,CAAC;MACnB;IACF,CAAC;IAEDP,QAAQ,CAACW,gBAAgB,CAAC,kBAAkB,EAAEF,sBAAsB,CAAC;IACrE,OAAO,MAAMT,QAAQ,CAACY,mBAAmB,CAAC,kBAAkB,EAAEH,sBAAsB,CAAC;EACvF,CAAC,EAAE,CAAC9F,IAAI,EAAEc,UAAU,EAAEU,YAAY,CAAC,CAAC;EAEpC,MAAM0E,eAAe,GAAGA,CAAA,KAAM;IAC5B7E,mBAAmB,CAAC,KAAK,CAAC;IAC1BM,oBAAoB,CAACgB,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;EAClC,CAAC;EAED,IAAIlC,OAAO,EAAE,oBAAOjB,OAAA;IAAK0G,SAAS,EAAC,SAAS;IAAAC,QAAA,EAAC;EAAgB;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;EACnE,IAAI5F,KAAK,EAAE,oBAAOnB,OAAA;IAAK0G,SAAS,EAAC,OAAO;IAAAC,QAAA,EAAExF;EAAK;IAAAyF,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EACtD,IAAI,CAACxG,IAAI,EAAE,oBAAOP,OAAA;IAAK0G,SAAS,EAAC,OAAO;IAAAC,QAAA,EAAC;EAAc;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;;EAE7D;EACA,IAAIpF,gBAAgB,EAAE;IAAA,IAAAqF,aAAA;IACpB,oBACEhH,OAAA;MAAK0G,SAAS,EAAC,6BAA6B;MAAAC,QAAA,eAC1C3G,OAAA;QAAK0G,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChC3G,OAAA;UAAA2G,QAAA,EAAI;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAE7B/G,OAAA;UAAK0G,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB3G,OAAA;YAAA2G,QAAA,EAAKpG,IAAI,CAAC0G;UAAK;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACrB/G,OAAA;YAAK0G,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3B3G,OAAA;cAAK0G,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1B3G,OAAA;gBAAM0G,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACvC/G,OAAA;gBAAA2G,QAAA,GAAM,WAAS,GAAAK,aAAA,GAACzG,IAAI,CAAC2G,OAAO,cAAAF,aAAA,uBAAZA,aAAA,CAAcG,IAAI;cAAA;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC,eACN/G,OAAA;cAAK0G,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1B3G,OAAA;gBAAM0G,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACtC/G,OAAA;gBAAA2G,QAAA,GAAM,aAAW,EAACpG,IAAI,CAAC8C,SAAS,CAAC+D,MAAM;cAAA;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC,eACN/G,OAAA;cAAK0G,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1B3G,OAAA;gBAAM0G,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACtC/G,OAAA;gBAAA2G,QAAA,GAAM,YAAU,EAACpG,IAAI,CAAC0C,QAAQ,EAAC,UAAQ;cAAA;gBAAA2D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC,eACN/G,OAAA;cAAK0G,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1B3G,OAAA;gBAAM0G,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACvC/G,OAAA;gBAAA2G,QAAA,GAAM,gBAAc,EAACpG,IAAI,CAAC8C,SAAS,CAACgE,MAAM,CAAC,CAACC,GAAG,EAAEC,CAAC,KAAKD,GAAG,IAAIC,CAAC,CAACC,MAAM,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;cAAA;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN/G,OAAA;UAAK0G,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBACnC3G,OAAA;YAAA2G,QAAA,EAAI;UAAuB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChC/G,OAAA;YAAA2G,QAAA,gBACE3G,OAAA;cAAA2G,QAAA,EAAI;YAA6C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtD/G,OAAA;cAAA2G,QAAA,EAAI;YAA6D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtE/G,OAAA;cAAA2G,QAAA,EAAI;YAAoC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7C/G,OAAA;cAAA2G,QAAA,EAAI;YAA+C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxD/G,OAAA;cAAA2G,QAAA,EAAI;YAA4C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrD/G,OAAA;cAAA2G,QAAA,EAAI;YAAoD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7D/G,OAAA;cAAA2G,QAAA,EAAI;YAAyC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,EAELxG,IAAI,CAACkH,YAAY,iBAChBzH,OAAA;UAAK0G,SAAS,EAAC,uBAAuB;UAAAC,QAAA,gBACpC3G,OAAA;YAAA2G,QAAA,EAAI;UAAwB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjC/G,OAAA;YAAA2G,QAAA,EAAIpG,IAAI,CAACkH;UAAY;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CACN,eAED/G,OAAA;UAAK0G,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5B3G,OAAA;YAAQ0H,OAAO,EAAEjC,gBAAiB;YAACiB,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAAC;UAEjE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT/G,OAAA;YAAQ0H,OAAO,EAAEjB,eAAgB;YAACC,SAAS,EAAC,2BAA2B;YAAAC,QAAA,EAAC;UAExE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAEN/G,OAAA;UAAK0G,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3B3G,OAAA;YAAA2G,QAAA,EAAG;UAAsE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1E,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,MAAMxD,QAAQ,GAAGhD,IAAI,CAAC8C,SAAS,CAACxC,eAAe,CAAC;EAEhD,oBACEb,OAAA;IAAK0G,SAAS,EAAE,yBAAyB7E,YAAY,GAAG,YAAY,GAAG,EAAE,EAAG;IAAA8E,QAAA,gBAE1E3G,OAAA;MAAK0G,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1B3G,OAAA;QAAK0G,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxB3G,OAAA;UAAA2G,QAAA,EAAKpG,IAAI,CAAC0G;QAAK;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACrB/G,OAAA;UAAA2G,QAAA,GAAG,WAAS,EAAC9F,eAAe,GAAG,CAAC,EAAC,MAAI,EAACN,IAAI,CAAC8C,SAAS,CAAC+D,MAAM;QAAA;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7D,CAAC,eAEN/G,OAAA;QAAK0G,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5B3G,OAAA;UAAK0G,SAAS,EAAC,OAAO;UAAAC,QAAA,GAAC,SACnB,EAAC9B,UAAU,CAAC9D,QAAQ,CAAC;QAAA;UAAA6F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,eACN/G,OAAA;UAAQ0H,OAAO,EAAEjC,gBAAiB;UAACiB,SAAS,EAAC,YAAY;UAAAC,QAAA,EACtD9E,YAAY,GAAG,IAAI,GAAG;QAAK;UAAA+E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN/G,OAAA;MAAK0G,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1B3G,OAAA;QAAA2G,QAAA,EAAKpG,IAAI,CAAC0G;MAAK;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACrB/G,OAAA;QAAA2G,QAAA,gBAAG3G,OAAA;UAAA2G,QAAA,EAAQ;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,KAAC,GAAA5G,cAAA,GAACI,IAAI,CAAC2G,OAAO,cAAA/G,cAAA,uBAAZA,cAAA,CAAcgH,IAAI;MAAA;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACrD/G,OAAA;QAAA2G,QAAA,GAAG,WAAS,EAAC9F,eAAe,GAAG,CAAC,EAAC,MAAI,EAACN,IAAI,CAAC8C,SAAS,CAAC+D,MAAM;MAAA;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7D,CAAC,eAGN/G,OAAA;MAAK0G,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC3B3G,OAAA;QAAK0G,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzB3G,OAAA;UAAA2G,QAAA,EAAI;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrB/G,OAAA;UAAK0G,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5B3G,OAAA;YAAM0G,SAAS,EAAC,gBAAgB;YAAAC,QAAA,GAC7BnC,MAAM,CAACC,MAAM,CAAC9D,OAAO,CAAC,CAAC0D,MAAM,CAACsD,CAAC,IAAIA,CAAC,CAAClE,eAAe,CAAC2D,MAAM,GAAG,CAAC,CAAC,CAACA,MAAM,EAAC,WAC3E;UAAA;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACP/G,OAAA;YAAM0G,SAAS,EAAC,aAAa;YAAAC,QAAA,GAAC,KAAG,EAACpG,IAAI,CAAC8C,SAAS,CAAC+D,MAAM;UAAA;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN/G,OAAA;QAAK0G,SAAS,EAAC,UAAU;QAAAC,QAAA,EACtBpG,IAAI,CAAC8C,SAAS,CAACuE,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK,KAAK;UAAA,IAAAC,qBAAA;UAChC,MAAMC,UAAU,GAAG,EAAAD,qBAAA,GAAApH,OAAO,CAACJ,IAAI,CAAC8C,SAAS,CAACyE,KAAK,CAAC,CAACtE,GAAG,CAAC,cAAAuE,qBAAA,uBAAlCA,qBAAA,CAAoCtE,eAAe,CAAC2D,MAAM,IAAG,CAAC;UACjF,MAAM9B,SAAS,GAAG7D,aAAa,CAAClB,IAAI,CAAC8C,SAAS,CAACyE,KAAK,CAAC,CAACtE,GAAG,CAAC,IAAI,CAAC;UAE/D,oBACExD,OAAA;YAEE0G,SAAS,EAAE,WAAWoB,KAAK,KAAKjH,eAAe,GAAG,QAAQ,GAAG,EAAE,IAC7DmH,UAAU,GAAG,UAAU,GAAG,EAAE,EAC3B;YACHN,OAAO,EAAEA,CAAA,KAAMnC,kBAAkB,CAACuC,KAAK,CAAE;YACzCb,KAAK,EAAE,YAAYa,KAAK,GAAG,CAAC,GAAGE,UAAU,GAAG,aAAa,GAAG,EAAE,YAAYhD,IAAI,CAACC,KAAK,CAACK,SAAS,GAAG,EAAE,CAAC,IAAI,CAACA,SAAS,GAAG,EAAE,EAAEH,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAG;YAAAuB,QAAA,gBAEvJ3G,OAAA;cAAM0G,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAEmB,KAAK,GAAG;YAAC;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EACnDiB,UAAU,iBAAIhI,OAAA;cAAM0G,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC5D/G,OAAA;cAAM0G,SAAS,EAAC,gBAAgB;cAAAC,QAAA,GAAE3B,IAAI,CAACC,KAAK,CAACK,SAAS,GAAG,EAAE,CAAC,EAAC,GAAC;YAAA;cAAAsB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA,GAThEe,KAAK;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAUJ,CAAC;QAEb,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEN/G,OAAA;QAAK0G,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzB3G,OAAA;UAAK0G,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B3G,OAAA;YAAK0G,SAAS,EAAC;UAAsB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC5C/G,OAAA;YAAA2G,QAAA,EAAM;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,eACN/G,OAAA;UAAK0G,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B3G,OAAA;YAAK0G,SAAS,EAAC;UAAuB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7C/G,OAAA;YAAA2G,QAAA,EAAM;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC,eACN/G,OAAA;UAAK0G,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B3G,OAAA;YAAK0G,SAAS,EAAC;UAAyB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC/C/G,OAAA;YAAA2G,QAAA,EAAM;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN/G,OAAA;MAAK0G,SAAS,EAAC,oBAAoB;MAAAC,QAAA,gBACjC3G,OAAA;QAAK0G,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9B3G,OAAA;UAAA2G,QAAA,GAAI,WAAS,EAAC9F,eAAe,GAAG,CAAC;QAAA;UAAA+F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACvC/G,OAAA;UAAM0G,SAAS,EAAC,eAAe;UAAAC,QAAA,EAC5BpD,QAAQ,CAAC0E,IAAI,KAAK,KAAK,GAAG,iBAAiB,GAC3C1E,QAAQ,CAAC0E,IAAI,KAAK,YAAY,GAAG,YAAY,GAC7C;QAAiB;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAEN/G,OAAA;QAAK0G,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAEpD,QAAQ,CAAC2E;MAAI;QAAAtB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAEpD/G,OAAA;QAAK0G,SAAS,EAAC,SAAS;QAAAC,QAAA,EACrBpD,QAAQ,CAAC4E,OAAO,CAACP,GAAG,CAAC,CAACQ,MAAM,EAAEN,KAAK,KAAK;UAAA,IAAAO,qBAAA;UACvC,MAAMC,UAAU,IAAAD,qBAAA,GAAG1H,OAAO,CAAC4C,QAAQ,CAACC,GAAG,CAAC,cAAA6E,qBAAA,uBAArBA,qBAAA,CAAuB5E,eAAe,CAACW,QAAQ,CAACgE,MAAM,CAACF,IAAI,CAAC;UAC/E,MAAMK,YAAY,GAAGC,MAAM,CAACC,YAAY,CAAC,EAAE,GAAGX,KAAK,CAAC;UAEpD,oBACE9H,OAAA;YAAiB0G,SAAS,EAAE,UAAU4B,UAAU,GAAG,UAAU,GAAG,EAAE,EAAG;YAAA3B,QAAA,eACnE3G,OAAA;cAAA2G,QAAA,gBACE3G,OAAA;gBACEiI,IAAI,EAAE1E,QAAQ,CAAC0E,IAAI,KAAK,cAAc,GAAG,UAAU,GAAG,OAAQ;gBAC9Dd,IAAI,EAAE,YAAY5D,QAAQ,CAACC,GAAG,EAAG;gBACjCS,KAAK,EAAEmE,MAAM,CAACF,IAAK;gBACnBQ,OAAO,EAAEJ,UAAW;gBACpBK,QAAQ,EAAGC,CAAC,IAAK7E,kBAAkB,CACjCR,QAAQ,CAACC,GAAG,EACZ4E,MAAM,CAACF,IAAI,EACX3E,QAAQ,CAAC0E,IAAI,KAAK,cACpB;cAAE;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACF/G,OAAA;gBAAM0G,SAAS,EAAC,eAAe;gBAAAC,QAAA,GAAE4B,YAAY,EAAC,GAAC;cAAA;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACtD/G,OAAA;gBAAM0G,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAEyB,MAAM,CAACF;cAAI;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C;UAAC,GAfAe,KAAK;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgBV,CAAC;QAEV,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGN/G,OAAA;QAAK0G,SAAS,EAAC,eAAe;QAAAC,QAAA,EAC3B,EAAAvG,sBAAA,GAAAO,OAAO,CAAC4C,QAAQ,CAACC,GAAG,CAAC,cAAApD,sBAAA,uBAArBA,sBAAA,CAAuBqD,eAAe,CAAC2D,MAAM,IAAG,CAAC,gBAChDpH,OAAA;UAAM0G,SAAS,EAAC,UAAU;UAAAC,QAAA,EAAC;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,gBAE5C/G,OAAA;UAAM0G,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAClD;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN/G,OAAA;MAAK0G,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACzB3G,OAAA;QACE0G,SAAS,EAAC,mBAAmB;QAC7BgB,OAAO,EAAEA,CAAA,KAAMnC,kBAAkB,CAACP,IAAI,CAAC6D,GAAG,CAAC,CAAC,EAAEhI,eAAe,GAAG,CAAC,CAAC,CAAE;QACpEiI,QAAQ,EAAEjI,eAAe,KAAK,CAAE;QAAA8F,QAAA,EACjC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAET/G,OAAA;QAAK0G,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzB3G,OAAA;UAAK0G,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5B3G,OAAA;YAAM0G,SAAS,EAAC,WAAW;YAAAC,QAAA,GAAC,GAAC,EAAC9F,eAAe,GAAG,CAAC;UAAA;YAAA+F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACzD/G,OAAA;YAAM0G,SAAS,EAAC,SAAS;YAAAC,QAAA,GAAC,KAAG,EAACpG,IAAI,CAAC8C,SAAS,CAAC+D,MAAM;UAAA;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD,CAAC,eACN/G,OAAA;UAAK0G,SAAS,EAAC,YAAY;UAAAC,QAAA,GAAC,yBACH,EAAC3B,IAAI,CAACC,KAAK,CAAC,CAACxD,aAAa,CAAC8B,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,EAAC,GAAC,EAAC,CAAC,CAAC/B,aAAa,CAAC8B,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE2B,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;QAAA;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAELlG,eAAe,GAAGN,IAAI,CAAC8C,SAAS,CAAC+D,MAAM,GAAG,CAAC,gBAC1CpH,OAAA;QACE0G,SAAS,EAAC,iBAAiB;QAC3BgB,OAAO,EAAEA,CAAA,KAAMnC,kBAAkB,CAAC1E,eAAe,GAAG,CAAC,CAAE;QAAA8F,QAAA,EACxD;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,gBAET/G,OAAA;QACE0G,SAAS,EAAC,iBAAiB;QAC3BgB,OAAO,EAAEhF,YAAa;QACtBoG,QAAQ,EAAEzH,UAAW;QAAAsF,QAAA,EAEpBtF,UAAU,GAAG,eAAe,GAAG;MAAa;QAAAuF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGN/G,OAAA;MAAK0G,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC3B3G,OAAA;QAAA2G,QAAA,EAAI;MAAa;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACtB/G,OAAA;QAAA2G,QAAA,GAAG,YACS,EAACnC,MAAM,CAACC,MAAM,CAAC9D,OAAO,CAAC,CAAC0D,MAAM,CAACsD,CAAC,IAAIA,CAAC,CAAClE,eAAe,CAAC2D,MAAM,GAAG,CAAC,CAAC,CAACA,MAAM,EAAC,KAAG,EAAC7G,IAAI,CAAC8C,SAAS,CAAC+D,MAAM;MAAA;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3G,CAAC,eACJ/G,OAAA;QAAK0G,SAAS,EAAC,cAAc;QAAAC,QAAA,eAC3B3G,OAAA;UACE0G,SAAS,EAAC,UAAU;UACpBqC,KAAK,EAAE;YACLC,KAAK,EAAE,GAAIxE,MAAM,CAACC,MAAM,CAAC9D,OAAO,CAAC,CAAC0D,MAAM,CAACsD,CAAC,IAAIA,CAAC,CAAClE,eAAe,CAAC2D,MAAM,GAAG,CAAC,CAAC,CAACA,MAAM,GAAG7G,IAAI,CAAC8C,SAAS,CAAC+D,MAAM,GAAI,GAAG;UACnH;QAAE;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC7G,EAAA,CA3aID,UAAU;EAAA,QACKL,SAAS,EACXC,WAAW;AAAA;AAAAoJ,EAAA,GAFxBhJ,UAAU;AA6ahB,eAAeA,UAAU;AAAC,IAAAgJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}