{"ast": null, "code": "/** @license React v0.20.2\n * scheduler.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nif (process.env.NODE_ENV !== \"production\") {\n  (function () {\n    'use strict';\n\n    var enableSchedulerDebugging = false;\n    var enableProfiling = false;\n    var requestHostCallback;\n    var requestHostTimeout;\n    var cancelHostTimeout;\n    var requestPaint;\n    var hasPerformanceNow = typeof performance === 'object' && typeof performance.now === 'function';\n    if (hasPerformanceNow) {\n      var localPerformance = performance;\n      exports.unstable_now = function () {\n        return localPerformance.now();\n      };\n    } else {\n      var localDate = Date;\n      var initialTime = localDate.now();\n      exports.unstable_now = function () {\n        return localDate.now() - initialTime;\n      };\n    }\n    if (\n    // If Scheduler runs in a non-DOM environment, it falls back to a naive\n    // implementation using setTimeout.\n    typeof window === 'undefined' ||\n    // Check if MessageChannel is supported, too.\n    typeof MessageChannel !== 'function') {\n      // If this accidentally gets imported in a non-browser environment, e.g. JavaScriptCore,\n      // fallback to a naive implementation.\n      var _callback = null;\n      var _timeoutID = null;\n      var _flushCallback = function () {\n        if (_callback !== null) {\n          try {\n            var currentTime = exports.unstable_now();\n            var hasRemainingTime = true;\n            _callback(hasRemainingTime, currentTime);\n            _callback = null;\n          } catch (e) {\n            setTimeout(_flushCallback, 0);\n            throw e;\n          }\n        }\n      };\n      requestHostCallback = function (cb) {\n        if (_callback !== null) {\n          // Protect against re-entrancy.\n          setTimeout(requestHostCallback, 0, cb);\n        } else {\n          _callback = cb;\n          setTimeout(_flushCallback, 0);\n        }\n      };\n      requestHostTimeout = function (cb, ms) {\n        _timeoutID = setTimeout(cb, ms);\n      };\n      cancelHostTimeout = function () {\n        clearTimeout(_timeoutID);\n      };\n      exports.unstable_shouldYield = function () {\n        return false;\n      };\n      requestPaint = exports.unstable_forceFrameRate = function () {};\n    } else {\n      // Capture local references to native APIs, in case a polyfill overrides them.\n      var _setTimeout = window.setTimeout;\n      var _clearTimeout = window.clearTimeout;\n      if (typeof console !== 'undefined') {\n        // TODO: Scheduler no longer requires these methods to be polyfilled. But\n        // maybe we want to continue warning if they don't exist, to preserve the\n        // option to rely on it in the future?\n        var requestAnimationFrame = window.requestAnimationFrame;\n        var cancelAnimationFrame = window.cancelAnimationFrame;\n        if (typeof requestAnimationFrame !== 'function') {\n          // Using console['error'] to evade Babel and ESLint\n          console['error'](\"This browser doesn't support requestAnimationFrame. \" + 'Make sure that you load a ' + 'polyfill in older browsers. https://reactjs.org/link/react-polyfills');\n        }\n        if (typeof cancelAnimationFrame !== 'function') {\n          // Using console['error'] to evade Babel and ESLint\n          console['error'](\"This browser doesn't support cancelAnimationFrame. \" + 'Make sure that you load a ' + 'polyfill in older browsers. https://reactjs.org/link/react-polyfills');\n        }\n      }\n      var isMessageLoopRunning = false;\n      var scheduledHostCallback = null;\n      var taskTimeoutID = -1; // Scheduler periodically yields in case there is other work on the main\n      // thread, like user events. By default, it yields multiple times per frame.\n      // It does not attempt to align with frame boundaries, since most tasks don't\n      // need to be frame aligned; for those that do, use requestAnimationFrame.\n\n      var yieldInterval = 5;\n      var deadline = 0; // TODO: Make this configurable\n\n      {\n        // `isInputPending` is not available. Since we have no way of knowing if\n        // there's pending input, always yield at the end of the frame.\n        exports.unstable_shouldYield = function () {\n          return exports.unstable_now() >= deadline;\n        }; // Since we yield every frame regardless, `requestPaint` has no effect.\n\n        requestPaint = function () {};\n      }\n      exports.unstable_forceFrameRate = function (fps) {\n        if (fps < 0 || fps > 125) {\n          // Using console['error'] to evade Babel and ESLint\n          console['error']('forceFrameRate takes a positive int between 0 and 125, ' + 'forcing frame rates higher than 125 fps is not supported');\n          return;\n        }\n        if (fps > 0) {\n          yieldInterval = Math.floor(1000 / fps);\n        } else {\n          // reset the framerate\n          yieldInterval = 5;\n        }\n      };\n      var performWorkUntilDeadline = function () {\n        if (scheduledHostCallback !== null) {\n          var currentTime = exports.unstable_now(); // Yield after `yieldInterval` ms, regardless of where we are in the vsync\n          // cycle. This means there's always time remaining at the beginning of\n          // the message event.\n\n          deadline = currentTime + yieldInterval;\n          var hasTimeRemaining = true;\n          try {\n            var hasMoreWork = scheduledHostCallback(hasTimeRemaining, currentTime);\n            if (!hasMoreWork) {\n              isMessageLoopRunning = false;\n              scheduledHostCallback = null;\n            } else {\n              // If there's more work, schedule the next message event at the end\n              // of the preceding one.\n              port.postMessage(null);\n            }\n          } catch (error) {\n            // If a scheduler task throws, exit the current browser task so the\n            // error can be observed.\n            port.postMessage(null);\n            throw error;\n          }\n        } else {\n          isMessageLoopRunning = false;\n        } // Yielding to the browser will give it a chance to paint, so we can\n      };\n      var channel = new MessageChannel();\n      var port = channel.port2;\n      channel.port1.onmessage = performWorkUntilDeadline;\n      requestHostCallback = function (callback) {\n        scheduledHostCallback = callback;\n        if (!isMessageLoopRunning) {\n          isMessageLoopRunning = true;\n          port.postMessage(null);\n        }\n      };\n      requestHostTimeout = function (callback, ms) {\n        taskTimeoutID = _setTimeout(function () {\n          callback(exports.unstable_now());\n        }, ms);\n      };\n      cancelHostTimeout = function () {\n        _clearTimeout(taskTimeoutID);\n        taskTimeoutID = -1;\n      };\n    }\n    function push(heap, node) {\n      var index = heap.length;\n      heap.push(node);\n      siftUp(heap, node, index);\n    }\n    function peek(heap) {\n      var first = heap[0];\n      return first === undefined ? null : first;\n    }\n    function pop(heap) {\n      var first = heap[0];\n      if (first !== undefined) {\n        var last = heap.pop();\n        if (last !== first) {\n          heap[0] = last;\n          siftDown(heap, last, 0);\n        }\n        return first;\n      } else {\n        return null;\n      }\n    }\n    function siftUp(heap, node, i) {\n      var index = i;\n      while (true) {\n        var parentIndex = index - 1 >>> 1;\n        var parent = heap[parentIndex];\n        if (parent !== undefined && compare(parent, node) > 0) {\n          // The parent is larger. Swap positions.\n          heap[parentIndex] = node;\n          heap[index] = parent;\n          index = parentIndex;\n        } else {\n          // The parent is smaller. Exit.\n          return;\n        }\n      }\n    }\n    function siftDown(heap, node, i) {\n      var index = i;\n      var length = heap.length;\n      while (index < length) {\n        var leftIndex = (index + 1) * 2 - 1;\n        var left = heap[leftIndex];\n        var rightIndex = leftIndex + 1;\n        var right = heap[rightIndex]; // If the left or right node is smaller, swap with the smaller of those.\n\n        if (left !== undefined && compare(left, node) < 0) {\n          if (right !== undefined && compare(right, left) < 0) {\n            heap[index] = right;\n            heap[rightIndex] = node;\n            index = rightIndex;\n          } else {\n            heap[index] = left;\n            heap[leftIndex] = node;\n            index = leftIndex;\n          }\n        } else if (right !== undefined && compare(right, node) < 0) {\n          heap[index] = right;\n          heap[rightIndex] = node;\n          index = rightIndex;\n        } else {\n          // Neither child is smaller. Exit.\n          return;\n        }\n      }\n    }\n    function compare(a, b) {\n      // Compare sort index first, then task id.\n      var diff = a.sortIndex - b.sortIndex;\n      return diff !== 0 ? diff : a.id - b.id;\n    }\n\n    // TODO: Use symbols?\n    var ImmediatePriority = 1;\n    var UserBlockingPriority = 2;\n    var NormalPriority = 3;\n    var LowPriority = 4;\n    var IdlePriority = 5;\n    function markTaskErrored(task, ms) {}\n\n    /* eslint-disable no-var */\n    // Math.pow(2, 30) - 1\n    // 0b111111111111111111111111111111\n\n    var maxSigned31BitInt = 1073741823; // Times out immediately\n\n    var IMMEDIATE_PRIORITY_TIMEOUT = -1; // Eventually times out\n\n    var USER_BLOCKING_PRIORITY_TIMEOUT = 250;\n    var NORMAL_PRIORITY_TIMEOUT = 5000;\n    var LOW_PRIORITY_TIMEOUT = 10000; // Never times out\n\n    var IDLE_PRIORITY_TIMEOUT = maxSigned31BitInt; // Tasks are stored on a min heap\n\n    var taskQueue = [];\n    var timerQueue = []; // Incrementing id counter. Used to maintain insertion order.\n\n    var taskIdCounter = 1; // Pausing the scheduler is useful for debugging.\n    var currentTask = null;\n    var currentPriorityLevel = NormalPriority; // This is set while performing work, to prevent re-entrancy.\n\n    var isPerformingWork = false;\n    var isHostCallbackScheduled = false;\n    var isHostTimeoutScheduled = false;\n    function advanceTimers(currentTime) {\n      // Check for tasks that are no longer delayed and add them to the queue.\n      var timer = peek(timerQueue);\n      while (timer !== null) {\n        if (timer.callback === null) {\n          // Timer was cancelled.\n          pop(timerQueue);\n        } else if (timer.startTime <= currentTime) {\n          // Timer fired. Transfer to the task queue.\n          pop(timerQueue);\n          timer.sortIndex = timer.expirationTime;\n          push(taskQueue, timer);\n        } else {\n          // Remaining timers are pending.\n          return;\n        }\n        timer = peek(timerQueue);\n      }\n    }\n    function handleTimeout(currentTime) {\n      isHostTimeoutScheduled = false;\n      advanceTimers(currentTime);\n      if (!isHostCallbackScheduled) {\n        if (peek(taskQueue) !== null) {\n          isHostCallbackScheduled = true;\n          requestHostCallback(flushWork);\n        } else {\n          var firstTimer = peek(timerQueue);\n          if (firstTimer !== null) {\n            requestHostTimeout(handleTimeout, firstTimer.startTime - currentTime);\n          }\n        }\n      }\n    }\n    function flushWork(hasTimeRemaining, initialTime) {\n      isHostCallbackScheduled = false;\n      if (isHostTimeoutScheduled) {\n        // We scheduled a timeout but it's no longer needed. Cancel it.\n        isHostTimeoutScheduled = false;\n        cancelHostTimeout();\n      }\n      isPerformingWork = true;\n      var previousPriorityLevel = currentPriorityLevel;\n      try {\n        if (enableProfiling) {\n          try {\n            return workLoop(hasTimeRemaining, initialTime);\n          } catch (error) {\n            if (currentTask !== null) {\n              var currentTime = exports.unstable_now();\n              markTaskErrored(currentTask, currentTime);\n              currentTask.isQueued = false;\n            }\n            throw error;\n          }\n        } else {\n          // No catch in prod code path.\n          return workLoop(hasTimeRemaining, initialTime);\n        }\n      } finally {\n        currentTask = null;\n        currentPriorityLevel = previousPriorityLevel;\n        isPerformingWork = false;\n      }\n    }\n    function workLoop(hasTimeRemaining, initialTime) {\n      var currentTime = initialTime;\n      advanceTimers(currentTime);\n      currentTask = peek(taskQueue);\n      while (currentTask !== null && !enableSchedulerDebugging) {\n        if (currentTask.expirationTime > currentTime && (!hasTimeRemaining || exports.unstable_shouldYield())) {\n          // This currentTask hasn't expired, and we've reached the deadline.\n          break;\n        }\n        var callback = currentTask.callback;\n        if (typeof callback === 'function') {\n          currentTask.callback = null;\n          currentPriorityLevel = currentTask.priorityLevel;\n          var didUserCallbackTimeout = currentTask.expirationTime <= currentTime;\n          var continuationCallback = callback(didUserCallbackTimeout);\n          currentTime = exports.unstable_now();\n          if (typeof continuationCallback === 'function') {\n            currentTask.callback = continuationCallback;\n          } else {\n            if (currentTask === peek(taskQueue)) {\n              pop(taskQueue);\n            }\n          }\n          advanceTimers(currentTime);\n        } else {\n          pop(taskQueue);\n        }\n        currentTask = peek(taskQueue);\n      } // Return whether there's additional work\n\n      if (currentTask !== null) {\n        return true;\n      } else {\n        var firstTimer = peek(timerQueue);\n        if (firstTimer !== null) {\n          requestHostTimeout(handleTimeout, firstTimer.startTime - currentTime);\n        }\n        return false;\n      }\n    }\n    function unstable_runWithPriority(priorityLevel, eventHandler) {\n      switch (priorityLevel) {\n        case ImmediatePriority:\n        case UserBlockingPriority:\n        case NormalPriority:\n        case LowPriority:\n        case IdlePriority:\n          break;\n        default:\n          priorityLevel = NormalPriority;\n      }\n      var previousPriorityLevel = currentPriorityLevel;\n      currentPriorityLevel = priorityLevel;\n      try {\n        return eventHandler();\n      } finally {\n        currentPriorityLevel = previousPriorityLevel;\n      }\n    }\n    function unstable_next(eventHandler) {\n      var priorityLevel;\n      switch (currentPriorityLevel) {\n        case ImmediatePriority:\n        case UserBlockingPriority:\n        case NormalPriority:\n          // Shift down to normal priority\n          priorityLevel = NormalPriority;\n          break;\n        default:\n          // Anything lower than normal priority should remain at the current level.\n          priorityLevel = currentPriorityLevel;\n          break;\n      }\n      var previousPriorityLevel = currentPriorityLevel;\n      currentPriorityLevel = priorityLevel;\n      try {\n        return eventHandler();\n      } finally {\n        currentPriorityLevel = previousPriorityLevel;\n      }\n    }\n    function unstable_wrapCallback(callback) {\n      var parentPriorityLevel = currentPriorityLevel;\n      return function () {\n        // This is a fork of runWithPriority, inlined for performance.\n        var previousPriorityLevel = currentPriorityLevel;\n        currentPriorityLevel = parentPriorityLevel;\n        try {\n          return callback.apply(this, arguments);\n        } finally {\n          currentPriorityLevel = previousPriorityLevel;\n        }\n      };\n    }\n    function unstable_scheduleCallback(priorityLevel, callback, options) {\n      var currentTime = exports.unstable_now();\n      var startTime;\n      if (typeof options === 'object' && options !== null) {\n        var delay = options.delay;\n        if (typeof delay === 'number' && delay > 0) {\n          startTime = currentTime + delay;\n        } else {\n          startTime = currentTime;\n        }\n      } else {\n        startTime = currentTime;\n      }\n      var timeout;\n      switch (priorityLevel) {\n        case ImmediatePriority:\n          timeout = IMMEDIATE_PRIORITY_TIMEOUT;\n          break;\n        case UserBlockingPriority:\n          timeout = USER_BLOCKING_PRIORITY_TIMEOUT;\n          break;\n        case IdlePriority:\n          timeout = IDLE_PRIORITY_TIMEOUT;\n          break;\n        case LowPriority:\n          timeout = LOW_PRIORITY_TIMEOUT;\n          break;\n        case NormalPriority:\n        default:\n          timeout = NORMAL_PRIORITY_TIMEOUT;\n          break;\n      }\n      var expirationTime = startTime + timeout;\n      var newTask = {\n        id: taskIdCounter++,\n        callback: callback,\n        priorityLevel: priorityLevel,\n        startTime: startTime,\n        expirationTime: expirationTime,\n        sortIndex: -1\n      };\n      if (startTime > currentTime) {\n        // This is a delayed task.\n        newTask.sortIndex = startTime;\n        push(timerQueue, newTask);\n        if (peek(taskQueue) === null && newTask === peek(timerQueue)) {\n          // All tasks are delayed, and this is the task with the earliest delay.\n          if (isHostTimeoutScheduled) {\n            // Cancel an existing timeout.\n            cancelHostTimeout();\n          } else {\n            isHostTimeoutScheduled = true;\n          } // Schedule a timeout.\n\n          requestHostTimeout(handleTimeout, startTime - currentTime);\n        }\n      } else {\n        newTask.sortIndex = expirationTime;\n        push(taskQueue, newTask);\n        // wait until the next time we yield.\n\n        if (!isHostCallbackScheduled && !isPerformingWork) {\n          isHostCallbackScheduled = true;\n          requestHostCallback(flushWork);\n        }\n      }\n      return newTask;\n    }\n    function unstable_pauseExecution() {}\n    function unstable_continueExecution() {\n      if (!isHostCallbackScheduled && !isPerformingWork) {\n        isHostCallbackScheduled = true;\n        requestHostCallback(flushWork);\n      }\n    }\n    function unstable_getFirstCallbackNode() {\n      return peek(taskQueue);\n    }\n    function unstable_cancelCallback(task) {\n      // remove from the queue because you can't remove arbitrary nodes from an\n      // array based heap, only the first one.)\n\n      task.callback = null;\n    }\n    function unstable_getCurrentPriorityLevel() {\n      return currentPriorityLevel;\n    }\n    var unstable_requestPaint = requestPaint;\n    var unstable_Profiling = null;\n    exports.unstable_IdlePriority = IdlePriority;\n    exports.unstable_ImmediatePriority = ImmediatePriority;\n    exports.unstable_LowPriority = LowPriority;\n    exports.unstable_NormalPriority = NormalPriority;\n    exports.unstable_Profiling = unstable_Profiling;\n    exports.unstable_UserBlockingPriority = UserBlockingPriority;\n    exports.unstable_cancelCallback = unstable_cancelCallback;\n    exports.unstable_continueExecution = unstable_continueExecution;\n    exports.unstable_getCurrentPriorityLevel = unstable_getCurrentPriorityLevel;\n    exports.unstable_getFirstCallbackNode = unstable_getFirstCallbackNode;\n    exports.unstable_next = unstable_next;\n    exports.unstable_pauseExecution = unstable_pauseExecution;\n    exports.unstable_requestPaint = unstable_requestPaint;\n    exports.unstable_runWithPriority = unstable_runWithPriority;\n    exports.unstable_scheduleCallback = unstable_scheduleCallback;\n    exports.unstable_wrapCallback = unstable_wrapCallback;\n  })();\n}", "map": {"version": 3, "names": ["process", "env", "NODE_ENV", "enableSchedulerDebugging", "enableProfiling", "requestHostCallback", "requestHostTimeout", "cancelHostTimeout", "requestPaint", "hasPerformanceNow", "performance", "now", "localPerformance", "exports", "unstable_now", "localDate", "Date", "initialTime", "window", "MessageChannel", "_callback", "_timeoutID", "_flushCallback", "currentTime", "hasRemainingTime", "e", "setTimeout", "cb", "ms", "clearTimeout", "unstable_shouldYield", "unstable_forceFrameRate", "_setTimeout", "_clearTimeout", "console", "requestAnimationFrame", "cancelAnimationFrame", "isMessageLoopRunning", "scheduledHostCallback", "taskTimeoutID", "yieldInterval", "deadline", "fps", "Math", "floor", "performWorkUntilDeadline", "hasTimeRemaining", "hasMoreWork", "port", "postMessage", "error", "channel", "port2", "port1", "onmessage", "callback", "push", "heap", "node", "index", "length", "siftUp", "peek", "first", "undefined", "pop", "last", "siftDown", "i", "parentIndex", "parent", "compare", "leftIndex", "left", "rightIndex", "right", "a", "b", "diff", "sortIndex", "id", "ImmediatePriority", "UserBlockingPriority", "NormalPriority", "LowPriority", "IdlePriority", "mark<PERSON><PERSON><PERSON><PERSON>red", "task", "maxSigned31BitInt", "IMMEDIATE_PRIORITY_TIMEOUT", "USER_BLOCKING_PRIORITY_TIMEOUT", "NORMAL_PRIORITY_TIMEOUT", "LOW_PRIORITY_TIMEOUT", "IDLE_PRIORITY_TIMEOUT", "taskQueue", "timerQueue", "taskIdCounter", "currentTask", "currentPriorityLevel", "isPerformingWork", "isHostCallbackScheduled", "isHostTimeoutScheduled", "advanceTimers", "timer", "startTime", "expirationTime", "handleTimeout", "flushWork", "firstTimer", "previousPriorityLevel", "workLoop", "isQueued", "priorityLevel", "didUserCallbackTimeout", "continuationCallback", "unstable_runWithPriority", "<PERSON><PERSON><PERSON><PERSON>", "unstable_next", "unstable_wrapCallback", "parentPriorityLevel", "apply", "arguments", "unstable_scheduleCallback", "options", "delay", "timeout", "newTask", "unstable_pauseExecution", "unstable_continueExecution", "unstable_getFirstCallbackNode", "unstable_cancelCallback", "unstable_getCurrentPriorityLevel", "unstable_requestPaint", "unstable_Profiling", "unstable_IdlePriority", "unstable_ImmediatePriority", "unstable_LowPriority", "unstable_NormalPriority", "unstable_UserBlockingPriority"], "sources": ["/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/node_modules/scheduler/cjs/scheduler.development.js"], "sourcesContent": ["/** @license React v0.20.2\n * scheduler.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nif (process.env.NODE_ENV !== \"production\") {\n  (function() {\n'use strict';\n\nvar enableSchedulerDebugging = false;\nvar enableProfiling = false;\n\nvar requestHostCallback;\nvar requestHostTimeout;\nvar cancelHostTimeout;\nvar requestPaint;\nvar hasPerformanceNow = typeof performance === 'object' && typeof performance.now === 'function';\n\nif (hasPerformanceNow) {\n  var localPerformance = performance;\n\n  exports.unstable_now = function () {\n    return localPerformance.now();\n  };\n} else {\n  var localDate = Date;\n  var initialTime = localDate.now();\n\n  exports.unstable_now = function () {\n    return localDate.now() - initialTime;\n  };\n}\n\nif ( // If Scheduler runs in a non-DOM environment, it falls back to a naive\n// implementation using setTimeout.\ntypeof window === 'undefined' || // Check if MessageChannel is supported, too.\ntypeof MessageChannel !== 'function') {\n  // If this accidentally gets imported in a non-browser environment, e.g. JavaScriptCore,\n  // fallback to a naive implementation.\n  var _callback = null;\n  var _timeoutID = null;\n\n  var _flushCallback = function () {\n    if (_callback !== null) {\n      try {\n        var currentTime = exports.unstable_now();\n        var hasRemainingTime = true;\n\n        _callback(hasRemainingTime, currentTime);\n\n        _callback = null;\n      } catch (e) {\n        setTimeout(_flushCallback, 0);\n        throw e;\n      }\n    }\n  };\n\n  requestHostCallback = function (cb) {\n    if (_callback !== null) {\n      // Protect against re-entrancy.\n      setTimeout(requestHostCallback, 0, cb);\n    } else {\n      _callback = cb;\n      setTimeout(_flushCallback, 0);\n    }\n  };\n\n  requestHostTimeout = function (cb, ms) {\n    _timeoutID = setTimeout(cb, ms);\n  };\n\n  cancelHostTimeout = function () {\n    clearTimeout(_timeoutID);\n  };\n\n  exports.unstable_shouldYield = function () {\n    return false;\n  };\n\n  requestPaint = exports.unstable_forceFrameRate = function () {};\n} else {\n  // Capture local references to native APIs, in case a polyfill overrides them.\n  var _setTimeout = window.setTimeout;\n  var _clearTimeout = window.clearTimeout;\n\n  if (typeof console !== 'undefined') {\n    // TODO: Scheduler no longer requires these methods to be polyfilled. But\n    // maybe we want to continue warning if they don't exist, to preserve the\n    // option to rely on it in the future?\n    var requestAnimationFrame = window.requestAnimationFrame;\n    var cancelAnimationFrame = window.cancelAnimationFrame;\n\n    if (typeof requestAnimationFrame !== 'function') {\n      // Using console['error'] to evade Babel and ESLint\n      console['error'](\"This browser doesn't support requestAnimationFrame. \" + 'Make sure that you load a ' + 'polyfill in older browsers. https://reactjs.org/link/react-polyfills');\n    }\n\n    if (typeof cancelAnimationFrame !== 'function') {\n      // Using console['error'] to evade Babel and ESLint\n      console['error'](\"This browser doesn't support cancelAnimationFrame. \" + 'Make sure that you load a ' + 'polyfill in older browsers. https://reactjs.org/link/react-polyfills');\n    }\n  }\n\n  var isMessageLoopRunning = false;\n  var scheduledHostCallback = null;\n  var taskTimeoutID = -1; // Scheduler periodically yields in case there is other work on the main\n  // thread, like user events. By default, it yields multiple times per frame.\n  // It does not attempt to align with frame boundaries, since most tasks don't\n  // need to be frame aligned; for those that do, use requestAnimationFrame.\n\n  var yieldInterval = 5;\n  var deadline = 0; // TODO: Make this configurable\n\n  {\n    // `isInputPending` is not available. Since we have no way of knowing if\n    // there's pending input, always yield at the end of the frame.\n    exports.unstable_shouldYield = function () {\n      return exports.unstable_now() >= deadline;\n    }; // Since we yield every frame regardless, `requestPaint` has no effect.\n\n\n    requestPaint = function () {};\n  }\n\n  exports.unstable_forceFrameRate = function (fps) {\n    if (fps < 0 || fps > 125) {\n      // Using console['error'] to evade Babel and ESLint\n      console['error']('forceFrameRate takes a positive int between 0 and 125, ' + 'forcing frame rates higher than 125 fps is not supported');\n      return;\n    }\n\n    if (fps > 0) {\n      yieldInterval = Math.floor(1000 / fps);\n    } else {\n      // reset the framerate\n      yieldInterval = 5;\n    }\n  };\n\n  var performWorkUntilDeadline = function () {\n    if (scheduledHostCallback !== null) {\n      var currentTime = exports.unstable_now(); // Yield after `yieldInterval` ms, regardless of where we are in the vsync\n      // cycle. This means there's always time remaining at the beginning of\n      // the message event.\n\n      deadline = currentTime + yieldInterval;\n      var hasTimeRemaining = true;\n\n      try {\n        var hasMoreWork = scheduledHostCallback(hasTimeRemaining, currentTime);\n\n        if (!hasMoreWork) {\n          isMessageLoopRunning = false;\n          scheduledHostCallback = null;\n        } else {\n          // If there's more work, schedule the next message event at the end\n          // of the preceding one.\n          port.postMessage(null);\n        }\n      } catch (error) {\n        // If a scheduler task throws, exit the current browser task so the\n        // error can be observed.\n        port.postMessage(null);\n        throw error;\n      }\n    } else {\n      isMessageLoopRunning = false;\n    } // Yielding to the browser will give it a chance to paint, so we can\n  };\n\n  var channel = new MessageChannel();\n  var port = channel.port2;\n  channel.port1.onmessage = performWorkUntilDeadline;\n\n  requestHostCallback = function (callback) {\n    scheduledHostCallback = callback;\n\n    if (!isMessageLoopRunning) {\n      isMessageLoopRunning = true;\n      port.postMessage(null);\n    }\n  };\n\n  requestHostTimeout = function (callback, ms) {\n    taskTimeoutID = _setTimeout(function () {\n      callback(exports.unstable_now());\n    }, ms);\n  };\n\n  cancelHostTimeout = function () {\n    _clearTimeout(taskTimeoutID);\n\n    taskTimeoutID = -1;\n  };\n}\n\nfunction push(heap, node) {\n  var index = heap.length;\n  heap.push(node);\n  siftUp(heap, node, index);\n}\nfunction peek(heap) {\n  var first = heap[0];\n  return first === undefined ? null : first;\n}\nfunction pop(heap) {\n  var first = heap[0];\n\n  if (first !== undefined) {\n    var last = heap.pop();\n\n    if (last !== first) {\n      heap[0] = last;\n      siftDown(heap, last, 0);\n    }\n\n    return first;\n  } else {\n    return null;\n  }\n}\n\nfunction siftUp(heap, node, i) {\n  var index = i;\n\n  while (true) {\n    var parentIndex = index - 1 >>> 1;\n    var parent = heap[parentIndex];\n\n    if (parent !== undefined && compare(parent, node) > 0) {\n      // The parent is larger. Swap positions.\n      heap[parentIndex] = node;\n      heap[index] = parent;\n      index = parentIndex;\n    } else {\n      // The parent is smaller. Exit.\n      return;\n    }\n  }\n}\n\nfunction siftDown(heap, node, i) {\n  var index = i;\n  var length = heap.length;\n\n  while (index < length) {\n    var leftIndex = (index + 1) * 2 - 1;\n    var left = heap[leftIndex];\n    var rightIndex = leftIndex + 1;\n    var right = heap[rightIndex]; // If the left or right node is smaller, swap with the smaller of those.\n\n    if (left !== undefined && compare(left, node) < 0) {\n      if (right !== undefined && compare(right, left) < 0) {\n        heap[index] = right;\n        heap[rightIndex] = node;\n        index = rightIndex;\n      } else {\n        heap[index] = left;\n        heap[leftIndex] = node;\n        index = leftIndex;\n      }\n    } else if (right !== undefined && compare(right, node) < 0) {\n      heap[index] = right;\n      heap[rightIndex] = node;\n      index = rightIndex;\n    } else {\n      // Neither child is smaller. Exit.\n      return;\n    }\n  }\n}\n\nfunction compare(a, b) {\n  // Compare sort index first, then task id.\n  var diff = a.sortIndex - b.sortIndex;\n  return diff !== 0 ? diff : a.id - b.id;\n}\n\n// TODO: Use symbols?\nvar ImmediatePriority = 1;\nvar UserBlockingPriority = 2;\nvar NormalPriority = 3;\nvar LowPriority = 4;\nvar IdlePriority = 5;\n\nfunction markTaskErrored(task, ms) {\n}\n\n/* eslint-disable no-var */\n// Math.pow(2, 30) - 1\n// 0b111111111111111111111111111111\n\nvar maxSigned31BitInt = 1073741823; // Times out immediately\n\nvar IMMEDIATE_PRIORITY_TIMEOUT = -1; // Eventually times out\n\nvar USER_BLOCKING_PRIORITY_TIMEOUT = 250;\nvar NORMAL_PRIORITY_TIMEOUT = 5000;\nvar LOW_PRIORITY_TIMEOUT = 10000; // Never times out\n\nvar IDLE_PRIORITY_TIMEOUT = maxSigned31BitInt; // Tasks are stored on a min heap\n\nvar taskQueue = [];\nvar timerQueue = []; // Incrementing id counter. Used to maintain insertion order.\n\nvar taskIdCounter = 1; // Pausing the scheduler is useful for debugging.\nvar currentTask = null;\nvar currentPriorityLevel = NormalPriority; // This is set while performing work, to prevent re-entrancy.\n\nvar isPerformingWork = false;\nvar isHostCallbackScheduled = false;\nvar isHostTimeoutScheduled = false;\n\nfunction advanceTimers(currentTime) {\n  // Check for tasks that are no longer delayed and add them to the queue.\n  var timer = peek(timerQueue);\n\n  while (timer !== null) {\n    if (timer.callback === null) {\n      // Timer was cancelled.\n      pop(timerQueue);\n    } else if (timer.startTime <= currentTime) {\n      // Timer fired. Transfer to the task queue.\n      pop(timerQueue);\n      timer.sortIndex = timer.expirationTime;\n      push(taskQueue, timer);\n    } else {\n      // Remaining timers are pending.\n      return;\n    }\n\n    timer = peek(timerQueue);\n  }\n}\n\nfunction handleTimeout(currentTime) {\n  isHostTimeoutScheduled = false;\n  advanceTimers(currentTime);\n\n  if (!isHostCallbackScheduled) {\n    if (peek(taskQueue) !== null) {\n      isHostCallbackScheduled = true;\n      requestHostCallback(flushWork);\n    } else {\n      var firstTimer = peek(timerQueue);\n\n      if (firstTimer !== null) {\n        requestHostTimeout(handleTimeout, firstTimer.startTime - currentTime);\n      }\n    }\n  }\n}\n\nfunction flushWork(hasTimeRemaining, initialTime) {\n\n\n  isHostCallbackScheduled = false;\n\n  if (isHostTimeoutScheduled) {\n    // We scheduled a timeout but it's no longer needed. Cancel it.\n    isHostTimeoutScheduled = false;\n    cancelHostTimeout();\n  }\n\n  isPerformingWork = true;\n  var previousPriorityLevel = currentPriorityLevel;\n\n  try {\n    if (enableProfiling) {\n      try {\n        return workLoop(hasTimeRemaining, initialTime);\n      } catch (error) {\n        if (currentTask !== null) {\n          var currentTime = exports.unstable_now();\n          markTaskErrored(currentTask, currentTime);\n          currentTask.isQueued = false;\n        }\n\n        throw error;\n      }\n    } else {\n      // No catch in prod code path.\n      return workLoop(hasTimeRemaining, initialTime);\n    }\n  } finally {\n    currentTask = null;\n    currentPriorityLevel = previousPriorityLevel;\n    isPerformingWork = false;\n  }\n}\n\nfunction workLoop(hasTimeRemaining, initialTime) {\n  var currentTime = initialTime;\n  advanceTimers(currentTime);\n  currentTask = peek(taskQueue);\n\n  while (currentTask !== null && !(enableSchedulerDebugging )) {\n    if (currentTask.expirationTime > currentTime && (!hasTimeRemaining || exports.unstable_shouldYield())) {\n      // This currentTask hasn't expired, and we've reached the deadline.\n      break;\n    }\n\n    var callback = currentTask.callback;\n\n    if (typeof callback === 'function') {\n      currentTask.callback = null;\n      currentPriorityLevel = currentTask.priorityLevel;\n      var didUserCallbackTimeout = currentTask.expirationTime <= currentTime;\n\n      var continuationCallback = callback(didUserCallbackTimeout);\n      currentTime = exports.unstable_now();\n\n      if (typeof continuationCallback === 'function') {\n        currentTask.callback = continuationCallback;\n      } else {\n\n        if (currentTask === peek(taskQueue)) {\n          pop(taskQueue);\n        }\n      }\n\n      advanceTimers(currentTime);\n    } else {\n      pop(taskQueue);\n    }\n\n    currentTask = peek(taskQueue);\n  } // Return whether there's additional work\n\n\n  if (currentTask !== null) {\n    return true;\n  } else {\n    var firstTimer = peek(timerQueue);\n\n    if (firstTimer !== null) {\n      requestHostTimeout(handleTimeout, firstTimer.startTime - currentTime);\n    }\n\n    return false;\n  }\n}\n\nfunction unstable_runWithPriority(priorityLevel, eventHandler) {\n  switch (priorityLevel) {\n    case ImmediatePriority:\n    case UserBlockingPriority:\n    case NormalPriority:\n    case LowPriority:\n    case IdlePriority:\n      break;\n\n    default:\n      priorityLevel = NormalPriority;\n  }\n\n  var previousPriorityLevel = currentPriorityLevel;\n  currentPriorityLevel = priorityLevel;\n\n  try {\n    return eventHandler();\n  } finally {\n    currentPriorityLevel = previousPriorityLevel;\n  }\n}\n\nfunction unstable_next(eventHandler) {\n  var priorityLevel;\n\n  switch (currentPriorityLevel) {\n    case ImmediatePriority:\n    case UserBlockingPriority:\n    case NormalPriority:\n      // Shift down to normal priority\n      priorityLevel = NormalPriority;\n      break;\n\n    default:\n      // Anything lower than normal priority should remain at the current level.\n      priorityLevel = currentPriorityLevel;\n      break;\n  }\n\n  var previousPriorityLevel = currentPriorityLevel;\n  currentPriorityLevel = priorityLevel;\n\n  try {\n    return eventHandler();\n  } finally {\n    currentPriorityLevel = previousPriorityLevel;\n  }\n}\n\nfunction unstable_wrapCallback(callback) {\n  var parentPriorityLevel = currentPriorityLevel;\n  return function () {\n    // This is a fork of runWithPriority, inlined for performance.\n    var previousPriorityLevel = currentPriorityLevel;\n    currentPriorityLevel = parentPriorityLevel;\n\n    try {\n      return callback.apply(this, arguments);\n    } finally {\n      currentPriorityLevel = previousPriorityLevel;\n    }\n  };\n}\n\nfunction unstable_scheduleCallback(priorityLevel, callback, options) {\n  var currentTime = exports.unstable_now();\n  var startTime;\n\n  if (typeof options === 'object' && options !== null) {\n    var delay = options.delay;\n\n    if (typeof delay === 'number' && delay > 0) {\n      startTime = currentTime + delay;\n    } else {\n      startTime = currentTime;\n    }\n  } else {\n    startTime = currentTime;\n  }\n\n  var timeout;\n\n  switch (priorityLevel) {\n    case ImmediatePriority:\n      timeout = IMMEDIATE_PRIORITY_TIMEOUT;\n      break;\n\n    case UserBlockingPriority:\n      timeout = USER_BLOCKING_PRIORITY_TIMEOUT;\n      break;\n\n    case IdlePriority:\n      timeout = IDLE_PRIORITY_TIMEOUT;\n      break;\n\n    case LowPriority:\n      timeout = LOW_PRIORITY_TIMEOUT;\n      break;\n\n    case NormalPriority:\n    default:\n      timeout = NORMAL_PRIORITY_TIMEOUT;\n      break;\n  }\n\n  var expirationTime = startTime + timeout;\n  var newTask = {\n    id: taskIdCounter++,\n    callback: callback,\n    priorityLevel: priorityLevel,\n    startTime: startTime,\n    expirationTime: expirationTime,\n    sortIndex: -1\n  };\n\n  if (startTime > currentTime) {\n    // This is a delayed task.\n    newTask.sortIndex = startTime;\n    push(timerQueue, newTask);\n\n    if (peek(taskQueue) === null && newTask === peek(timerQueue)) {\n      // All tasks are delayed, and this is the task with the earliest delay.\n      if (isHostTimeoutScheduled) {\n        // Cancel an existing timeout.\n        cancelHostTimeout();\n      } else {\n        isHostTimeoutScheduled = true;\n      } // Schedule a timeout.\n\n\n      requestHostTimeout(handleTimeout, startTime - currentTime);\n    }\n  } else {\n    newTask.sortIndex = expirationTime;\n    push(taskQueue, newTask);\n    // wait until the next time we yield.\n\n\n    if (!isHostCallbackScheduled && !isPerformingWork) {\n      isHostCallbackScheduled = true;\n      requestHostCallback(flushWork);\n    }\n  }\n\n  return newTask;\n}\n\nfunction unstable_pauseExecution() {\n}\n\nfunction unstable_continueExecution() {\n\n  if (!isHostCallbackScheduled && !isPerformingWork) {\n    isHostCallbackScheduled = true;\n    requestHostCallback(flushWork);\n  }\n}\n\nfunction unstable_getFirstCallbackNode() {\n  return peek(taskQueue);\n}\n\nfunction unstable_cancelCallback(task) {\n  // remove from the queue because you can't remove arbitrary nodes from an\n  // array based heap, only the first one.)\n\n\n  task.callback = null;\n}\n\nfunction unstable_getCurrentPriorityLevel() {\n  return currentPriorityLevel;\n}\n\nvar unstable_requestPaint = requestPaint;\nvar unstable_Profiling =  null;\n\nexports.unstable_IdlePriority = IdlePriority;\nexports.unstable_ImmediatePriority = ImmediatePriority;\nexports.unstable_LowPriority = LowPriority;\nexports.unstable_NormalPriority = NormalPriority;\nexports.unstable_Profiling = unstable_Profiling;\nexports.unstable_UserBlockingPriority = UserBlockingPriority;\nexports.unstable_cancelCallback = unstable_cancelCallback;\nexports.unstable_continueExecution = unstable_continueExecution;\nexports.unstable_getCurrentPriorityLevel = unstable_getCurrentPriorityLevel;\nexports.unstable_getFirstCallbackNode = unstable_getFirstCallbackNode;\nexports.unstable_next = unstable_next;\nexports.unstable_pauseExecution = unstable_pauseExecution;\nexports.unstable_requestPaint = unstable_requestPaint;\nexports.unstable_runWithPriority = unstable_runWithPriority;\nexports.unstable_scheduleCallback = unstable_scheduleCallback;\nexports.unstable_wrapCallback = unstable_wrapCallback;\n  })();\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AAEZ,IAAIA,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzC,CAAC,YAAW;IACd,YAAY;;IAEZ,IAAIC,wBAAwB,GAAG,KAAK;IACpC,IAAIC,eAAe,GAAG,KAAK;IAE3B,IAAIC,mBAAmB;IACvB,IAAIC,kBAAkB;IACtB,IAAIC,iBAAiB;IACrB,IAAIC,YAAY;IAChB,IAAIC,iBAAiB,GAAG,OAAOC,WAAW,KAAK,QAAQ,IAAI,OAAOA,WAAW,CAACC,GAAG,KAAK,UAAU;IAEhG,IAAIF,iBAAiB,EAAE;MACrB,IAAIG,gBAAgB,GAAGF,WAAW;MAElCG,OAAO,CAACC,YAAY,GAAG,YAAY;QACjC,OAAOF,gBAAgB,CAACD,GAAG,CAAC,CAAC;MAC/B,CAAC;IACH,CAAC,MAAM;MACL,IAAII,SAAS,GAAGC,IAAI;MACpB,IAAIC,WAAW,GAAGF,SAAS,CAACJ,GAAG,CAAC,CAAC;MAEjCE,OAAO,CAACC,YAAY,GAAG,YAAY;QACjC,OAAOC,SAAS,CAACJ,GAAG,CAAC,CAAC,GAAGM,WAAW;MACtC,CAAC;IACH;IAEA;IAAK;IACL;IACA,OAAOC,MAAM,KAAK,WAAW;IAAI;IACjC,OAAOC,cAAc,KAAK,UAAU,EAAE;MACpC;MACA;MACA,IAAIC,SAAS,GAAG,IAAI;MACpB,IAAIC,UAAU,GAAG,IAAI;MAErB,IAAIC,cAAc,GAAG,SAAAA,CAAA,EAAY;QAC/B,IAAIF,SAAS,KAAK,IAAI,EAAE;UACtB,IAAI;YACF,IAAIG,WAAW,GAAGV,OAAO,CAACC,YAAY,CAAC,CAAC;YACxC,IAAIU,gBAAgB,GAAG,IAAI;YAE3BJ,SAAS,CAACI,gBAAgB,EAAED,WAAW,CAAC;YAExCH,SAAS,GAAG,IAAI;UAClB,CAAC,CAAC,OAAOK,CAAC,EAAE;YACVC,UAAU,CAACJ,cAAc,EAAE,CAAC,CAAC;YAC7B,MAAMG,CAAC;UACT;QACF;MACF,CAAC;MAEDpB,mBAAmB,GAAG,SAAAA,CAAUsB,EAAE,EAAE;QAClC,IAAIP,SAAS,KAAK,IAAI,EAAE;UACtB;UACAM,UAAU,CAACrB,mBAAmB,EAAE,CAAC,EAAEsB,EAAE,CAAC;QACxC,CAAC,MAAM;UACLP,SAAS,GAAGO,EAAE;UACdD,UAAU,CAACJ,cAAc,EAAE,CAAC,CAAC;QAC/B;MACF,CAAC;MAEDhB,kBAAkB,GAAG,SAAAA,CAAUqB,EAAE,EAAEC,EAAE,EAAE;QACrCP,UAAU,GAAGK,UAAU,CAACC,EAAE,EAAEC,EAAE,CAAC;MACjC,CAAC;MAEDrB,iBAAiB,GAAG,SAAAA,CAAA,EAAY;QAC9BsB,YAAY,CAACR,UAAU,CAAC;MAC1B,CAAC;MAEDR,OAAO,CAACiB,oBAAoB,GAAG,YAAY;QACzC,OAAO,KAAK;MACd,CAAC;MAEDtB,YAAY,GAAGK,OAAO,CAACkB,uBAAuB,GAAG,YAAY,CAAC,CAAC;IACjE,CAAC,MAAM;MACL;MACA,IAAIC,WAAW,GAAGd,MAAM,CAACQ,UAAU;MACnC,IAAIO,aAAa,GAAGf,MAAM,CAACW,YAAY;MAEvC,IAAI,OAAOK,OAAO,KAAK,WAAW,EAAE;QAClC;QACA;QACA;QACA,IAAIC,qBAAqB,GAAGjB,MAAM,CAACiB,qBAAqB;QACxD,IAAIC,oBAAoB,GAAGlB,MAAM,CAACkB,oBAAoB;QAEtD,IAAI,OAAOD,qBAAqB,KAAK,UAAU,EAAE;UAC/C;UACAD,OAAO,CAAC,OAAO,CAAC,CAAC,sDAAsD,GAAG,4BAA4B,GAAG,sEAAsE,CAAC;QAClL;QAEA,IAAI,OAAOE,oBAAoB,KAAK,UAAU,EAAE;UAC9C;UACAF,OAAO,CAAC,OAAO,CAAC,CAAC,qDAAqD,GAAG,4BAA4B,GAAG,sEAAsE,CAAC;QACjL;MACF;MAEA,IAAIG,oBAAoB,GAAG,KAAK;MAChC,IAAIC,qBAAqB,GAAG,IAAI;MAChC,IAAIC,aAAa,GAAG,CAAC,CAAC,CAAC,CAAC;MACxB;MACA;MACA;;MAEA,IAAIC,aAAa,GAAG,CAAC;MACrB,IAAIC,QAAQ,GAAG,CAAC,CAAC,CAAC;;MAElB;QACE;QACA;QACA5B,OAAO,CAACiB,oBAAoB,GAAG,YAAY;UACzC,OAAOjB,OAAO,CAACC,YAAY,CAAC,CAAC,IAAI2B,QAAQ;QAC3C,CAAC,CAAC,CAAC;;QAGHjC,YAAY,GAAG,SAAAA,CAAA,EAAY,CAAC,CAAC;MAC/B;MAEAK,OAAO,CAACkB,uBAAuB,GAAG,UAAUW,GAAG,EAAE;QAC/C,IAAIA,GAAG,GAAG,CAAC,IAAIA,GAAG,GAAG,GAAG,EAAE;UACxB;UACAR,OAAO,CAAC,OAAO,CAAC,CAAC,yDAAyD,GAAG,0DAA0D,CAAC;UACxI;QACF;QAEA,IAAIQ,GAAG,GAAG,CAAC,EAAE;UACXF,aAAa,GAAGG,IAAI,CAACC,KAAK,CAAC,IAAI,GAAGF,GAAG,CAAC;QACxC,CAAC,MAAM;UACL;UACAF,aAAa,GAAG,CAAC;QACnB;MACF,CAAC;MAED,IAAIK,wBAAwB,GAAG,SAAAA,CAAA,EAAY;QACzC,IAAIP,qBAAqB,KAAK,IAAI,EAAE;UAClC,IAAIf,WAAW,GAAGV,OAAO,CAACC,YAAY,CAAC,CAAC,CAAC,CAAC;UAC1C;UACA;;UAEA2B,QAAQ,GAAGlB,WAAW,GAAGiB,aAAa;UACtC,IAAIM,gBAAgB,GAAG,IAAI;UAE3B,IAAI;YACF,IAAIC,WAAW,GAAGT,qBAAqB,CAACQ,gBAAgB,EAAEvB,WAAW,CAAC;YAEtE,IAAI,CAACwB,WAAW,EAAE;cAChBV,oBAAoB,GAAG,KAAK;cAC5BC,qBAAqB,GAAG,IAAI;YAC9B,CAAC,MAAM;cACL;cACA;cACAU,IAAI,CAACC,WAAW,CAAC,IAAI,CAAC;YACxB;UACF,CAAC,CAAC,OAAOC,KAAK,EAAE;YACd;YACA;YACAF,IAAI,CAACC,WAAW,CAAC,IAAI,CAAC;YACtB,MAAMC,KAAK;UACb;QACF,CAAC,MAAM;UACLb,oBAAoB,GAAG,KAAK;QAC9B,CAAC,CAAC;MACJ,CAAC;MAED,IAAIc,OAAO,GAAG,IAAIhC,cAAc,CAAC,CAAC;MAClC,IAAI6B,IAAI,GAAGG,OAAO,CAACC,KAAK;MACxBD,OAAO,CAACE,KAAK,CAACC,SAAS,GAAGT,wBAAwB;MAElDxC,mBAAmB,GAAG,SAAAA,CAAUkD,QAAQ,EAAE;QACxCjB,qBAAqB,GAAGiB,QAAQ;QAEhC,IAAI,CAAClB,oBAAoB,EAAE;UACzBA,oBAAoB,GAAG,IAAI;UAC3BW,IAAI,CAACC,WAAW,CAAC,IAAI,CAAC;QACxB;MACF,CAAC;MAED3C,kBAAkB,GAAG,SAAAA,CAAUiD,QAAQ,EAAE3B,EAAE,EAAE;QAC3CW,aAAa,GAAGP,WAAW,CAAC,YAAY;UACtCuB,QAAQ,CAAC1C,OAAO,CAACC,YAAY,CAAC,CAAC,CAAC;QAClC,CAAC,EAAEc,EAAE,CAAC;MACR,CAAC;MAEDrB,iBAAiB,GAAG,SAAAA,CAAA,EAAY;QAC9B0B,aAAa,CAACM,aAAa,CAAC;QAE5BA,aAAa,GAAG,CAAC,CAAC;MACpB,CAAC;IACH;IAEA,SAASiB,IAAIA,CAACC,IAAI,EAAEC,IAAI,EAAE;MACxB,IAAIC,KAAK,GAAGF,IAAI,CAACG,MAAM;MACvBH,IAAI,CAACD,IAAI,CAACE,IAAI,CAAC;MACfG,MAAM,CAACJ,IAAI,EAAEC,IAAI,EAAEC,KAAK,CAAC;IAC3B;IACA,SAASG,IAAIA,CAACL,IAAI,EAAE;MAClB,IAAIM,KAAK,GAAGN,IAAI,CAAC,CAAC,CAAC;MACnB,OAAOM,KAAK,KAAKC,SAAS,GAAG,IAAI,GAAGD,KAAK;IAC3C;IACA,SAASE,GAAGA,CAACR,IAAI,EAAE;MACjB,IAAIM,KAAK,GAAGN,IAAI,CAAC,CAAC,CAAC;MAEnB,IAAIM,KAAK,KAAKC,SAAS,EAAE;QACvB,IAAIE,IAAI,GAAGT,IAAI,CAACQ,GAAG,CAAC,CAAC;QAErB,IAAIC,IAAI,KAAKH,KAAK,EAAE;UAClBN,IAAI,CAAC,CAAC,CAAC,GAAGS,IAAI;UACdC,QAAQ,CAACV,IAAI,EAAES,IAAI,EAAE,CAAC,CAAC;QACzB;QAEA,OAAOH,KAAK;MACd,CAAC,MAAM;QACL,OAAO,IAAI;MACb;IACF;IAEA,SAASF,MAAMA,CAACJ,IAAI,EAAEC,IAAI,EAAEU,CAAC,EAAE;MAC7B,IAAIT,KAAK,GAAGS,CAAC;MAEb,OAAO,IAAI,EAAE;QACX,IAAIC,WAAW,GAAGV,KAAK,GAAG,CAAC,KAAK,CAAC;QACjC,IAAIW,MAAM,GAAGb,IAAI,CAACY,WAAW,CAAC;QAE9B,IAAIC,MAAM,KAAKN,SAAS,IAAIO,OAAO,CAACD,MAAM,EAAEZ,IAAI,CAAC,GAAG,CAAC,EAAE;UACrD;UACAD,IAAI,CAACY,WAAW,CAAC,GAAGX,IAAI;UACxBD,IAAI,CAACE,KAAK,CAAC,GAAGW,MAAM;UACpBX,KAAK,GAAGU,WAAW;QACrB,CAAC,MAAM;UACL;UACA;QACF;MACF;IACF;IAEA,SAASF,QAAQA,CAACV,IAAI,EAAEC,IAAI,EAAEU,CAAC,EAAE;MAC/B,IAAIT,KAAK,GAAGS,CAAC;MACb,IAAIR,MAAM,GAAGH,IAAI,CAACG,MAAM;MAExB,OAAOD,KAAK,GAAGC,MAAM,EAAE;QACrB,IAAIY,SAAS,GAAG,CAACb,KAAK,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC;QACnC,IAAIc,IAAI,GAAGhB,IAAI,CAACe,SAAS,CAAC;QAC1B,IAAIE,UAAU,GAAGF,SAAS,GAAG,CAAC;QAC9B,IAAIG,KAAK,GAAGlB,IAAI,CAACiB,UAAU,CAAC,CAAC,CAAC;;QAE9B,IAAID,IAAI,KAAKT,SAAS,IAAIO,OAAO,CAACE,IAAI,EAAEf,IAAI,CAAC,GAAG,CAAC,EAAE;UACjD,IAAIiB,KAAK,KAAKX,SAAS,IAAIO,OAAO,CAACI,KAAK,EAAEF,IAAI,CAAC,GAAG,CAAC,EAAE;YACnDhB,IAAI,CAACE,KAAK,CAAC,GAAGgB,KAAK;YACnBlB,IAAI,CAACiB,UAAU,CAAC,GAAGhB,IAAI;YACvBC,KAAK,GAAGe,UAAU;UACpB,CAAC,MAAM;YACLjB,IAAI,CAACE,KAAK,CAAC,GAAGc,IAAI;YAClBhB,IAAI,CAACe,SAAS,CAAC,GAAGd,IAAI;YACtBC,KAAK,GAAGa,SAAS;UACnB;QACF,CAAC,MAAM,IAAIG,KAAK,KAAKX,SAAS,IAAIO,OAAO,CAACI,KAAK,EAAEjB,IAAI,CAAC,GAAG,CAAC,EAAE;UAC1DD,IAAI,CAACE,KAAK,CAAC,GAAGgB,KAAK;UACnBlB,IAAI,CAACiB,UAAU,CAAC,GAAGhB,IAAI;UACvBC,KAAK,GAAGe,UAAU;QACpB,CAAC,MAAM;UACL;UACA;QACF;MACF;IACF;IAEA,SAASH,OAAOA,CAACK,CAAC,EAAEC,CAAC,EAAE;MACrB;MACA,IAAIC,IAAI,GAAGF,CAAC,CAACG,SAAS,GAAGF,CAAC,CAACE,SAAS;MACpC,OAAOD,IAAI,KAAK,CAAC,GAAGA,IAAI,GAAGF,CAAC,CAACI,EAAE,GAAGH,CAAC,CAACG,EAAE;IACxC;;IAEA;IACA,IAAIC,iBAAiB,GAAG,CAAC;IACzB,IAAIC,oBAAoB,GAAG,CAAC;IAC5B,IAAIC,cAAc,GAAG,CAAC;IACtB,IAAIC,WAAW,GAAG,CAAC;IACnB,IAAIC,YAAY,GAAG,CAAC;IAEpB,SAASC,eAAeA,CAACC,IAAI,EAAE3D,EAAE,EAAE,CACnC;;IAEA;IACA;IACA;;IAEA,IAAI4D,iBAAiB,GAAG,UAAU,CAAC,CAAC;;IAEpC,IAAIC,0BAA0B,GAAG,CAAC,CAAC,CAAC,CAAC;;IAErC,IAAIC,8BAA8B,GAAG,GAAG;IACxC,IAAIC,uBAAuB,GAAG,IAAI;IAClC,IAAIC,oBAAoB,GAAG,KAAK,CAAC,CAAC;;IAElC,IAAIC,qBAAqB,GAAGL,iBAAiB,CAAC,CAAC;;IAE/C,IAAIM,SAAS,GAAG,EAAE;IAClB,IAAIC,UAAU,GAAG,EAAE,CAAC,CAAC;;IAErB,IAAIC,aAAa,GAAG,CAAC,CAAC,CAAC;IACvB,IAAIC,WAAW,GAAG,IAAI;IACtB,IAAIC,oBAAoB,GAAGf,cAAc,CAAC,CAAC;;IAE3C,IAAIgB,gBAAgB,GAAG,KAAK;IAC5B,IAAIC,uBAAuB,GAAG,KAAK;IACnC,IAAIC,sBAAsB,GAAG,KAAK;IAElC,SAASC,aAAaA,CAAC/E,WAAW,EAAE;MAClC;MACA,IAAIgF,KAAK,GAAGzC,IAAI,CAACiC,UAAU,CAAC;MAE5B,OAAOQ,KAAK,KAAK,IAAI,EAAE;QACrB,IAAIA,KAAK,CAAChD,QAAQ,KAAK,IAAI,EAAE;UAC3B;UACAU,GAAG,CAAC8B,UAAU,CAAC;QACjB,CAAC,MAAM,IAAIQ,KAAK,CAACC,SAAS,IAAIjF,WAAW,EAAE;UACzC;UACA0C,GAAG,CAAC8B,UAAU,CAAC;UACfQ,KAAK,CAACxB,SAAS,GAAGwB,KAAK,CAACE,cAAc;UACtCjD,IAAI,CAACsC,SAAS,EAAES,KAAK,CAAC;QACxB,CAAC,MAAM;UACL;UACA;QACF;QAEAA,KAAK,GAAGzC,IAAI,CAACiC,UAAU,CAAC;MAC1B;IACF;IAEA,SAASW,aAAaA,CAACnF,WAAW,EAAE;MAClC8E,sBAAsB,GAAG,KAAK;MAC9BC,aAAa,CAAC/E,WAAW,CAAC;MAE1B,IAAI,CAAC6E,uBAAuB,EAAE;QAC5B,IAAItC,IAAI,CAACgC,SAAS,CAAC,KAAK,IAAI,EAAE;UAC5BM,uBAAuB,GAAG,IAAI;UAC9B/F,mBAAmB,CAACsG,SAAS,CAAC;QAChC,CAAC,MAAM;UACL,IAAIC,UAAU,GAAG9C,IAAI,CAACiC,UAAU,CAAC;UAEjC,IAAIa,UAAU,KAAK,IAAI,EAAE;YACvBtG,kBAAkB,CAACoG,aAAa,EAAEE,UAAU,CAACJ,SAAS,GAAGjF,WAAW,CAAC;UACvE;QACF;MACF;IACF;IAEA,SAASoF,SAASA,CAAC7D,gBAAgB,EAAE7B,WAAW,EAAE;MAGhDmF,uBAAuB,GAAG,KAAK;MAE/B,IAAIC,sBAAsB,EAAE;QAC1B;QACAA,sBAAsB,GAAG,KAAK;QAC9B9F,iBAAiB,CAAC,CAAC;MACrB;MAEA4F,gBAAgB,GAAG,IAAI;MACvB,IAAIU,qBAAqB,GAAGX,oBAAoB;MAEhD,IAAI;QACF,IAAI9F,eAAe,EAAE;UACnB,IAAI;YACF,OAAO0G,QAAQ,CAAChE,gBAAgB,EAAE7B,WAAW,CAAC;UAChD,CAAC,CAAC,OAAOiC,KAAK,EAAE;YACd,IAAI+C,WAAW,KAAK,IAAI,EAAE;cACxB,IAAI1E,WAAW,GAAGV,OAAO,CAACC,YAAY,CAAC,CAAC;cACxCwE,eAAe,CAACW,WAAW,EAAE1E,WAAW,CAAC;cACzC0E,WAAW,CAACc,QAAQ,GAAG,KAAK;YAC9B;YAEA,MAAM7D,KAAK;UACb;QACF,CAAC,MAAM;UACL;UACA,OAAO4D,QAAQ,CAAChE,gBAAgB,EAAE7B,WAAW,CAAC;QAChD;MACF,CAAC,SAAS;QACRgF,WAAW,GAAG,IAAI;QAClBC,oBAAoB,GAAGW,qBAAqB;QAC5CV,gBAAgB,GAAG,KAAK;MAC1B;IACF;IAEA,SAASW,QAAQA,CAAChE,gBAAgB,EAAE7B,WAAW,EAAE;MAC/C,IAAIM,WAAW,GAAGN,WAAW;MAC7BqF,aAAa,CAAC/E,WAAW,CAAC;MAC1B0E,WAAW,GAAGnC,IAAI,CAACgC,SAAS,CAAC;MAE7B,OAAOG,WAAW,KAAK,IAAI,IAAI,CAAE9F,wBAA0B,EAAE;QAC3D,IAAI8F,WAAW,CAACQ,cAAc,GAAGlF,WAAW,KAAK,CAACuB,gBAAgB,IAAIjC,OAAO,CAACiB,oBAAoB,CAAC,CAAC,CAAC,EAAE;UACrG;UACA;QACF;QAEA,IAAIyB,QAAQ,GAAG0C,WAAW,CAAC1C,QAAQ;QAEnC,IAAI,OAAOA,QAAQ,KAAK,UAAU,EAAE;UAClC0C,WAAW,CAAC1C,QAAQ,GAAG,IAAI;UAC3B2C,oBAAoB,GAAGD,WAAW,CAACe,aAAa;UAChD,IAAIC,sBAAsB,GAAGhB,WAAW,CAACQ,cAAc,IAAIlF,WAAW;UAEtE,IAAI2F,oBAAoB,GAAG3D,QAAQ,CAAC0D,sBAAsB,CAAC;UAC3D1F,WAAW,GAAGV,OAAO,CAACC,YAAY,CAAC,CAAC;UAEpC,IAAI,OAAOoG,oBAAoB,KAAK,UAAU,EAAE;YAC9CjB,WAAW,CAAC1C,QAAQ,GAAG2D,oBAAoB;UAC7C,CAAC,MAAM;YAEL,IAAIjB,WAAW,KAAKnC,IAAI,CAACgC,SAAS,CAAC,EAAE;cACnC7B,GAAG,CAAC6B,SAAS,CAAC;YAChB;UACF;UAEAQ,aAAa,CAAC/E,WAAW,CAAC;QAC5B,CAAC,MAAM;UACL0C,GAAG,CAAC6B,SAAS,CAAC;QAChB;QAEAG,WAAW,GAAGnC,IAAI,CAACgC,SAAS,CAAC;MAC/B,CAAC,CAAC;;MAGF,IAAIG,WAAW,KAAK,IAAI,EAAE;QACxB,OAAO,IAAI;MACb,CAAC,MAAM;QACL,IAAIW,UAAU,GAAG9C,IAAI,CAACiC,UAAU,CAAC;QAEjC,IAAIa,UAAU,KAAK,IAAI,EAAE;UACvBtG,kBAAkB,CAACoG,aAAa,EAAEE,UAAU,CAACJ,SAAS,GAAGjF,WAAW,CAAC;QACvE;QAEA,OAAO,KAAK;MACd;IACF;IAEA,SAAS4F,wBAAwBA,CAACH,aAAa,EAAEI,YAAY,EAAE;MAC7D,QAAQJ,aAAa;QACnB,KAAK/B,iBAAiB;QACtB,KAAKC,oBAAoB;QACzB,KAAKC,cAAc;QACnB,KAAKC,WAAW;QAChB,KAAKC,YAAY;UACf;QAEF;UACE2B,aAAa,GAAG7B,cAAc;MAClC;MAEA,IAAI0B,qBAAqB,GAAGX,oBAAoB;MAChDA,oBAAoB,GAAGc,aAAa;MAEpC,IAAI;QACF,OAAOI,YAAY,CAAC,CAAC;MACvB,CAAC,SAAS;QACRlB,oBAAoB,GAAGW,qBAAqB;MAC9C;IACF;IAEA,SAASQ,aAAaA,CAACD,YAAY,EAAE;MACnC,IAAIJ,aAAa;MAEjB,QAAQd,oBAAoB;QAC1B,KAAKjB,iBAAiB;QACtB,KAAKC,oBAAoB;QACzB,KAAKC,cAAc;UACjB;UACA6B,aAAa,GAAG7B,cAAc;UAC9B;QAEF;UACE;UACA6B,aAAa,GAAGd,oBAAoB;UACpC;MACJ;MAEA,IAAIW,qBAAqB,GAAGX,oBAAoB;MAChDA,oBAAoB,GAAGc,aAAa;MAEpC,IAAI;QACF,OAAOI,YAAY,CAAC,CAAC;MACvB,CAAC,SAAS;QACRlB,oBAAoB,GAAGW,qBAAqB;MAC9C;IACF;IAEA,SAASS,qBAAqBA,CAAC/D,QAAQ,EAAE;MACvC,IAAIgE,mBAAmB,GAAGrB,oBAAoB;MAC9C,OAAO,YAAY;QACjB;QACA,IAAIW,qBAAqB,GAAGX,oBAAoB;QAChDA,oBAAoB,GAAGqB,mBAAmB;QAE1C,IAAI;UACF,OAAOhE,QAAQ,CAACiE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;QACxC,CAAC,SAAS;UACRvB,oBAAoB,GAAGW,qBAAqB;QAC9C;MACF,CAAC;IACH;IAEA,SAASa,yBAAyBA,CAACV,aAAa,EAAEzD,QAAQ,EAAEoE,OAAO,EAAE;MACnE,IAAIpG,WAAW,GAAGV,OAAO,CAACC,YAAY,CAAC,CAAC;MACxC,IAAI0F,SAAS;MAEb,IAAI,OAAOmB,OAAO,KAAK,QAAQ,IAAIA,OAAO,KAAK,IAAI,EAAE;QACnD,IAAIC,KAAK,GAAGD,OAAO,CAACC,KAAK;QAEzB,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,GAAG,CAAC,EAAE;UAC1CpB,SAAS,GAAGjF,WAAW,GAAGqG,KAAK;QACjC,CAAC,MAAM;UACLpB,SAAS,GAAGjF,WAAW;QACzB;MACF,CAAC,MAAM;QACLiF,SAAS,GAAGjF,WAAW;MACzB;MAEA,IAAIsG,OAAO;MAEX,QAAQb,aAAa;QACnB,KAAK/B,iBAAiB;UACpB4C,OAAO,GAAGpC,0BAA0B;UACpC;QAEF,KAAKP,oBAAoB;UACvB2C,OAAO,GAAGnC,8BAA8B;UACxC;QAEF,KAAKL,YAAY;UACfwC,OAAO,GAAGhC,qBAAqB;UAC/B;QAEF,KAAKT,WAAW;UACdyC,OAAO,GAAGjC,oBAAoB;UAC9B;QAEF,KAAKT,cAAc;QACnB;UACE0C,OAAO,GAAGlC,uBAAuB;UACjC;MACJ;MAEA,IAAIc,cAAc,GAAGD,SAAS,GAAGqB,OAAO;MACxC,IAAIC,OAAO,GAAG;QACZ9C,EAAE,EAAEgB,aAAa,EAAE;QACnBzC,QAAQ,EAAEA,QAAQ;QAClByD,aAAa,EAAEA,aAAa;QAC5BR,SAAS,EAAEA,SAAS;QACpBC,cAAc,EAAEA,cAAc;QAC9B1B,SAAS,EAAE,CAAC;MACd,CAAC;MAED,IAAIyB,SAAS,GAAGjF,WAAW,EAAE;QAC3B;QACAuG,OAAO,CAAC/C,SAAS,GAAGyB,SAAS;QAC7BhD,IAAI,CAACuC,UAAU,EAAE+B,OAAO,CAAC;QAEzB,IAAIhE,IAAI,CAACgC,SAAS,CAAC,KAAK,IAAI,IAAIgC,OAAO,KAAKhE,IAAI,CAACiC,UAAU,CAAC,EAAE;UAC5D;UACA,IAAIM,sBAAsB,EAAE;YAC1B;YACA9F,iBAAiB,CAAC,CAAC;UACrB,CAAC,MAAM;YACL8F,sBAAsB,GAAG,IAAI;UAC/B,CAAC,CAAC;;UAGF/F,kBAAkB,CAACoG,aAAa,EAAEF,SAAS,GAAGjF,WAAW,CAAC;QAC5D;MACF,CAAC,MAAM;QACLuG,OAAO,CAAC/C,SAAS,GAAG0B,cAAc;QAClCjD,IAAI,CAACsC,SAAS,EAAEgC,OAAO,CAAC;QACxB;;QAGA,IAAI,CAAC1B,uBAAuB,IAAI,CAACD,gBAAgB,EAAE;UACjDC,uBAAuB,GAAG,IAAI;UAC9B/F,mBAAmB,CAACsG,SAAS,CAAC;QAChC;MACF;MAEA,OAAOmB,OAAO;IAChB;IAEA,SAASC,uBAAuBA,CAAA,EAAG,CACnC;IAEA,SAASC,0BAA0BA,CAAA,EAAG;MAEpC,IAAI,CAAC5B,uBAAuB,IAAI,CAACD,gBAAgB,EAAE;QACjDC,uBAAuB,GAAG,IAAI;QAC9B/F,mBAAmB,CAACsG,SAAS,CAAC;MAChC;IACF;IAEA,SAASsB,6BAA6BA,CAAA,EAAG;MACvC,OAAOnE,IAAI,CAACgC,SAAS,CAAC;IACxB;IAEA,SAASoC,uBAAuBA,CAAC3C,IAAI,EAAE;MACrC;MACA;;MAGAA,IAAI,CAAChC,QAAQ,GAAG,IAAI;IACtB;IAEA,SAAS4E,gCAAgCA,CAAA,EAAG;MAC1C,OAAOjC,oBAAoB;IAC7B;IAEA,IAAIkC,qBAAqB,GAAG5H,YAAY;IACxC,IAAI6H,kBAAkB,GAAI,IAAI;IAE9BxH,OAAO,CAACyH,qBAAqB,GAAGjD,YAAY;IAC5CxE,OAAO,CAAC0H,0BAA0B,GAAGtD,iBAAiB;IACtDpE,OAAO,CAAC2H,oBAAoB,GAAGpD,WAAW;IAC1CvE,OAAO,CAAC4H,uBAAuB,GAAGtD,cAAc;IAChDtE,OAAO,CAACwH,kBAAkB,GAAGA,kBAAkB;IAC/CxH,OAAO,CAAC6H,6BAA6B,GAAGxD,oBAAoB;IAC5DrE,OAAO,CAACqH,uBAAuB,GAAGA,uBAAuB;IACzDrH,OAAO,CAACmH,0BAA0B,GAAGA,0BAA0B;IAC/DnH,OAAO,CAACsH,gCAAgC,GAAGA,gCAAgC;IAC3EtH,OAAO,CAACoH,6BAA6B,GAAGA,6BAA6B;IACrEpH,OAAO,CAACwG,aAAa,GAAGA,aAAa;IACrCxG,OAAO,CAACkH,uBAAuB,GAAGA,uBAAuB;IACzDlH,OAAO,CAACuH,qBAAqB,GAAGA,qBAAqB;IACrDvH,OAAO,CAACsG,wBAAwB,GAAGA,wBAAwB;IAC3DtG,OAAO,CAAC6G,yBAAyB,GAAGA,yBAAyB;IAC7D7G,OAAO,CAACyG,qBAAqB,GAAGA,qBAAqB;EACnD,CAAC,EAAE,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}