{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/App.js\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport Home from './pages/Home';\nimport Login from './pages/Login';\nimport Register from './pages/Register';\nimport ExamList from './pages/ExamList';\nimport ExamTaking from './pages/ExamTaking';\nimport ExamResults from './pages/ExamResults';\nimport StudentDashboard from './pages/StudentDashboard';\nimport AdminLogin from './pages/AdminLogin';\nimport AdminDashboard from './pages/AdminDashboard';\nimport CreateExam from './pages/CreateExam';\nimport Debug from './pages/Debug';\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(Router, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"App\",\n      children: /*#__PURE__*/_jsxDEV(Routes, {\n        children: [/*#__PURE__*/_jsxDEV(Route, {\n          path: \"/\",\n          element: /*#__PURE__*/_jsxDEV(Home, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 21,\n            columnNumber: 36\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/login\",\n          element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 22,\n            columnNumber: 41\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 22,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/register\",\n          element: /*#__PURE__*/_jsxDEV(Register, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 23,\n            columnNumber: 44\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/student\",\n          element: /*#__PURE__*/_jsxDEV(StudentDashboard, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 24,\n            columnNumber: 43\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/exams\",\n          element: /*#__PURE__*/_jsxDEV(ExamList, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 41\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/exam/:examId\",\n          element: /*#__PURE__*/_jsxDEV(ExamTaking, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 26,\n            columnNumber: 48\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/results/:attemptId\",\n          element: /*#__PURE__*/_jsxDEV(ExamResults, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 27,\n            columnNumber: 54\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/admin/login\",\n          element: /*#__PURE__*/_jsxDEV(AdminLogin, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 28,\n            columnNumber: 47\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/admin\",\n          element: /*#__PURE__*/_jsxDEV(AdminDashboard, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 29,\n            columnNumber: 41\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/admin/create-exam\",\n          element: /*#__PURE__*/_jsxDEV(CreateExam, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 30,\n            columnNumber: 53\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/admin/edit-exam/:examId\",\n          element: /*#__PURE__*/_jsxDEV(CreateExam, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 31,\n            columnNumber: 59\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/debug\",\n          element: /*#__PURE__*/_jsxDEV(Debug, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 32,\n            columnNumber: 41\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 18,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Home", "<PERSON><PERSON>", "Register", "ExamList", "ExamTaking", "ExamResults", "StudentDashboard", "AdminLogin", "AdminDashboard", "CreateExam", "Debug", "jsxDEV", "_jsxDEV", "App", "children", "className", "path", "element", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport Home from './pages/Home';\nimport Login from './pages/Login';\nimport Register from './pages/Register';\nimport ExamList from './pages/ExamList';\nimport ExamTaking from './pages/ExamTaking';\nimport ExamResults from './pages/ExamResults';\nimport StudentDashboard from './pages/StudentDashboard';\nimport AdminLogin from './pages/AdminLogin';\nimport AdminDashboard from './pages/AdminDashboard';\nimport CreateExam from './pages/CreateExam';\nimport Debug from './pages/Debug';\nimport './App.css';\n\nfunction App() {\n  return (\n    <Router>\n      <div className=\"App\">\n        <Routes>\n          <Route path=\"/\" element={<Home />} />\n          <Route path=\"/login\" element={<Login />} />\n          <Route path=\"/register\" element={<Register />} />\n          <Route path=\"/student\" element={<StudentDashboard />} />\n          <Route path=\"/exams\" element={<ExamList />} />\n          <Route path=\"/exam/:examId\" element={<ExamTaking />} />\n          <Route path=\"/results/:attemptId\" element={<ExamResults />} />\n          <Route path=\"/admin/login\" element={<AdminLogin />} />\n          <Route path=\"/admin\" element={<AdminDashboard />} />\n          <Route path=\"/admin/create-exam\" element={<CreateExam />} />\n          <Route path=\"/admin/edit-exam/:examId\" element={<CreateExam />} />\n          <Route path=\"/debug\" element={<Debug />} />\n        </Routes>\n      </div>\n    </Router>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AACzE,OAAOC,IAAI,MAAM,cAAc;AAC/B,OAAOC,KAAK,MAAM,eAAe;AACjC,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,UAAU,MAAM,oBAAoB;AAC3C,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,gBAAgB,MAAM,0BAA0B;AACvD,OAAOC,UAAU,MAAM,oBAAoB;AAC3C,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,UAAU,MAAM,oBAAoB;AAC3C,OAAOC,KAAK,MAAM,eAAe;AACjC,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACf,MAAM;IAAAiB,QAAA,eACLF,OAAA;MAAKG,SAAS,EAAC,KAAK;MAAAD,QAAA,eAClBF,OAAA,CAACd,MAAM;QAAAgB,QAAA,gBACLF,OAAA,CAACb,KAAK;UAACiB,IAAI,EAAC,GAAG;UAACC,OAAO,eAAEL,OAAA,CAACZ,IAAI;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACrCT,OAAA,CAACb,KAAK;UAACiB,IAAI,EAAC,QAAQ;UAACC,OAAO,eAAEL,OAAA,CAACX,KAAK;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC3CT,OAAA,CAACb,KAAK;UAACiB,IAAI,EAAC,WAAW;UAACC,OAAO,eAAEL,OAAA,CAACV,QAAQ;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACjDT,OAAA,CAACb,KAAK;UAACiB,IAAI,EAAC,UAAU;UAACC,OAAO,eAAEL,OAAA,CAACN,gBAAgB;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACxDT,OAAA,CAACb,KAAK;UAACiB,IAAI,EAAC,QAAQ;UAACC,OAAO,eAAEL,OAAA,CAACT,QAAQ;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9CT,OAAA,CAACb,KAAK;UAACiB,IAAI,EAAC,eAAe;UAACC,OAAO,eAAEL,OAAA,CAACR,UAAU;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACvDT,OAAA,CAACb,KAAK;UAACiB,IAAI,EAAC,qBAAqB;UAACC,OAAO,eAAEL,OAAA,CAACP,WAAW;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9DT,OAAA,CAACb,KAAK;UAACiB,IAAI,EAAC,cAAc;UAACC,OAAO,eAAEL,OAAA,CAACL,UAAU;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACtDT,OAAA,CAACb,KAAK;UAACiB,IAAI,EAAC,QAAQ;UAACC,OAAO,eAAEL,OAAA,CAACJ,cAAc;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACpDT,OAAA,CAACb,KAAK;UAACiB,IAAI,EAAC,oBAAoB;UAACC,OAAO,eAAEL,OAAA,CAACH,UAAU;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC5DT,OAAA,CAACb,KAAK;UAACiB,IAAI,EAAC,0BAA0B;UAACC,OAAO,eAAEL,OAAA,CAACH,UAAU;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAClET,OAAA,CAACb,KAAK;UAACiB,IAAI,EAAC,QAAQ;UAACC,OAAO,eAAEL,OAAA,CAACF,KAAK;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb;AAACC,EAAA,GArBQT,GAAG;AAuBZ,eAAeA,GAAG;AAAC,IAAAS,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}