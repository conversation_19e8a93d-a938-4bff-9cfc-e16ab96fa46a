{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/ExamResults.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { useParams, Link } from 'react-router-dom';\nimport api from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ExamResults = () => {\n  _s();\n  const {\n    attemptId\n  } = useParams();\n  const [results, setResults] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  useEffect(() => {\n    const fetchResults = async () => {\n      try {\n        console.log('Fetching results for attempt:', attemptId);\n        const response = await api.get(`/exam-attempts/${attemptId}/results`);\n        console.log('Results response:', response.data);\n        setResults(response.data);\n      } catch (error) {\n        var _error$response, _error$response$data;\n        console.error('Failed to fetch results:', error);\n        setError(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Failed to fetch results');\n      } finally {\n        setLoading(false);\n      }\n    };\n    if (attemptId) {\n      fetchResults();\n    }\n  }, [attemptId]);\n  if (loading) return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"loading\",\n    children: \"Loading results...\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 31,\n    columnNumber: 23\n  }, this);\n  if (error) return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"error\",\n    children: error\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 32,\n    columnNumber: 21\n  }, this);\n  if (!results) return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"error\",\n    children: \"No results found\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 33,\n    columnNumber: 24\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      children: \"Exam Results\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"score-summary\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: [\"Score: \", results.score, \"/\", results.totalQuestions]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Percentage:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 12\n        }, this), \" \", results.percentage, \"%\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Time Taken:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 12\n        }, this), \" \", results.timeTaken, \" minutes\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Exam:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 12\n        }, this), \" \", results.examTitle]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 9\n      }, this), results.percentage >= 70 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"success\",\n        children: \"\\uD83C\\uDF89 Congratulations! You passed!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 11\n      }, this) : results.percentage >= 50 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          color: '#ff9800',\n          padding: '10px',\n          background: '#fff3cd',\n          borderRadius: '4px'\n        },\n        children: \"\\u26A0\\uFE0F Average performance. You can do better!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error\",\n        children: \"\\u274C You need more practice. Keep studying!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginTop: '20px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Link, {\n        to: \"/exams\",\n        className: \"btn\",\n        children: \"Take Another Exam\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/\",\n        className: \"btn btn-secondary\",\n        children: \"Home\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 56,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 36,\n    columnNumber: 5\n  }, this);\n};\n_s(ExamResults, \"TZLrADohvS0MoaWayPIEbiBzsjM=\", false, function () {\n  return [useParams];\n});\n_c = ExamResults;\nexport default ExamResults;\nvar _c;\n$RefreshReg$(_c, \"ExamResults\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useParams", "Link", "api", "jsxDEV", "_jsxDEV", "ExamResults", "_s", "attemptId", "results", "setResults", "loading", "setLoading", "error", "setError", "fetchResults", "console", "log", "response", "get", "data", "_error$response", "_error$response$data", "message", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "score", "totalQuestions", "percentage", "timeTaken", "examTitle", "style", "color", "padding", "background", "borderRadius", "marginTop", "to", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/ExamResults.jsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { usePara<PERSON>, Link } from 'react-router-dom';\nimport api from '../services/api';\n\nconst ExamResults = () => {\n  const { attemptId } = useParams();\n  const [results, setResults] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n\n  useEffect(() => {\n    const fetchResults = async () => {\n      try {\n        console.log('Fetching results for attempt:', attemptId);\n        const response = await api.get(`/exam-attempts/${attemptId}/results`);\n        console.log('Results response:', response.data);\n        setResults(response.data);\n      } catch (error) {\n        console.error('Failed to fetch results:', error);\n        setError(error.response?.data?.message || 'Failed to fetch results');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    if (attemptId) {\n      fetchResults();\n    }\n  }, [attemptId]);\n\n  if (loading) return <div className=\"loading\">Loading results...</div>;\n  if (error) return <div className=\"error\">{error}</div>;\n  if (!results) return <div className=\"error\">No results found</div>;\n\n  return (\n    <div className=\"container\">\n      <h1>Exam Results</h1>\n\n      <div className=\"score-summary\">\n        <h2>Score: {results.score}/{results.totalQuestions}</h2>\n        <p><strong>Percentage:</strong> {results.percentage}%</p>\n        <p><strong>Time Taken:</strong> {results.timeTaken} minutes</p>\n        <p><strong>Exam:</strong> {results.examTitle}</p>\n\n        {results.percentage >= 70 ? (\n          <div className=\"success\">🎉 Congratulations! You passed!</div>\n        ) : results.percentage >= 50 ? (\n          <div style={{color: '#ff9800', padding: '10px', background: '#fff3cd', borderRadius: '4px'}}>\n            ⚠️ Average performance. You can do better!\n          </div>\n        ) : (\n          <div className=\"error\">❌ You need more practice. Keep studying!</div>\n        )}\n      </div>\n\n      <div style={{marginTop: '20px'}}>\n        <Link to=\"/exams\" className=\"btn\">Take Another Exam</Link>\n        <Link to=\"/\" className=\"btn btn-secondary\">Home</Link>\n      </div>\n    </div>\n  );\n};\n\nexport default ExamResults;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,IAAI,QAAQ,kBAAkB;AAClD,OAAOC,GAAG,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAM;IAAEC;EAAU,CAAC,GAAGP,SAAS,CAAC,CAAC;EACjC,MAAM,CAACQ,OAAO,EAAEC,UAAU,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACW,OAAO,EAAEC,UAAU,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACa,KAAK,EAAEC,QAAQ,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EAEtCD,SAAS,CAAC,MAAM;IACd,MAAMgB,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC/B,IAAI;QACFC,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAET,SAAS,CAAC;QACvD,MAAMU,QAAQ,GAAG,MAAMf,GAAG,CAACgB,GAAG,CAAC,kBAAkBX,SAAS,UAAU,CAAC;QACrEQ,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEC,QAAQ,CAACE,IAAI,CAAC;QAC/CV,UAAU,CAACQ,QAAQ,CAACE,IAAI,CAAC;MAC3B,CAAC,CAAC,OAAOP,KAAK,EAAE;QAAA,IAAAQ,eAAA,EAAAC,oBAAA;QACdN,OAAO,CAACH,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChDC,QAAQ,CAAC,EAAAO,eAAA,GAAAR,KAAK,CAACK,QAAQ,cAAAG,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBD,IAAI,cAAAE,oBAAA,uBAApBA,oBAAA,CAAsBC,OAAO,KAAI,yBAAyB,CAAC;MACtE,CAAC,SAAS;QACRX,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAED,IAAIJ,SAAS,EAAE;MACbO,YAAY,CAAC,CAAC;IAChB;EACF,CAAC,EAAE,CAACP,SAAS,CAAC,CAAC;EAEf,IAAIG,OAAO,EAAE,oBAAON,OAAA;IAAKmB,SAAS,EAAC,SAAS;IAAAC,QAAA,EAAC;EAAkB;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;EACrE,IAAIhB,KAAK,EAAE,oBAAOR,OAAA;IAAKmB,SAAS,EAAC,OAAO;IAAAC,QAAA,EAAEZ;EAAK;IAAAa,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EACtD,IAAI,CAACpB,OAAO,EAAE,oBAAOJ,OAAA;IAAKmB,SAAS,EAAC,OAAO;IAAAC,QAAA,EAAC;EAAgB;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;EAElE,oBACExB,OAAA;IAAKmB,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxBpB,OAAA;MAAAoB,QAAA,EAAI;IAAY;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAErBxB,OAAA;MAAKmB,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5BpB,OAAA;QAAAoB,QAAA,GAAI,SAAO,EAAChB,OAAO,CAACqB,KAAK,EAAC,GAAC,EAACrB,OAAO,CAACsB,cAAc;MAAA;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACxDxB,OAAA;QAAAoB,QAAA,gBAAGpB,OAAA;UAAAoB,QAAA,EAAQ;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,KAAC,EAACpB,OAAO,CAACuB,UAAU,EAAC,GAAC;MAAA;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACzDxB,OAAA;QAAAoB,QAAA,gBAAGpB,OAAA;UAAAoB,QAAA,EAAQ;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,KAAC,EAACpB,OAAO,CAACwB,SAAS,EAAC,UAAQ;MAAA;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAC/DxB,OAAA;QAAAoB,QAAA,gBAAGpB,OAAA;UAAAoB,QAAA,EAAQ;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,KAAC,EAACpB,OAAO,CAACyB,SAAS;MAAA;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EAEhDpB,OAAO,CAACuB,UAAU,IAAI,EAAE,gBACvB3B,OAAA;QAAKmB,SAAS,EAAC,SAAS;QAAAC,QAAA,EAAC;MAA+B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,GAC5DpB,OAAO,CAACuB,UAAU,IAAI,EAAE,gBAC1B3B,OAAA;QAAK8B,KAAK,EAAE;UAACC,KAAK,EAAE,SAAS;UAAEC,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,SAAS;UAAEC,YAAY,EAAE;QAAK,CAAE;QAAAd,QAAA,EAAC;MAE7F;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,gBAENxB,OAAA;QAAKmB,SAAS,EAAC,OAAO;QAAAC,QAAA,EAAC;MAAwC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CACrE;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAENxB,OAAA;MAAK8B,KAAK,EAAE;QAACK,SAAS,EAAE;MAAM,CAAE;MAAAf,QAAA,gBAC9BpB,OAAA,CAACH,IAAI;QAACuC,EAAE,EAAC,QAAQ;QAACjB,SAAS,EAAC,KAAK;QAAAC,QAAA,EAAC;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC1DxB,OAAA,CAACH,IAAI;QAACuC,EAAE,EAAC,GAAG;QAACjB,SAAS,EAAC,mBAAmB;QAAAC,QAAA,EAAC;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACtB,EAAA,CAzDID,WAAW;EAAA,QACOL,SAAS;AAAA;AAAAyC,EAAA,GAD3BpC,WAAW;AA2DjB,eAAeA,WAAW;AAAC,IAAAoC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}