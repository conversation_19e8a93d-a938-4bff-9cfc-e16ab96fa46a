{"ast": null, "code": "'use strict';\n\nvar call = require('../internals/function-call');\nvar isObject = require('../internals/is-object');\nvar isSymbol = require('../internals/is-symbol');\nvar getMethod = require('../internals/get-method');\nvar ordinaryToPrimitive = require('../internals/ordinary-to-primitive');\nvar wellKnownSymbol = require('../internals/well-known-symbol');\nvar $TypeError = TypeError;\nvar TO_PRIMITIVE = wellKnownSymbol('toPrimitive');\n\n// `ToPrimitive` abstract operation\n// https://tc39.es/ecma262/#sec-toprimitive\nmodule.exports = function (input, pref) {\n  if (!isObject(input) || isSymbol(input)) return input;\n  var exoticToPrim = getMethod(input, TO_PRIMITIVE);\n  var result;\n  if (exoticToPrim) {\n    if (pref === undefined) pref = 'default';\n    result = call(exoticToPrim, input, pref);\n    if (!isObject(result) || isSymbol(result)) return result;\n    throw new $TypeError(\"Can't convert object to primitive value\");\n  }\n  if (pref === undefined) pref = 'number';\n  return ordinaryToPrimitive(input, pref);\n};", "map": {"version": 3, "names": ["call", "require", "isObject", "isSymbol", "getMethod", "ordinaryToPrimitive", "wellKnownSymbol", "$TypeError", "TypeError", "TO_PRIMITIVE", "module", "exports", "input", "pref", "exoticToPrim", "result", "undefined"], "sources": ["/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/node_modules/core-js-pure/internals/to-primitive.js"], "sourcesContent": ["'use strict';\nvar call = require('../internals/function-call');\nvar isObject = require('../internals/is-object');\nvar isSymbol = require('../internals/is-symbol');\nvar getMethod = require('../internals/get-method');\nvar ordinaryToPrimitive = require('../internals/ordinary-to-primitive');\nvar wellKnownSymbol = require('../internals/well-known-symbol');\n\nvar $TypeError = TypeError;\nvar TO_PRIMITIVE = wellKnownSymbol('toPrimitive');\n\n// `ToPrimitive` abstract operation\n// https://tc39.es/ecma262/#sec-toprimitive\nmodule.exports = function (input, pref) {\n  if (!isObject(input) || isSymbol(input)) return input;\n  var exoticToPrim = getMethod(input, TO_PRIMITIVE);\n  var result;\n  if (exoticToPrim) {\n    if (pref === undefined) pref = 'default';\n    result = call(exoticToPrim, input, pref);\n    if (!isObject(result) || isSymbol(result)) return result;\n    throw new $TypeError(\"Can't convert object to primitive value\");\n  }\n  if (pref === undefined) pref = 'number';\n  return ordinaryToPrimitive(input, pref);\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,IAAI,GAAGC,OAAO,CAAC,4BAA4B,CAAC;AAChD,IAAIC,QAAQ,GAAGD,OAAO,CAAC,wBAAwB,CAAC;AAChD,IAAIE,QAAQ,GAAGF,OAAO,CAAC,wBAAwB,CAAC;AAChD,IAAIG,SAAS,GAAGH,OAAO,CAAC,yBAAyB,CAAC;AAClD,IAAII,mBAAmB,GAAGJ,OAAO,CAAC,oCAAoC,CAAC;AACvE,IAAIK,eAAe,GAAGL,OAAO,CAAC,gCAAgC,CAAC;AAE/D,IAAIM,UAAU,GAAGC,SAAS;AAC1B,IAAIC,YAAY,GAAGH,eAAe,CAAC,aAAa,CAAC;;AAEjD;AACA;AACAI,MAAM,CAACC,OAAO,GAAG,UAAUC,KAAK,EAAEC,IAAI,EAAE;EACtC,IAAI,CAACX,QAAQ,CAACU,KAAK,CAAC,IAAIT,QAAQ,CAACS,KAAK,CAAC,EAAE,OAAOA,KAAK;EACrD,IAAIE,YAAY,GAAGV,SAAS,CAACQ,KAAK,EAAEH,YAAY,CAAC;EACjD,IAAIM,MAAM;EACV,IAAID,YAAY,EAAE;IAChB,IAAID,IAAI,KAAKG,SAAS,EAAEH,IAAI,GAAG,SAAS;IACxCE,MAAM,GAAGf,IAAI,CAACc,YAAY,EAAEF,KAAK,EAAEC,IAAI,CAAC;IACxC,IAAI,CAACX,QAAQ,CAACa,MAAM,CAAC,IAAIZ,QAAQ,CAACY,MAAM,CAAC,EAAE,OAAOA,MAAM;IACxD,MAAM,IAAIR,UAAU,CAAC,yCAAyC,CAAC;EACjE;EACA,IAAIM,IAAI,KAAKG,SAAS,EAAEH,IAAI,GAAG,QAAQ;EACvC,OAAOR,mBAAmB,CAACO,KAAK,EAAEC,IAAI,CAAC;AACzC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}