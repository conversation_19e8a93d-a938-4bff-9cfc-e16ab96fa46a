{"ast": null, "code": "'use strict';\n\nvar utils = require('./utils');\nvar normalizeHeaderName = require('./helpers/normalizeHeaderName');\nvar enhanceError = require('./core/enhanceError');\nvar DEFAULT_CONTENT_TYPE = {\n  'Content-Type': 'application/x-www-form-urlencoded'\n};\nfunction setContentTypeIfUnset(headers, value) {\n  if (!utils.isUndefined(headers) && utils.isUndefined(headers['Content-Type'])) {\n    headers['Content-Type'] = value;\n  }\n}\nfunction getDefaultAdapter() {\n  var adapter;\n  if (typeof XMLHttpRequest !== 'undefined') {\n    // For browsers use XHR adapter\n    adapter = require('./adapters/xhr');\n  } else if (typeof process !== 'undefined' && Object.prototype.toString.call(process) === '[object process]') {\n    // For node use HTTP adapter\n    adapter = require('./adapters/http');\n  }\n  return adapter;\n}\nfunction stringifySafely(rawValue, parser, encoder) {\n  if (utils.isString(rawValue)) {\n    try {\n      (parser || JSON.parse)(rawValue);\n      return utils.trim(rawValue);\n    } catch (e) {\n      if (e.name !== 'SyntaxError') {\n        throw e;\n      }\n    }\n  }\n  return (encoder || JSON.stringify)(rawValue);\n}\nvar defaults = {\n  transitional: {\n    silentJSONParsing: true,\n    forcedJSONParsing: true,\n    clarifyTimeoutError: false\n  },\n  adapter: getDefaultAdapter(),\n  transformRequest: [function transformRequest(data, headers) {\n    normalizeHeaderName(headers, 'Accept');\n    normalizeHeaderName(headers, 'Content-Type');\n    if (utils.isFormData(data) || utils.isArrayBuffer(data) || utils.isBuffer(data) || utils.isStream(data) || utils.isFile(data) || utils.isBlob(data)) {\n      return data;\n    }\n    if (utils.isArrayBufferView(data)) {\n      return data.buffer;\n    }\n    if (utils.isURLSearchParams(data)) {\n      setContentTypeIfUnset(headers, 'application/x-www-form-urlencoded;charset=utf-8');\n      return data.toString();\n    }\n    if (utils.isObject(data) || headers && headers['Content-Type'] === 'application/json') {\n      setContentTypeIfUnset(headers, 'application/json');\n      return stringifySafely(data);\n    }\n    return data;\n  }],\n  transformResponse: [function transformResponse(data) {\n    var transitional = this.transitional || defaults.transitional;\n    var silentJSONParsing = transitional && transitional.silentJSONParsing;\n    var forcedJSONParsing = transitional && transitional.forcedJSONParsing;\n    var strictJSONParsing = !silentJSONParsing && this.responseType === 'json';\n    if (strictJSONParsing || forcedJSONParsing && utils.isString(data) && data.length) {\n      try {\n        return JSON.parse(data);\n      } catch (e) {\n        if (strictJSONParsing) {\n          if (e.name === 'SyntaxError') {\n            throw enhanceError(e, this, 'E_JSON_PARSE');\n          }\n          throw e;\n        }\n      }\n    }\n    return data;\n  }],\n  /**\n   * A timeout in milliseconds to abort a request. If set to 0 (default) a\n   * timeout is not created.\n   */\n  timeout: 0,\n  xsrfCookieName: 'XSRF-TOKEN',\n  xsrfHeaderName: 'X-XSRF-TOKEN',\n  maxContentLength: -1,\n  maxBodyLength: -1,\n  validateStatus: function validateStatus(status) {\n    return status >= 200 && status < 300;\n  },\n  headers: {\n    common: {\n      'Accept': 'application/json, text/plain, */*'\n    }\n  }\n};\nutils.forEach(['delete', 'get', 'head'], function forEachMethodNoData(method) {\n  defaults.headers[method] = {};\n});\nutils.forEach(['post', 'put', 'patch'], function forEachMethodWithData(method) {\n  defaults.headers[method] = utils.merge(DEFAULT_CONTENT_TYPE);\n});\nmodule.exports = defaults;", "map": {"version": 3, "names": ["utils", "require", "normalizeHeaderName", "enhanceError", "DEFAULT_CONTENT_TYPE", "setContentTypeIfUnset", "headers", "value", "isUndefined", "getDefaultAdapter", "adapter", "XMLHttpRequest", "process", "Object", "prototype", "toString", "call", "stringifySafely", "rawValue", "parser", "encoder", "isString", "JSON", "parse", "trim", "e", "name", "stringify", "defaults", "transitional", "silentJSONParsing", "forcedJSONParsing", "clarifyTimeoutError", "transformRequest", "data", "isFormData", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "isStream", "isFile", "isBlob", "isArrayBuffer<PERSON>iew", "buffer", "isURLSearchParams", "isObject", "transformResponse", "strictJSONParsing", "responseType", "length", "timeout", "xsrfCookieName", "xsrfHeaderName", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "validateStatus", "status", "common", "for<PERSON>ach", "forEachMethodNoData", "method", "forEachMethodWithData", "merge", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/node_modules/axios/lib/defaults.js"], "sourcesContent": ["'use strict';\n\nvar utils = require('./utils');\nvar normalizeHeaderName = require('./helpers/normalizeHeaderName');\nvar enhanceError = require('./core/enhanceError');\n\nvar DEFAULT_CONTENT_TYPE = {\n  'Content-Type': 'application/x-www-form-urlencoded'\n};\n\nfunction setContentTypeIfUnset(headers, value) {\n  if (!utils.isUndefined(headers) && utils.isUndefined(headers['Content-Type'])) {\n    headers['Content-Type'] = value;\n  }\n}\n\nfunction getDefaultAdapter() {\n  var adapter;\n  if (typeof XMLHttpRequest !== 'undefined') {\n    // For browsers use XHR adapter\n    adapter = require('./adapters/xhr');\n  } else if (typeof process !== 'undefined' && Object.prototype.toString.call(process) === '[object process]') {\n    // For node use HTTP adapter\n    adapter = require('./adapters/http');\n  }\n  return adapter;\n}\n\nfunction stringifySafely(rawValue, parser, encoder) {\n  if (utils.isString(rawValue)) {\n    try {\n      (parser || JSON.parse)(rawValue);\n      return utils.trim(rawValue);\n    } catch (e) {\n      if (e.name !== 'SyntaxError') {\n        throw e;\n      }\n    }\n  }\n\n  return (encoder || JSON.stringify)(rawValue);\n}\n\nvar defaults = {\n\n  transitional: {\n    silentJSONParsing: true,\n    forcedJSONParsing: true,\n    clarifyTimeoutError: false\n  },\n\n  adapter: getDefaultAdapter(),\n\n  transformRequest: [function transformRequest(data, headers) {\n    normalizeHeaderName(headers, 'Accept');\n    normalizeHeaderName(headers, 'Content-Type');\n\n    if (utils.isFormData(data) ||\n      utils.isArrayBuffer(data) ||\n      utils.isBuffer(data) ||\n      utils.isStream(data) ||\n      utils.isFile(data) ||\n      utils.isBlob(data)\n    ) {\n      return data;\n    }\n    if (utils.isArrayBufferView(data)) {\n      return data.buffer;\n    }\n    if (utils.isURLSearchParams(data)) {\n      setContentTypeIfUnset(headers, 'application/x-www-form-urlencoded;charset=utf-8');\n      return data.toString();\n    }\n    if (utils.isObject(data) || (headers && headers['Content-Type'] === 'application/json')) {\n      setContentTypeIfUnset(headers, 'application/json');\n      return stringifySafely(data);\n    }\n    return data;\n  }],\n\n  transformResponse: [function transformResponse(data) {\n    var transitional = this.transitional || defaults.transitional;\n    var silentJSONParsing = transitional && transitional.silentJSONParsing;\n    var forcedJSONParsing = transitional && transitional.forcedJSONParsing;\n    var strictJSONParsing = !silentJSONParsing && this.responseType === 'json';\n\n    if (strictJSONParsing || (forcedJSONParsing && utils.isString(data) && data.length)) {\n      try {\n        return JSON.parse(data);\n      } catch (e) {\n        if (strictJSONParsing) {\n          if (e.name === 'SyntaxError') {\n            throw enhanceError(e, this, 'E_JSON_PARSE');\n          }\n          throw e;\n        }\n      }\n    }\n\n    return data;\n  }],\n\n  /**\n   * A timeout in milliseconds to abort a request. If set to 0 (default) a\n   * timeout is not created.\n   */\n  timeout: 0,\n\n  xsrfCookieName: 'XSRF-TOKEN',\n  xsrfHeaderName: 'X-XSRF-TOKEN',\n\n  maxContentLength: -1,\n  maxBodyLength: -1,\n\n  validateStatus: function validateStatus(status) {\n    return status >= 200 && status < 300;\n  },\n\n  headers: {\n    common: {\n      'Accept': 'application/json, text/plain, */*'\n    }\n  }\n};\n\nutils.forEach(['delete', 'get', 'head'], function forEachMethodNoData(method) {\n  defaults.headers[method] = {};\n});\n\nutils.forEach(['post', 'put', 'patch'], function forEachMethodWithData(method) {\n  defaults.headers[method] = utils.merge(DEFAULT_CONTENT_TYPE);\n});\n\nmodule.exports = defaults;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,KAAK,GAAGC,OAAO,CAAC,SAAS,CAAC;AAC9B,IAAIC,mBAAmB,GAAGD,OAAO,CAAC,+BAA+B,CAAC;AAClE,IAAIE,YAAY,GAAGF,OAAO,CAAC,qBAAqB,CAAC;AAEjD,IAAIG,oBAAoB,GAAG;EACzB,cAAc,EAAE;AAClB,CAAC;AAED,SAASC,qBAAqBA,CAACC,OAAO,EAAEC,KAAK,EAAE;EAC7C,IAAI,CAACP,KAAK,CAACQ,WAAW,CAACF,OAAO,CAAC,IAAIN,KAAK,CAACQ,WAAW,CAACF,OAAO,CAAC,cAAc,CAAC,CAAC,EAAE;IAC7EA,OAAO,CAAC,cAAc,CAAC,GAAGC,KAAK;EACjC;AACF;AAEA,SAASE,iBAAiBA,CAAA,EAAG;EAC3B,IAAIC,OAAO;EACX,IAAI,OAAOC,cAAc,KAAK,WAAW,EAAE;IACzC;IACAD,OAAO,GAAGT,OAAO,CAAC,gBAAgB,CAAC;EACrC,CAAC,MAAM,IAAI,OAAOW,OAAO,KAAK,WAAW,IAAIC,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACJ,OAAO,CAAC,KAAK,kBAAkB,EAAE;IAC3G;IACAF,OAAO,GAAGT,OAAO,CAAC,iBAAiB,CAAC;EACtC;EACA,OAAOS,OAAO;AAChB;AAEA,SAASO,eAAeA,CAACC,QAAQ,EAAEC,MAAM,EAAEC,OAAO,EAAE;EAClD,IAAIpB,KAAK,CAACqB,QAAQ,CAACH,QAAQ,CAAC,EAAE;IAC5B,IAAI;MACF,CAACC,MAAM,IAAIG,IAAI,CAACC,KAAK,EAAEL,QAAQ,CAAC;MAChC,OAAOlB,KAAK,CAACwB,IAAI,CAACN,QAAQ,CAAC;IAC7B,CAAC,CAAC,OAAOO,CAAC,EAAE;MACV,IAAIA,CAAC,CAACC,IAAI,KAAK,aAAa,EAAE;QAC5B,MAAMD,CAAC;MACT;IACF;EACF;EAEA,OAAO,CAACL,OAAO,IAAIE,IAAI,CAACK,SAAS,EAAET,QAAQ,CAAC;AAC9C;AAEA,IAAIU,QAAQ,GAAG;EAEbC,YAAY,EAAE;IACZC,iBAAiB,EAAE,IAAI;IACvBC,iBAAiB,EAAE,IAAI;IACvBC,mBAAmB,EAAE;EACvB,CAAC;EAEDtB,OAAO,EAAED,iBAAiB,CAAC,CAAC;EAE5BwB,gBAAgB,EAAE,CAAC,SAASA,gBAAgBA,CAACC,IAAI,EAAE5B,OAAO,EAAE;IAC1DJ,mBAAmB,CAACI,OAAO,EAAE,QAAQ,CAAC;IACtCJ,mBAAmB,CAACI,OAAO,EAAE,cAAc,CAAC;IAE5C,IAAIN,KAAK,CAACmC,UAAU,CAACD,IAAI,CAAC,IACxBlC,KAAK,CAACoC,aAAa,CAACF,IAAI,CAAC,IACzBlC,KAAK,CAACqC,QAAQ,CAACH,IAAI,CAAC,IACpBlC,KAAK,CAACsC,QAAQ,CAACJ,IAAI,CAAC,IACpBlC,KAAK,CAACuC,MAAM,CAACL,IAAI,CAAC,IAClBlC,KAAK,CAACwC,MAAM,CAACN,IAAI,CAAC,EAClB;MACA,OAAOA,IAAI;IACb;IACA,IAAIlC,KAAK,CAACyC,iBAAiB,CAACP,IAAI,CAAC,EAAE;MACjC,OAAOA,IAAI,CAACQ,MAAM;IACpB;IACA,IAAI1C,KAAK,CAAC2C,iBAAiB,CAACT,IAAI,CAAC,EAAE;MACjC7B,qBAAqB,CAACC,OAAO,EAAE,iDAAiD,CAAC;MACjF,OAAO4B,IAAI,CAACnB,QAAQ,CAAC,CAAC;IACxB;IACA,IAAIf,KAAK,CAAC4C,QAAQ,CAACV,IAAI,CAAC,IAAK5B,OAAO,IAAIA,OAAO,CAAC,cAAc,CAAC,KAAK,kBAAmB,EAAE;MACvFD,qBAAqB,CAACC,OAAO,EAAE,kBAAkB,CAAC;MAClD,OAAOW,eAAe,CAACiB,IAAI,CAAC;IAC9B;IACA,OAAOA,IAAI;EACb,CAAC,CAAC;EAEFW,iBAAiB,EAAE,CAAC,SAASA,iBAAiBA,CAACX,IAAI,EAAE;IACnD,IAAIL,YAAY,GAAG,IAAI,CAACA,YAAY,IAAID,QAAQ,CAACC,YAAY;IAC7D,IAAIC,iBAAiB,GAAGD,YAAY,IAAIA,YAAY,CAACC,iBAAiB;IACtE,IAAIC,iBAAiB,GAAGF,YAAY,IAAIA,YAAY,CAACE,iBAAiB;IACtE,IAAIe,iBAAiB,GAAG,CAAChB,iBAAiB,IAAI,IAAI,CAACiB,YAAY,KAAK,MAAM;IAE1E,IAAID,iBAAiB,IAAKf,iBAAiB,IAAI/B,KAAK,CAACqB,QAAQ,CAACa,IAAI,CAAC,IAAIA,IAAI,CAACc,MAAO,EAAE;MACnF,IAAI;QACF,OAAO1B,IAAI,CAACC,KAAK,CAACW,IAAI,CAAC;MACzB,CAAC,CAAC,OAAOT,CAAC,EAAE;QACV,IAAIqB,iBAAiB,EAAE;UACrB,IAAIrB,CAAC,CAACC,IAAI,KAAK,aAAa,EAAE;YAC5B,MAAMvB,YAAY,CAACsB,CAAC,EAAE,IAAI,EAAE,cAAc,CAAC;UAC7C;UACA,MAAMA,CAAC;QACT;MACF;IACF;IAEA,OAAOS,IAAI;EACb,CAAC,CAAC;EAEF;AACF;AACA;AACA;EACEe,OAAO,EAAE,CAAC;EAEVC,cAAc,EAAE,YAAY;EAC5BC,cAAc,EAAE,cAAc;EAE9BC,gBAAgB,EAAE,CAAC,CAAC;EACpBC,aAAa,EAAE,CAAC,CAAC;EAEjBC,cAAc,EAAE,SAASA,cAAcA,CAACC,MAAM,EAAE;IAC9C,OAAOA,MAAM,IAAI,GAAG,IAAIA,MAAM,GAAG,GAAG;EACtC,CAAC;EAEDjD,OAAO,EAAE;IACPkD,MAAM,EAAE;MACN,QAAQ,EAAE;IACZ;EACF;AACF,CAAC;AAEDxD,KAAK,CAACyD,OAAO,CAAC,CAAC,QAAQ,EAAE,KAAK,EAAE,MAAM,CAAC,EAAE,SAASC,mBAAmBA,CAACC,MAAM,EAAE;EAC5E/B,QAAQ,CAACtB,OAAO,CAACqD,MAAM,CAAC,GAAG,CAAC,CAAC;AAC/B,CAAC,CAAC;AAEF3D,KAAK,CAACyD,OAAO,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,EAAE,SAASG,qBAAqBA,CAACD,MAAM,EAAE;EAC7E/B,QAAQ,CAACtB,OAAO,CAACqD,MAAM,CAAC,GAAG3D,KAAK,CAAC6D,KAAK,CAACzD,oBAAoB,CAAC;AAC9D,CAAC,CAAC;AAEF0D,MAAM,CAACC,OAAO,GAAGnC,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}