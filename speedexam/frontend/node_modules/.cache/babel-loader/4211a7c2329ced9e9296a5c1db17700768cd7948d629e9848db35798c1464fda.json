{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/ExamList.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport api from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ExamList = () => {\n  _s();\n  const [exams, setExams] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const navigate = useNavigate();\n  useEffect(() => {\n    const token = localStorage.getItem('token');\n    if (!token) {\n      navigate('/login');\n      return;\n    }\n    fetchExams();\n  }, [navigate]);\n  const fetchExams = async () => {\n    try {\n      console.log('Fetching exams from API...');\n      const response = await api.get('/exams');\n      console.log('Exams fetched successfully:', response.data);\n      setExams(response.data);\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('Error fetching exams:', error);\n      setError('Failed to fetch exams: ' + (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || error.message));\n    } finally {\n      setLoading(false);\n    }\n  };\n  if (loading) return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"loading\",\n    children: \"Loading exams...\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 35,\n    columnNumber: 23\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Available Exams\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/\",\n        className: \"btn btn-secondary\",\n        children: \"Home\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 17\n    }, this), exams.length === 0 ? /*#__PURE__*/_jsxDEV(\"p\", {\n      children: \"No exams available at the moment.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      children: exams.map(exam => {\n        var _exam$subject, _exam$questions;\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"exam-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: exam.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: exam.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Subject:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 54,\n              columnNumber: 18\n            }, this), \" \", (_exam$subject = exam.subject) === null || _exam$subject === void 0 ? void 0 : _exam$subject.name]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 54,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Duration:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 55,\n              columnNumber: 18\n            }, this), \" \", exam.duration, \" minutes\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 55,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Questions:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 56,\n              columnNumber: 18\n            }, this), \" \", (_exam$questions = exam.questions) === null || _exam$questions === void 0 ? void 0 : _exam$questions.length]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Type:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 57,\n              columnNumber: 18\n            }, this), \" \", exam.examType]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 57,\n            columnNumber: 15\n          }, this), exam.isPaid && /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Price:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 58,\n              columnNumber: 34\n            }, this), \" \\u20B9\", exam.price]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 31\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: `/exam/${exam._id}`,\n            className: \"btn\",\n            children: \"Start Exam\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 15\n          }, this)]\n        }, exam._id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 38,\n    columnNumber: 5\n  }, this);\n};\n_s(ExamList, \"nJpvx+TMc/J6oRbrhNFnpEGlaMk=\", false, function () {\n  return [useNavigate];\n});\n_c = ExamList;\nexport default ExamList;\nvar _c;\n$RefreshReg$(_c, \"ExamList\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "useNavigate", "api", "jsxDEV", "_jsxDEV", "ExamList", "_s", "exams", "setExams", "loading", "setLoading", "error", "setError", "navigate", "token", "localStorage", "getItem", "fetchExams", "console", "log", "response", "get", "data", "_error$response", "_error$response$data", "message", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "display", "justifyContent", "alignItems", "to", "length", "map", "exam", "_exam$subject", "_exam$questions", "title", "description", "subject", "name", "duration", "questions", "examType", "isPaid", "price", "_id", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/ExamList.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport api from '../services/api';\n\nconst ExamList = () => {\n  const [exams, setExams] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const navigate = useNavigate();\n\n  useEffect(() => {\n    const token = localStorage.getItem('token');\n    if (!token) {\n      navigate('/login');\n      return;\n    }\n\n    fetchExams();\n  }, [navigate]);\n\n  const fetchExams = async () => {\n    try {\n      console.log('Fetching exams from API...');\n      const response = await api.get('/exams');\n      console.log('Exams fetched successfully:', response.data);\n      setExams(response.data);\n    } catch (error) {\n      console.error('Error fetching exams:', error);\n      setError('Failed to fetch exams: ' + (error.response?.data?.message || error.message));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (loading) return <div className=\"loading\">Loading exams...</div>;\n\n  return (\n    <div className=\"container\">\n      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n        <h2>Available Exams</h2>\n        <Link to=\"/\" className=\"btn btn-secondary\">Home</Link>\n      </div>\n      \n      {error && <div className=\"error\">{error}</div>}\n      \n      {exams.length === 0 ? (\n        <p>No exams available at the moment.</p>\n      ) : (\n        <div>\n          {exams.map(exam => (\n            <div key={exam._id} className=\"exam-card\">\n              <h3>{exam.title}</h3>\n              <p>{exam.description}</p>\n              <p><strong>Subject:</strong> {exam.subject?.name}</p>\n              <p><strong>Duration:</strong> {exam.duration} minutes</p>\n              <p><strong>Questions:</strong> {exam.questions?.length}</p>\n              <p><strong>Type:</strong> {exam.examType}</p>\n              {exam.isPaid && <p><strong>Price:</strong> ₹{exam.price}</p>}\n              \n              <Link to={`/exam/${exam._id}`} className=\"btn\">\n                Start Exam\n              </Link>\n            </div>\n          ))}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default ExamList;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,OAAOC,GAAG,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGV,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACW,OAAO,EAAEC,UAAU,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACa,KAAK,EAAEC,QAAQ,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAMe,QAAQ,GAAGZ,WAAW,CAAC,CAAC;EAE9BF,SAAS,CAAC,MAAM;IACd,MAAMe,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAI,CAACF,KAAK,EAAE;MACVD,QAAQ,CAAC,QAAQ,CAAC;MAClB;IACF;IAEAI,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,CAACJ,QAAQ,CAAC,CAAC;EAEd,MAAMI,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFC,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;MACzC,MAAMC,QAAQ,GAAG,MAAMlB,GAAG,CAACmB,GAAG,CAAC,QAAQ,CAAC;MACxCH,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEC,QAAQ,CAACE,IAAI,CAAC;MACzDd,QAAQ,CAACY,QAAQ,CAACE,IAAI,CAAC;IACzB,CAAC,CAAC,OAAOX,KAAK,EAAE;MAAA,IAAAY,eAAA,EAAAC,oBAAA;MACdN,OAAO,CAACP,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7CC,QAAQ,CAAC,yBAAyB,IAAI,EAAAW,eAAA,GAAAZ,KAAK,CAACS,QAAQ,cAAAG,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBD,IAAI,cAAAE,oBAAA,uBAApBA,oBAAA,CAAsBC,OAAO,KAAId,KAAK,CAACc,OAAO,CAAC,CAAC;IACxF,CAAC,SAAS;MACRf,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,IAAID,OAAO,EAAE,oBAAOL,OAAA;IAAKsB,SAAS,EAAC,SAAS;IAAAC,QAAA,EAAC;EAAgB;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;EAEnE,oBACE3B,OAAA;IAAKsB,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxBvB,OAAA;MAAK4B,KAAK,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAAR,QAAA,gBACrFvB,OAAA;QAAAuB,QAAA,EAAI;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACxB3B,OAAA,CAACJ,IAAI;QAACoC,EAAE,EAAC,GAAG;QAACV,SAAS,EAAC,mBAAmB;QAAAC,QAAA,EAAC;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnD,CAAC,EAELpB,KAAK,iBAAIP,OAAA;MAAKsB,SAAS,EAAC,OAAO;MAAAC,QAAA,EAAEhB;IAAK;MAAAiB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,EAE7CxB,KAAK,CAAC8B,MAAM,KAAK,CAAC,gBACjBjC,OAAA;MAAAuB,QAAA,EAAG;IAAiC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC,gBAExC3B,OAAA;MAAAuB,QAAA,EACGpB,KAAK,CAAC+B,GAAG,CAACC,IAAI;QAAA,IAAAC,aAAA,EAAAC,eAAA;QAAA,oBACbrC,OAAA;UAAoBsB,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACvCvB,OAAA;YAAAuB,QAAA,EAAKY,IAAI,CAACG;UAAK;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACrB3B,OAAA;YAAAuB,QAAA,EAAIY,IAAI,CAACI;UAAW;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzB3B,OAAA;YAAAuB,QAAA,gBAAGvB,OAAA;cAAAuB,QAAA,EAAQ;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,GAAAS,aAAA,GAACD,IAAI,CAACK,OAAO,cAAAJ,aAAA,uBAAZA,aAAA,CAAcK,IAAI;UAAA;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrD3B,OAAA;YAAAuB,QAAA,gBAAGvB,OAAA;cAAAuB,QAAA,EAAQ;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACQ,IAAI,CAACO,QAAQ,EAAC,UAAQ;UAAA;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACzD3B,OAAA;YAAAuB,QAAA,gBAAGvB,OAAA;cAAAuB,QAAA,EAAQ;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,GAAAU,eAAA,GAACF,IAAI,CAACQ,SAAS,cAAAN,eAAA,uBAAdA,eAAA,CAAgBJ,MAAM;UAAA;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3D3B,OAAA;YAAAuB,QAAA,gBAAGvB,OAAA;cAAAuB,QAAA,EAAQ;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACQ,IAAI,CAACS,QAAQ;UAAA;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EAC5CQ,IAAI,CAACU,MAAM,iBAAI7C,OAAA;YAAAuB,QAAA,gBAAGvB,OAAA;cAAAuB,QAAA,EAAQ;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,WAAE,EAACQ,IAAI,CAACW,KAAK;UAAA;YAAAtB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAE5D3B,OAAA,CAACJ,IAAI;YAACoC,EAAE,EAAE,SAASG,IAAI,CAACY,GAAG,EAAG;YAACzB,SAAS,EAAC,KAAK;YAAAC,QAAA,EAAC;UAE/C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA,GAXCQ,IAAI,CAACY,GAAG;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAYb,CAAC;MAAA,CACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACzB,EAAA,CAhEID,QAAQ;EAAA,QAIKJ,WAAW;AAAA;AAAAmD,EAAA,GAJxB/C,QAAQ;AAkEd,eAAeA,QAAQ;AAAC,IAAA+C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}