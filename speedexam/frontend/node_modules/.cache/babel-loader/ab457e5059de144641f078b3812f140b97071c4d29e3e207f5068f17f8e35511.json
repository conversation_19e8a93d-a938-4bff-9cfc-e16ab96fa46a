{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/Login.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useNavigate, Link } from 'react-router-dom';\nimport api from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Login = () => {\n  _s();\n  const [formData, setFormData] = useState({\n    email: '',\n    password: ''\n  });\n  const [error, setError] = useState('');\n  const [loading, setLoading] = useState(false);\n  const navigate = useNavigate();\n  const handleChange = e => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n    try {\n      console.log('Attempting login with:', formData.email);\n      const response = await api.post('/auth/login', formData);\n      console.log('Login successful:', response.data);\n      localStorage.setItem('token', response.data.token);\n      navigate('/exams');\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('Login error:', error);\n      setError(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Login failed');\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"Login\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Email:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"email\",\n          name: \"email\",\n          value: formData.email,\n          onChange: handleChange,\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Password:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"password\",\n          name: \"password\",\n          value: formData.password,\n          onChange: handleChange,\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        className: \"btn\",\n        disabled: loading,\n        children: loading ? 'Logging in...' : 'Login'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: [\"Don't have an account? \", /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/register\",\n        children: \"Register here\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 32\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 74,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n        children: \"Demo Credentials:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 43\n      }, this), \"Email: <EMAIL>\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 32\n      }, this), \"Password: password123\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 41,\n    columnNumber: 5\n  }, this);\n};\n_s(Login, \"xgbvgKFAJw5hexSliSarJCeDaxw=\", false, function () {\n  return [useNavigate];\n});\n_c = Login;\nexport default Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "Link", "api", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "_s", "formData", "setFormData", "email", "password", "error", "setError", "loading", "setLoading", "navigate", "handleChange", "e", "target", "name", "value", "handleSubmit", "preventDefault", "console", "log", "response", "post", "data", "localStorage", "setItem", "token", "_error$response", "_error$response$data", "message", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "type", "onChange", "required", "disabled", "to", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/Login.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useNavigate, Link } from 'react-router-dom';\nimport api from '../services/api';\n\nconst Login = () => {\n  const [formData, setFormData] = useState({\n    email: '',\n    password: ''\n  });\n  const [error, setError] = useState('');\n  const [loading, setLoading] = useState(false);\n  const navigate = useNavigate();\n\n  const handleChange = (e) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n\n    try {\n      console.log('Attempting login with:', formData.email);\n      const response = await api.post('/auth/login', formData);\n      console.log('Login successful:', response.data);\n      localStorage.setItem('token', response.data.token);\n      navigate('/exams');\n    } catch (error) {\n      console.error('Login error:', error);\n      setError(error.response?.data?.message || 'Login failed');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"container\">\n      <h2>Login</h2>\n      \n      {error && <div className=\"error\">{error}</div>}\n      \n      <form onSubmit={handleSubmit}>\n        <div className=\"form-group\">\n          <label>Email:</label>\n          <input\n            type=\"email\"\n            name=\"email\"\n            value={formData.email}\n            onChange={handleChange}\n            required\n          />\n        </div>\n        \n        <div className=\"form-group\">\n          <label>Password:</label>\n          <input\n            type=\"password\"\n            name=\"password\"\n            value={formData.password}\n            onChange={handleChange}\n            required\n          />\n        </div>\n        \n        <button type=\"submit\" className=\"btn\" disabled={loading}>\n          {loading ? 'Logging in...' : 'Login'}\n        </button>\n      </form>\n      \n      <p>\n        Don't have an account? <Link to=\"/register\">Register here</Link>\n      </p>\n      \n      <p>\n        <strong>Demo Credentials:</strong><br />\n        Email: <EMAIL><br />\n        Password: password123\n      </p>\n    </div>\n  );\n};\n\nexport default Login;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,EAAEC,IAAI,QAAQ,kBAAkB;AACpD,OAAOC,GAAG,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGT,QAAQ,CAAC;IACvCU,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAMgB,QAAQ,GAAGf,WAAW,CAAC,CAAC;EAE9B,MAAMgB,YAAY,GAAIC,CAAC,IAAK;IAC1BT,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACU,CAAC,CAACC,MAAM,CAACC,IAAI,GAAGF,CAAC,CAACC,MAAM,CAACE;IAC5B,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,YAAY,GAAG,MAAOJ,CAAC,IAAK;IAChCA,CAAC,CAACK,cAAc,CAAC,CAAC;IAClBR,UAAU,CAAC,IAAI,CAAC;IAChBF,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACFW,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEjB,QAAQ,CAACE,KAAK,CAAC;MACrD,MAAMgB,QAAQ,GAAG,MAAMvB,GAAG,CAACwB,IAAI,CAAC,aAAa,EAAEnB,QAAQ,CAAC;MACxDgB,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEC,QAAQ,CAACE,IAAI,CAAC;MAC/CC,YAAY,CAACC,OAAO,CAAC,OAAO,EAAEJ,QAAQ,CAACE,IAAI,CAACG,KAAK,CAAC;MAClDf,QAAQ,CAAC,QAAQ,CAAC;IACpB,CAAC,CAAC,OAAOJ,KAAK,EAAE;MAAA,IAAAoB,eAAA,EAAAC,oBAAA;MACdT,OAAO,CAACZ,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACpCC,QAAQ,CAAC,EAAAmB,eAAA,GAAApB,KAAK,CAACc,QAAQ,cAAAM,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBJ,IAAI,cAAAK,oBAAA,uBAApBA,oBAAA,CAAsBC,OAAO,KAAI,cAAc,CAAC;IAC3D,CAAC,SAAS;MACRnB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACEV,OAAA;IAAK8B,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxB/B,OAAA;MAAA+B,QAAA,EAAI;IAAK;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,EAEb5B,KAAK,iBAAIP,OAAA;MAAK8B,SAAS,EAAC,OAAO;MAAAC,QAAA,EAAExB;IAAK;MAAAyB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAE9CnC,OAAA;MAAMoC,QAAQ,EAAEnB,YAAa;MAAAc,QAAA,gBAC3B/B,OAAA;QAAK8B,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzB/B,OAAA;UAAA+B,QAAA,EAAO;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACrBnC,OAAA;UACEqC,IAAI,EAAC,OAAO;UACZtB,IAAI,EAAC,OAAO;UACZC,KAAK,EAAEb,QAAQ,CAACE,KAAM;UACtBiC,QAAQ,EAAE1B,YAAa;UACvB2B,QAAQ;QAAA;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENnC,OAAA;QAAK8B,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzB/B,OAAA;UAAA+B,QAAA,EAAO;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACxBnC,OAAA;UACEqC,IAAI,EAAC,UAAU;UACftB,IAAI,EAAC,UAAU;UACfC,KAAK,EAAEb,QAAQ,CAACG,QAAS;UACzBgC,QAAQ,EAAE1B,YAAa;UACvB2B,QAAQ;QAAA;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENnC,OAAA;QAAQqC,IAAI,EAAC,QAAQ;QAACP,SAAS,EAAC,KAAK;QAACU,QAAQ,EAAE/B,OAAQ;QAAAsB,QAAA,EACrDtB,OAAO,GAAG,eAAe,GAAG;MAAO;QAAAuB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAEPnC,OAAA;MAAA+B,QAAA,GAAG,yBACsB,eAAA/B,OAAA,CAACH,IAAI;QAAC4C,EAAE,EAAC,WAAW;QAAAV,QAAA,EAAC;MAAa;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/D,CAAC,eAEJnC,OAAA;MAAA+B,QAAA,gBACE/B,OAAA;QAAA+B,QAAA,EAAQ;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAAAnC,OAAA;QAAAgC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,2BACjB,eAAAnC,OAAA;QAAAgC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,yBAE/B;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEV,CAAC;AAACjC,EAAA,CAhFID,KAAK;EAAA,QAOQL,WAAW;AAAA;AAAA8C,EAAA,GAPxBzC,KAAK;AAkFX,eAAeA,KAAK;AAAC,IAAAyC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}