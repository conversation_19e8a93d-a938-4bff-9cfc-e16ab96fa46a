{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/AdminDashboard.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport api from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminDashboard = () => {\n  _s();\n  const [stats, setStats] = useState({\n    totalExams: 0,\n    totalStudents: 0,\n    totalAttempts: 0,\n    averageScore: 0\n  });\n  const [recentAttempts, setRecentAttempts] = useState([]);\n  const [exams, setExams] = useState([]);\n  useEffect(() => {\n    fetchDashboardData();\n  }, []);\n  const fetchDashboardData = async () => {\n    try {\n      // Mock admin dashboard data\n      setStats({\n        totalExams: 2,\n        totalStudents: 15,\n        totalAttempts: 45,\n        averageScore: 78\n      });\n      setRecentAttempts([{\n        id: 1,\n        studentName: '<PERSON>',\n        examTitle: 'Basic Mathematics Test',\n        score: 100,\n        percentage: 100,\n        timeTaken: 15,\n        date: '2024-01-15'\n      }, {\n        id: 2,\n        studentName: 'Jane Smith',\n        examTitle: 'General Science Quiz',\n        score: 1,\n        percentage: 50,\n        timeTaken: 12,\n        date: '2024-01-15'\n      }, {\n        id: 3,\n        studentName: 'Mike Johnson',\n        examTitle: 'Basic Mathematics Test',\n        score: 2,\n        percentage: 67,\n        timeTaken: 20,\n        date: '2024-01-14'\n      }, {\n        id: 4,\n        studentName: 'Sarah Wilson',\n        examTitle: 'General Science Quiz',\n        score: 2,\n        percentage: 100,\n        timeTaken: 8,\n        date: '2024-01-14'\n      }, {\n        id: 5,\n        studentName: 'David Brown',\n        examTitle: 'Basic Mathematics Test',\n        score: 1,\n        percentage: 33,\n        timeTaken: 25,\n        date: '2024-01-13'\n      }]);\n      const examsResponse = await api.get('/exams');\n      setExams(examsResponse.data);\n    } catch (error) {\n      console.error('Error fetching dashboard data:', error);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"admin-dashboard\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"admin-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"\\uD83C\\uDF93 Admin Dashboard\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"admin-nav\",\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          to: \"/\",\n          className: \"btn btn-secondary\",\n          children: \"Student View\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/admin/exams\",\n          className: \"btn\",\n          children: \"Manage Exams\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/admin/students\",\n          className: \"btn\",\n          children: \"View Students\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 86,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"stats-grid\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-icon\",\n          children: \"\\uD83D\\uDCDA\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: stats.totalExams\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Total Exams\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-icon\",\n          children: \"\\uD83D\\uDC65\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: stats.totalStudents\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Total Students\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-icon\",\n          children: \"\\uD83D\\uDCDD\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: stats.totalAttempts\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Exam Attempts\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 113,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-icon\",\n          children: \"\\uD83D\\uDCCA\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: [stats.averageScore, \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Average Score\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 121,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 96,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"admin-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"\\uD83D\\uDCDA Available Exams\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"exams-grid\",\n        children: exams.map(exam => {\n          var _exam$subject, _exam$questions;\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"admin-exam-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: exam.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Subject:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 137,\n                columnNumber: 18\n              }, this), \" \", (_exam$subject = exam.subject) === null || _exam$subject === void 0 ? void 0 : _exam$subject.name]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Questions:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 138,\n                columnNumber: 18\n              }, this), \" \", (_exam$questions = exam.questions) === null || _exam$questions === void 0 ? void 0 : _exam$questions.length]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Duration:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 139,\n                columnNumber: 18\n              }, this), \" \", exam.duration, \" minutes\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Type:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 140,\n                columnNumber: 18\n              }, this), \" \", exam.examType]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 140,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"exam-actions\",\n              children: [/*#__PURE__*/_jsxDEV(Link, {\n                to: `/admin/exam/${exam._id}`,\n                className: \"btn btn-sm\",\n                children: \"Edit\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 142,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: `/exam/${exam._id}`,\n                className: \"btn btn-sm btn-secondary\",\n                children: \"Preview\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 143,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 15\n            }, this)]\n          }, exam._id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 13\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 133,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 131,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"admin-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"\\uD83D\\uDCCB Recent Exam Attempts\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"attempts-table\",\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Student\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 157,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Exam\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 158,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Score\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Percentage\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 160,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Time\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Date\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 162,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 163,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            children: recentAttempts.map(attempt => /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                children: attempt.studentName\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: attempt.examTitle\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 170,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: [attempt.score, \"/3\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `percentage ${attempt.percentage >= 70 ? 'good' : attempt.percentage >= 50 ? 'average' : 'poor'}`,\n                  children: [attempt.percentage, \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 173,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: [attempt.timeTaken, \" min\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 180,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: attempt.date\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `status ${attempt.percentage >= 70 ? 'passed' : 'failed'}`,\n                  children: attempt.percentage >= 70 ? 'Passed' : 'Failed'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 183,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 182,\n                columnNumber: 19\n              }, this)]\n            }, attempt.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 151,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"admin-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"\\u26A1 Quick Actions\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 198,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"quick-actions\",\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          to: \"/admin/create-exam\",\n          className: \"action-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"action-icon\",\n            children: \"\\u2795\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Create New Exam\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Add a new exam with questions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/admin/reports\",\n          className: \"action-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"action-icon\",\n            children: \"\\uD83D\\uDCC8\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"View Reports\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Detailed analytics and reports\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/admin/settings\",\n          className: \"action-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"action-icon\",\n            children: \"\\u2699\\uFE0F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"System Settings\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Configure application settings\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 199,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 197,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 85,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminDashboard, \"lltM98tBdI2X42VABRzCfm4v7e0=\");\n_c = AdminDashboard;\nexport default AdminDashboard;\nvar _c;\n$RefreshReg$(_c, \"AdminDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "api", "jsxDEV", "_jsxDEV", "AdminDashboard", "_s", "stats", "setStats", "totalExams", "totalStudents", "totalAttempts", "averageScore", "recentAttempts", "setRecentAttempts", "exams", "setExams", "fetchDashboardData", "id", "studentName", "examTitle", "score", "percentage", "timeTaken", "date", "examsResponse", "get", "data", "error", "console", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "map", "exam", "_exam$subject", "_exam$questions", "title", "subject", "name", "questions", "length", "duration", "examType", "_id", "attempt", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/AdminDashboard.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport api from '../services/api';\n\nconst AdminDashboard = () => {\n  const [stats, setStats] = useState({\n    totalExams: 0,\n    totalStudents: 0,\n    totalAttempts: 0,\n    averageScore: 0\n  });\n  const [recentAttempts, setRecentAttempts] = useState([]);\n  const [exams, setExams] = useState([]);\n\n  useEffect(() => {\n    fetchDashboardData();\n  }, []);\n\n  const fetchDashboardData = async () => {\n    try {\n      // Mock admin dashboard data\n      setStats({\n        totalExams: 2,\n        totalStudents: 15,\n        totalAttempts: 45,\n        averageScore: 78\n      });\n\n      setRecentAttempts([\n        {\n          id: 1,\n          studentName: '<PERSON>',\n          examTitle: 'Basic Mathematics Test',\n          score: 100,\n          percentage: 100,\n          timeTaken: 15,\n          date: '2024-01-15'\n        },\n        {\n          id: 2,\n          studentName: '<PERSON>',\n          examTitle: 'General Science Quiz',\n          score: 1,\n          percentage: 50,\n          timeTaken: 12,\n          date: '2024-01-15'\n        },\n        {\n          id: 3,\n          studentName: '<PERSON>',\n          examTitle: 'Basic Mathematics Test',\n          score: 2,\n          percentage: 67,\n          timeTaken: 20,\n          date: '2024-01-14'\n        },\n        {\n          id: 4,\n          studentName: 'Sarah Wilson',\n          examTitle: 'General Science Quiz',\n          score: 2,\n          percentage: 100,\n          timeTaken: 8,\n          date: '2024-01-14'\n        },\n        {\n          id: 5,\n          studentName: 'David Brown',\n          examTitle: 'Basic Mathematics Test',\n          score: 1,\n          percentage: 33,\n          timeTaken: 25,\n          date: '2024-01-13'\n        }\n      ]);\n\n      const examsResponse = await api.get('/exams');\n      setExams(examsResponse.data);\n    } catch (error) {\n      console.error('Error fetching dashboard data:', error);\n    }\n  };\n\n  return (\n    <div className=\"admin-dashboard\">\n      <div className=\"admin-header\">\n        <h1>🎓 Admin Dashboard</h1>\n        <div className=\"admin-nav\">\n          <Link to=\"/\" className=\"btn btn-secondary\">Student View</Link>\n          <Link to=\"/admin/exams\" className=\"btn\">Manage Exams</Link>\n          <Link to=\"/admin/students\" className=\"btn\">View Students</Link>\n        </div>\n      </div>\n\n      {/* Statistics Cards */}\n      <div className=\"stats-grid\">\n        <div className=\"stat-card\">\n          <div className=\"stat-icon\">📚</div>\n          <div className=\"stat-content\">\n            <h3>{stats.totalExams}</h3>\n            <p>Total Exams</p>\n          </div>\n        </div>\n        \n        <div className=\"stat-card\">\n          <div className=\"stat-icon\">👥</div>\n          <div className=\"stat-content\">\n            <h3>{stats.totalStudents}</h3>\n            <p>Total Students</p>\n          </div>\n        </div>\n        \n        <div className=\"stat-card\">\n          <div className=\"stat-icon\">📝</div>\n          <div className=\"stat-content\">\n            <h3>{stats.totalAttempts}</h3>\n            <p>Exam Attempts</p>\n          </div>\n        </div>\n        \n        <div className=\"stat-card\">\n          <div className=\"stat-icon\">📊</div>\n          <div className=\"stat-content\">\n            <h3>{stats.averageScore}%</h3>\n            <p>Average Score</p>\n          </div>\n        </div>\n      </div>\n\n      {/* Available Exams */}\n      <div className=\"admin-section\">\n        <h2>📚 Available Exams</h2>\n        <div className=\"exams-grid\">\n          {exams.map(exam => (\n            <div key={exam._id} className=\"admin-exam-card\">\n              <h3>{exam.title}</h3>\n              <p><strong>Subject:</strong> {exam.subject?.name}</p>\n              <p><strong>Questions:</strong> {exam.questions?.length}</p>\n              <p><strong>Duration:</strong> {exam.duration} minutes</p>\n              <p><strong>Type:</strong> {exam.examType}</p>\n              <div className=\"exam-actions\">\n                <Link to={`/admin/exam/${exam._id}`} className=\"btn btn-sm\">Edit</Link>\n                <Link to={`/exam/${exam._id}`} className=\"btn btn-sm btn-secondary\">Preview</Link>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n\n      {/* Recent Exam Attempts */}\n      <div className=\"admin-section\">\n        <h2>📋 Recent Exam Attempts</h2>\n        <div className=\"attempts-table\">\n          <table>\n            <thead>\n              <tr>\n                <th>Student</th>\n                <th>Exam</th>\n                <th>Score</th>\n                <th>Percentage</th>\n                <th>Time</th>\n                <th>Date</th>\n                <th>Status</th>\n              </tr>\n            </thead>\n            <tbody>\n              {recentAttempts.map(attempt => (\n                <tr key={attempt.id}>\n                  <td>{attempt.studentName}</td>\n                  <td>{attempt.examTitle}</td>\n                  <td>{attempt.score}/3</td>\n                  <td>\n                    <span className={`percentage ${\n                      attempt.percentage >= 70 ? 'good' : \n                      attempt.percentage >= 50 ? 'average' : 'poor'\n                    }`}>\n                      {attempt.percentage}%\n                    </span>\n                  </td>\n                  <td>{attempt.timeTaken} min</td>\n                  <td>{attempt.date}</td>\n                  <td>\n                    <span className={`status ${\n                      attempt.percentage >= 70 ? 'passed' : 'failed'\n                    }`}>\n                      {attempt.percentage >= 70 ? 'Passed' : 'Failed'}\n                    </span>\n                  </td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n      </div>\n\n      {/* Quick Actions */}\n      <div className=\"admin-section\">\n        <h2>⚡ Quick Actions</h2>\n        <div className=\"quick-actions\">\n          <Link to=\"/admin/create-exam\" className=\"action-card\">\n            <div className=\"action-icon\">➕</div>\n            <h3>Create New Exam</h3>\n            <p>Add a new exam with questions</p>\n          </Link>\n          \n          <Link to=\"/admin/reports\" className=\"action-card\">\n            <div className=\"action-icon\">📈</div>\n            <h3>View Reports</h3>\n            <p>Detailed analytics and reports</p>\n          </Link>\n          \n          <Link to=\"/admin/settings\" className=\"action-card\">\n            <div className=\"action-icon\">⚙️</div>\n            <h3>System Settings</h3>\n            <p>Configure application settings</p>\n          </Link>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default AdminDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,QAAQ,kBAAkB;AACvC,OAAOC,GAAG,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGT,QAAQ,CAAC;IACjCU,UAAU,EAAE,CAAC;IACbC,aAAa,EAAE,CAAC;IAChBC,aAAa,EAAE,CAAC;IAChBC,YAAY,EAAE;EAChB,CAAC,CAAC;EACF,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACgB,KAAK,EAAEC,QAAQ,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAEtCC,SAAS,CAAC,MAAM;IACdiB,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACF;MACAT,QAAQ,CAAC;QACPC,UAAU,EAAE,CAAC;QACbC,aAAa,EAAE,EAAE;QACjBC,aAAa,EAAE,EAAE;QACjBC,YAAY,EAAE;MAChB,CAAC,CAAC;MAEFE,iBAAiB,CAAC,CAChB;QACEI,EAAE,EAAE,CAAC;QACLC,WAAW,EAAE,UAAU;QACvBC,SAAS,EAAE,wBAAwB;QACnCC,KAAK,EAAE,GAAG;QACVC,UAAU,EAAE,GAAG;QACfC,SAAS,EAAE,EAAE;QACbC,IAAI,EAAE;MACR,CAAC,EACD;QACEN,EAAE,EAAE,CAAC;QACLC,WAAW,EAAE,YAAY;QACzBC,SAAS,EAAE,sBAAsB;QACjCC,KAAK,EAAE,CAAC;QACRC,UAAU,EAAE,EAAE;QACdC,SAAS,EAAE,EAAE;QACbC,IAAI,EAAE;MACR,CAAC,EACD;QACEN,EAAE,EAAE,CAAC;QACLC,WAAW,EAAE,cAAc;QAC3BC,SAAS,EAAE,wBAAwB;QACnCC,KAAK,EAAE,CAAC;QACRC,UAAU,EAAE,EAAE;QACdC,SAAS,EAAE,EAAE;QACbC,IAAI,EAAE;MACR,CAAC,EACD;QACEN,EAAE,EAAE,CAAC;QACLC,WAAW,EAAE,cAAc;QAC3BC,SAAS,EAAE,sBAAsB;QACjCC,KAAK,EAAE,CAAC;QACRC,UAAU,EAAE,GAAG;QACfC,SAAS,EAAE,CAAC;QACZC,IAAI,EAAE;MACR,CAAC,EACD;QACEN,EAAE,EAAE,CAAC;QACLC,WAAW,EAAE,aAAa;QAC1BC,SAAS,EAAE,wBAAwB;QACnCC,KAAK,EAAE,CAAC;QACRC,UAAU,EAAE,EAAE;QACdC,SAAS,EAAE,EAAE;QACbC,IAAI,EAAE;MACR,CAAC,CACF,CAAC;MAEF,MAAMC,aAAa,GAAG,MAAMvB,GAAG,CAACwB,GAAG,CAAC,QAAQ,CAAC;MAC7CV,QAAQ,CAACS,aAAa,CAACE,IAAI,CAAC;IAC9B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;IACxD;EACF,CAAC;EAED,oBACExB,OAAA;IAAK0B,SAAS,EAAC,iBAAiB;IAAAC,QAAA,gBAC9B3B,OAAA;MAAK0B,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC3B3B,OAAA;QAAA2B,QAAA,EAAI;MAAkB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC3B/B,OAAA;QAAK0B,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxB3B,OAAA,CAACH,IAAI;UAACmC,EAAE,EAAC,GAAG;UAACN,SAAS,EAAC,mBAAmB;UAAAC,QAAA,EAAC;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC9D/B,OAAA,CAACH,IAAI;UAACmC,EAAE,EAAC,cAAc;UAACN,SAAS,EAAC,KAAK;UAAAC,QAAA,EAAC;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC3D/B,OAAA,CAACH,IAAI;UAACmC,EAAE,EAAC,iBAAiB;UAACN,SAAS,EAAC,KAAK;UAAAC,QAAA,EAAC;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5D,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN/B,OAAA;MAAK0B,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACzB3B,OAAA;QAAK0B,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxB3B,OAAA;UAAK0B,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACnC/B,OAAA;UAAK0B,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3B3B,OAAA;YAAA2B,QAAA,EAAKxB,KAAK,CAACE;UAAU;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC3B/B,OAAA;YAAA2B,QAAA,EAAG;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN/B,OAAA;QAAK0B,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxB3B,OAAA;UAAK0B,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACnC/B,OAAA;UAAK0B,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3B3B,OAAA;YAAA2B,QAAA,EAAKxB,KAAK,CAACG;UAAa;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC9B/B,OAAA;YAAA2B,QAAA,EAAG;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN/B,OAAA;QAAK0B,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxB3B,OAAA;UAAK0B,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACnC/B,OAAA;UAAK0B,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3B3B,OAAA;YAAA2B,QAAA,EAAKxB,KAAK,CAACI;UAAa;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC9B/B,OAAA;YAAA2B,QAAA,EAAG;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN/B,OAAA;QAAK0B,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxB3B,OAAA;UAAK0B,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACnC/B,OAAA;UAAK0B,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3B3B,OAAA;YAAA2B,QAAA,GAAKxB,KAAK,CAACK,YAAY,EAAC,GAAC;UAAA;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9B/B,OAAA;YAAA2B,QAAA,EAAG;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN/B,OAAA;MAAK0B,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5B3B,OAAA;QAAA2B,QAAA,EAAI;MAAkB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC3B/B,OAAA;QAAK0B,SAAS,EAAC,YAAY;QAAAC,QAAA,EACxBhB,KAAK,CAACsB,GAAG,CAACC,IAAI;UAAA,IAAAC,aAAA,EAAAC,eAAA;UAAA,oBACbpC,OAAA;YAAoB0B,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC7C3B,OAAA;cAAA2B,QAAA,EAAKO,IAAI,CAACG;YAAK;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACrB/B,OAAA;cAAA2B,QAAA,gBAAG3B,OAAA;gBAAA2B,QAAA,EAAQ;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,GAAAI,aAAA,GAACD,IAAI,CAACI,OAAO,cAAAH,aAAA,uBAAZA,aAAA,CAAcI,IAAI;YAAA;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrD/B,OAAA;cAAA2B,QAAA,gBAAG3B,OAAA;gBAAA2B,QAAA,EAAQ;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,GAAAK,eAAA,GAACF,IAAI,CAACM,SAAS,cAAAJ,eAAA,uBAAdA,eAAA,CAAgBK,MAAM;YAAA;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3D/B,OAAA;cAAA2B,QAAA,gBAAG3B,OAAA;gBAAA2B,QAAA,EAAQ;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACG,IAAI,CAACQ,QAAQ,EAAC,UAAQ;YAAA;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACzD/B,OAAA;cAAA2B,QAAA,gBAAG3B,OAAA;gBAAA2B,QAAA,EAAQ;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACG,IAAI,CAACS,QAAQ;YAAA;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7C/B,OAAA;cAAK0B,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3B3B,OAAA,CAACH,IAAI;gBAACmC,EAAE,EAAE,eAAeE,IAAI,CAACU,GAAG,EAAG;gBAAClB,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACvE/B,OAAA,CAACH,IAAI;gBAACmC,EAAE,EAAE,SAASE,IAAI,CAACU,GAAG,EAAG;gBAAClB,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/E,CAAC;UAAA,GATEG,IAAI,CAACU,GAAG;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAUb,CAAC;QAAA,CACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN/B,OAAA;MAAK0B,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5B3B,OAAA;QAAA2B,QAAA,EAAI;MAAuB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAChC/B,OAAA;QAAK0B,SAAS,EAAC,gBAAgB;QAAAC,QAAA,eAC7B3B,OAAA;UAAA2B,QAAA,gBACE3B,OAAA;YAAA2B,QAAA,eACE3B,OAAA;cAAA2B,QAAA,gBACE3B,OAAA;gBAAA2B,QAAA,EAAI;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChB/B,OAAA;gBAAA2B,QAAA,EAAI;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACb/B,OAAA;gBAAA2B,QAAA,EAAI;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACd/B,OAAA;gBAAA2B,QAAA,EAAI;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnB/B,OAAA;gBAAA2B,QAAA,EAAI;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACb/B,OAAA;gBAAA2B,QAAA,EAAI;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACb/B,OAAA;gBAAA2B,QAAA,EAAI;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACR/B,OAAA;YAAA2B,QAAA,EACGlB,cAAc,CAACwB,GAAG,CAACY,OAAO,iBACzB7C,OAAA;cAAA2B,QAAA,gBACE3B,OAAA;gBAAA2B,QAAA,EAAKkB,OAAO,CAAC9B;cAAW;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC9B/B,OAAA;gBAAA2B,QAAA,EAAKkB,OAAO,CAAC7B;cAAS;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC5B/B,OAAA;gBAAA2B,QAAA,GAAKkB,OAAO,CAAC5B,KAAK,EAAC,IAAE;cAAA;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1B/B,OAAA;gBAAA2B,QAAA,eACE3B,OAAA;kBAAM0B,SAAS,EAAE,cACfmB,OAAO,CAAC3B,UAAU,IAAI,EAAE,GAAG,MAAM,GACjC2B,OAAO,CAAC3B,UAAU,IAAI,EAAE,GAAG,SAAS,GAAG,MAAM,EAC5C;kBAAAS,QAAA,GACAkB,OAAO,CAAC3B,UAAU,EAAC,GACtB;gBAAA;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACL/B,OAAA;gBAAA2B,QAAA,GAAKkB,OAAO,CAAC1B,SAAS,EAAC,MAAI;cAAA;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChC/B,OAAA;gBAAA2B,QAAA,EAAKkB,OAAO,CAACzB;cAAI;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACvB/B,OAAA;gBAAA2B,QAAA,eACE3B,OAAA;kBAAM0B,SAAS,EAAE,UACfmB,OAAO,CAAC3B,UAAU,IAAI,EAAE,GAAG,QAAQ,GAAG,QAAQ,EAC7C;kBAAAS,QAAA,EACAkB,OAAO,CAAC3B,UAAU,IAAI,EAAE,GAAG,QAAQ,GAAG;gBAAQ;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA,GApBEc,OAAO,CAAC/B,EAAE;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAqBf,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN/B,OAAA;MAAK0B,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5B3B,OAAA;QAAA2B,QAAA,EAAI;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACxB/B,OAAA;QAAK0B,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5B3B,OAAA,CAACH,IAAI;UAACmC,EAAE,EAAC,oBAAoB;UAACN,SAAS,EAAC,aAAa;UAAAC,QAAA,gBACnD3B,OAAA;YAAK0B,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACpC/B,OAAA;YAAA2B,QAAA,EAAI;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxB/B,OAAA;YAAA2B,QAAA,EAAG;UAA6B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC,eAEP/B,OAAA,CAACH,IAAI;UAACmC,EAAE,EAAC,gBAAgB;UAACN,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC/C3B,OAAA;YAAK0B,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACrC/B,OAAA;YAAA2B,QAAA,EAAI;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrB/B,OAAA;YAAA2B,QAAA,EAAG;UAA8B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC,eAEP/B,OAAA,CAACH,IAAI;UAACmC,EAAE,EAAC,iBAAiB;UAACN,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAChD3B,OAAA;YAAK0B,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACrC/B,OAAA;YAAA2B,QAAA,EAAI;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxB/B,OAAA;YAAA2B,QAAA,EAAG;UAA8B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC7B,EAAA,CAxNID,cAAc;AAAA6C,EAAA,GAAd7C,cAAc;AA0NpB,eAAeA,cAAc;AAAC,IAAA6C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}