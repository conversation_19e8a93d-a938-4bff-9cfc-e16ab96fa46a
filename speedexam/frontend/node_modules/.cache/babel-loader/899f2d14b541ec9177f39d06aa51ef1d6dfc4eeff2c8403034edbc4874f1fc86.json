{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/Debug.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport api from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Debug = () => {\n  _s();\n  const [apiStatus, setApiStatus] = useState('Testing...');\n  const [exams, setExams] = useState([]);\n  const [loginStatus, setLoginStatus] = useState('Not tested');\n  const [token, setToken] = useState(localStorage.getItem('token'));\n  useEffect(() => {\n    testAPI();\n  }, []);\n  const testAPI = async () => {\n    try {\n      // Test basic API connection\n      const response = await fetch('http://localhost:5000/api/test');\n      const data = await response.json();\n      setApiStatus(`✅ API Connected: ${data.message}`);\n\n      // Test exams endpoint\n      const examsResponse = await fetch('http://localhost:5000/api/exams');\n      const examsData = await examsResponse.json();\n      setExams(examsData);\n    } catch (error) {\n      setApiStatus(`❌ API Error: ${error.message}`);\n    }\n  };\n  const testLogin = async () => {\n    try {\n      const response = await api.post('/auth/login', {\n        email: '<EMAIL>',\n        password: 'password123'\n      });\n      localStorage.setItem('token', response.data.token);\n      setToken(response.data.token);\n      setLoginStatus(`✅ Login successful: ${response.data.user.name}`);\n    } catch (error) {\n      setLoginStatus(`❌ Login failed: ${error.message}`);\n    }\n  };\n  const testStartExam = async examId => {\n    try {\n      const response = await api.post(`/exams/${examId}/start`);\n      alert(`✅ Exam started successfully! Attempt ID: ${response.data.attempt}`);\n    } catch (error) {\n      var _error$response, _error$response$data;\n      alert(`❌ Failed to start exam: ${((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || error.message}`);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      children: \"Debug Page\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '20px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"API Status\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: apiStatus\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: testAPI,\n        className: \"btn\",\n        children: \"Test API\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 59,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '20px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Authentication\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [\"Status: \", loginStatus]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [\"Token: \", token ? `${token.substring(0, 20)}...` : 'None']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: testLogin,\n        className: \"btn\",\n        children: \"Test Login\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 65,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '20px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: [\"Available Exams (\", exams.length, \")\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 9\n      }, this), exams.map(exam => {\n        var _exam$questions;\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            border: '1px solid #ccc',\n            padding: '10px',\n            margin: '10px 0'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: exam.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: exam.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 77,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"Questions: \", (_exam$questions = exam.questions) === null || _exam$questions === void 0 ? void 0 : _exam$questions.length]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => testStartExam(exam._id),\n            className: \"btn\",\n            disabled: !token,\n            children: \"Test Start Exam\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 13\n          }, this)]\n        }, exam._id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 11\n        }, this);\n      })]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 72,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Environment\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [\"API URL: \", process.env.REACT_APP_API_URL]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [\"Current URL: \", window.location.href]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 56,\n    columnNumber: 5\n  }, this);\n};\n_s(Debug, \"1t0geW4WB6Mckq7TM27gWoVN0MY=\");\n_c = Debug;\nexport default Debug;\nvar _c;\n$RefreshReg$(_c, \"Debug\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "api", "jsxDEV", "_jsxDEV", "Debug", "_s", "api<PERSON><PERSON>us", "setApiStatus", "exams", "setExams", "loginStatus", "setLoginStatus", "token", "setToken", "localStorage", "getItem", "testAPI", "response", "fetch", "data", "json", "message", "examsResponse", "examsData", "error", "testLogin", "post", "email", "password", "setItem", "user", "name", "testStartExam", "examId", "alert", "attempt", "_error$response", "_error$response$data", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "marginBottom", "onClick", "substring", "length", "map", "exam", "_exam$questions", "border", "padding", "margin", "title", "description", "questions", "_id", "disabled", "process", "env", "REACT_APP_API_URL", "window", "location", "href", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/Debug.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport api from '../services/api';\n\nconst Debug = () => {\n  const [apiStatus, setApiStatus] = useState('Testing...');\n  const [exams, setExams] = useState([]);\n  const [loginStatus, setLoginStatus] = useState('Not tested');\n  const [token, setToken] = useState(localStorage.getItem('token'));\n\n  useEffect(() => {\n    testAPI();\n  }, []);\n\n  const testAPI = async () => {\n    try {\n      // Test basic API connection\n      const response = await fetch('http://localhost:5000/api/test');\n      const data = await response.json();\n      setApiStatus(`✅ API Connected: ${data.message}`);\n      \n      // Test exams endpoint\n      const examsResponse = await fetch('http://localhost:5000/api/exams');\n      const examsData = await examsResponse.json();\n      setExams(examsData);\n      \n    } catch (error) {\n      setApiStatus(`❌ API Error: ${error.message}`);\n    }\n  };\n\n  const testLogin = async () => {\n    try {\n      const response = await api.post('/auth/login', {\n        email: '<EMAIL>',\n        password: 'password123'\n      });\n      \n      localStorage.setItem('token', response.data.token);\n      setToken(response.data.token);\n      setLoginStatus(`✅ Login successful: ${response.data.user.name}`);\n    } catch (error) {\n      setLoginStatus(`❌ Login failed: ${error.message}`);\n    }\n  };\n\n  const testStartExam = async (examId) => {\n    try {\n      const response = await api.post(`/exams/${examId}/start`);\n      alert(`✅ Exam started successfully! Attempt ID: ${response.data.attempt}`);\n    } catch (error) {\n      alert(`❌ Failed to start exam: ${error.response?.data?.message || error.message}`);\n    }\n  };\n\n  return (\n    <div className=\"container\">\n      <h1>Debug Page</h1>\n      \n      <div style={{marginBottom: '20px'}}>\n        <h3>API Status</h3>\n        <p>{apiStatus}</p>\n        <button onClick={testAPI} className=\"btn\">Test API</button>\n      </div>\n\n      <div style={{marginBottom: '20px'}}>\n        <h3>Authentication</h3>\n        <p>Status: {loginStatus}</p>\n        <p>Token: {token ? `${token.substring(0, 20)}...` : 'None'}</p>\n        <button onClick={testLogin} className=\"btn\">Test Login</button>\n      </div>\n\n      <div style={{marginBottom: '20px'}}>\n        <h3>Available Exams ({exams.length})</h3>\n        {exams.map(exam => (\n          <div key={exam._id} style={{border: '1px solid #ccc', padding: '10px', margin: '10px 0'}}>\n            <h4>{exam.title}</h4>\n            <p>{exam.description}</p>\n            <p>Questions: {exam.questions?.length}</p>\n            <button \n              onClick={() => testStartExam(exam._id)} \n              className=\"btn\"\n              disabled={!token}\n            >\n              Test Start Exam\n            </button>\n          </div>\n        ))}\n      </div>\n\n      <div>\n        <h3>Environment</h3>\n        <p>API URL: {process.env.REACT_APP_API_URL}</p>\n        <p>Current URL: {window.location.href}</p>\n      </div>\n    </div>\n  );\n};\n\nexport default Debug;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,GAAG,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClB,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGR,QAAQ,CAAC,YAAY,CAAC;EACxD,MAAM,CAACS,KAAK,EAAEC,QAAQ,CAAC,GAAGV,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACW,WAAW,EAAEC,cAAc,CAAC,GAAGZ,QAAQ,CAAC,YAAY,CAAC;EAC5D,MAAM,CAACa,KAAK,EAAEC,QAAQ,CAAC,GAAGd,QAAQ,CAACe,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;EAEjEf,SAAS,CAAC,MAAM;IACdgB,OAAO,CAAC,CAAC;EACX,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,OAAO,GAAG,MAAAA,CAAA,KAAY;IAC1B,IAAI;MACF;MACA,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,gCAAgC,CAAC;MAC9D,MAAMC,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAClCb,YAAY,CAAC,oBAAoBY,IAAI,CAACE,OAAO,EAAE,CAAC;;MAEhD;MACA,MAAMC,aAAa,GAAG,MAAMJ,KAAK,CAAC,iCAAiC,CAAC;MACpE,MAAMK,SAAS,GAAG,MAAMD,aAAa,CAACF,IAAI,CAAC,CAAC;MAC5CX,QAAQ,CAACc,SAAS,CAAC;IAErB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdjB,YAAY,CAAC,gBAAgBiB,KAAK,CAACH,OAAO,EAAE,CAAC;IAC/C;EACF,CAAC;EAED,MAAMI,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI;MACF,MAAMR,QAAQ,GAAG,MAAMhB,GAAG,CAACyB,IAAI,CAAC,aAAa,EAAE;QAC7CC,KAAK,EAAE,kBAAkB;QACzBC,QAAQ,EAAE;MACZ,CAAC,CAAC;MAEFd,YAAY,CAACe,OAAO,CAAC,OAAO,EAAEZ,QAAQ,CAACE,IAAI,CAACP,KAAK,CAAC;MAClDC,QAAQ,CAACI,QAAQ,CAACE,IAAI,CAACP,KAAK,CAAC;MAC7BD,cAAc,CAAC,uBAAuBM,QAAQ,CAACE,IAAI,CAACW,IAAI,CAACC,IAAI,EAAE,CAAC;IAClE,CAAC,CAAC,OAAOP,KAAK,EAAE;MACdb,cAAc,CAAC,mBAAmBa,KAAK,CAACH,OAAO,EAAE,CAAC;IACpD;EACF,CAAC;EAED,MAAMW,aAAa,GAAG,MAAOC,MAAM,IAAK;IACtC,IAAI;MACF,MAAMhB,QAAQ,GAAG,MAAMhB,GAAG,CAACyB,IAAI,CAAC,UAAUO,MAAM,QAAQ,CAAC;MACzDC,KAAK,CAAC,4CAA4CjB,QAAQ,CAACE,IAAI,CAACgB,OAAO,EAAE,CAAC;IAC5E,CAAC,CAAC,OAAOX,KAAK,EAAE;MAAA,IAAAY,eAAA,EAAAC,oBAAA;MACdH,KAAK,CAAC,2BAA2B,EAAAE,eAAA,GAAAZ,KAAK,CAACP,QAAQ,cAAAmB,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBjB,IAAI,cAAAkB,oBAAA,uBAApBA,oBAAA,CAAsBhB,OAAO,KAAIG,KAAK,CAACH,OAAO,EAAE,CAAC;IACpF;EACF,CAAC;EAED,oBACElB,OAAA;IAAKmC,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxBpC,OAAA;MAAAoC,QAAA,EAAI;IAAU;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAEnBxC,OAAA;MAAKyC,KAAK,EAAE;QAACC,YAAY,EAAE;MAAM,CAAE;MAAAN,QAAA,gBACjCpC,OAAA;QAAAoC,QAAA,EAAI;MAAU;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnBxC,OAAA;QAAAoC,QAAA,EAAIjC;MAAS;QAAAkC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAClBxC,OAAA;QAAQ2C,OAAO,EAAE9B,OAAQ;QAACsB,SAAS,EAAC,KAAK;QAAAC,QAAA,EAAC;MAAQ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxD,CAAC,eAENxC,OAAA;MAAKyC,KAAK,EAAE;QAACC,YAAY,EAAE;MAAM,CAAE;MAAAN,QAAA,gBACjCpC,OAAA;QAAAoC,QAAA,EAAI;MAAc;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACvBxC,OAAA;QAAAoC,QAAA,GAAG,UAAQ,EAAC7B,WAAW;MAAA;QAAA8B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC5BxC,OAAA;QAAAoC,QAAA,GAAG,SAAO,EAAC3B,KAAK,GAAG,GAAGA,KAAK,CAACmC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,GAAG,MAAM;MAAA;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC/DxC,OAAA;QAAQ2C,OAAO,EAAErB,SAAU;QAACa,SAAS,EAAC,KAAK;QAAAC,QAAA,EAAC;MAAU;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5D,CAAC,eAENxC,OAAA;MAAKyC,KAAK,EAAE;QAACC,YAAY,EAAE;MAAM,CAAE;MAAAN,QAAA,gBACjCpC,OAAA;QAAAoC,QAAA,GAAI,mBAAiB,EAAC/B,KAAK,CAACwC,MAAM,EAAC,GAAC;MAAA;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EACxCnC,KAAK,CAACyC,GAAG,CAACC,IAAI;QAAA,IAAAC,eAAA;QAAA,oBACbhD,OAAA;UAAoByC,KAAK,EAAE;YAACQ,MAAM,EAAE,gBAAgB;YAAEC,OAAO,EAAE,MAAM;YAAEC,MAAM,EAAE;UAAQ,CAAE;UAAAf,QAAA,gBACvFpC,OAAA;YAAAoC,QAAA,EAAKW,IAAI,CAACK;UAAK;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACrBxC,OAAA;YAAAoC,QAAA,EAAIW,IAAI,CAACM;UAAW;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzBxC,OAAA;YAAAoC,QAAA,GAAG,aAAW,GAAAY,eAAA,GAACD,IAAI,CAACO,SAAS,cAAAN,eAAA,uBAAdA,eAAA,CAAgBH,MAAM;UAAA;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1CxC,OAAA;YACE2C,OAAO,EAAEA,CAAA,KAAMd,aAAa,CAACkB,IAAI,CAACQ,GAAG,CAAE;YACvCpB,SAAS,EAAC,KAAK;YACfqB,QAAQ,EAAE,CAAC/C,KAAM;YAAA2B,QAAA,EAClB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA,GAVDO,IAAI,CAACQ,GAAG;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAWb,CAAC;MAAA,CACP,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAENxC,OAAA;MAAAoC,QAAA,gBACEpC,OAAA;QAAAoC,QAAA,EAAI;MAAW;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACpBxC,OAAA;QAAAoC,QAAA,GAAG,WAAS,EAACqB,OAAO,CAACC,GAAG,CAACC,iBAAiB;MAAA;QAAAtB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC/CxC,OAAA;QAAAoC,QAAA,GAAG,eAAa,EAACwB,MAAM,CAACC,QAAQ,CAACC,IAAI;MAAA;QAAAzB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACtC,EAAA,CA7FID,KAAK;AAAA8D,EAAA,GAAL9D,KAAK;AA+FX,eAAeA,KAAK;AAAC,IAAA8D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}