{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/ExamTaking.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport api from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ExamTaking = () => {\n  _s();\n  var _exam$subject, _answers$question$_id2;\n  const {\n    examId\n  } = useParams();\n  const navigate = useNavigate();\n  const [exam, setExam] = useState(null);\n  const [attemptId, setAttemptId] = useState(null);\n  const [answers, setAnswers] = useState({});\n  const [currentQuestion, setCurrentQuestion] = useState(0);\n  const [timeLeft, setTimeLeft] = useState(0);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [submitting, setSubmitting] = useState(false);\n  useEffect(() => {\n    const token = localStorage.getItem('token');\n    if (!token) {\n      navigate('/login');\n      return;\n    }\n    startExam();\n  }, [examId, navigate]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  useEffect(() => {\n    if (timeLeft > 0) {\n      const timer = setTimeout(() => setTimeLeft(timeLeft - 1), 1000);\n      return () => clearTimeout(timer);\n    } else if (timeLeft === 0 && exam) {\n      handleSubmit();\n    }\n  }, [timeLeft, exam]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  const startExam = async () => {\n    try {\n      console.log('Starting exam with ID:', examId);\n      const response = await api.post(`/exams/${examId}/start`);\n      console.log('Start exam response:', response.data);\n      setExam(response.data.exam);\n      setAttemptId(response.data.attempt);\n      setTimeLeft(response.data.exam.duration * 60); // Convert minutes to seconds\n\n      // Initialize answers\n      const initialAnswers = {};\n      response.data.exam.questions.forEach(question => {\n        initialAnswers[question._id] = {\n          question: question._id,\n          selectedOptions: [],\n          answer: ''\n        };\n      });\n      setAnswers(initialAnswers);\n      console.log('Exam started successfully');\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('Error starting exam:', error);\n      setError(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Failed to start exam');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleAnswerChange = (questionId, value, isMultiple = false) => {\n    setAnswers(prev => ({\n      ...prev,\n      [questionId]: {\n        ...prev[questionId],\n        selectedOptions: isMultiple ? prev[questionId].selectedOptions.includes(value) ? prev[questionId].selectedOptions.filter(opt => opt !== value) : [...prev[questionId].selectedOptions, value] : [value]\n      }\n    }));\n  };\n  const handleSubmit = async () => {\n    if (submitting) return;\n    setSubmitting(true);\n    try {\n      const answersArray = Object.values(answers);\n      await api.post(`/exam-attempts/${attemptId}/submit`, {\n        answers: answersArray\n      });\n      navigate(`/results/${attemptId}`);\n    } catch (error) {\n      setError('Failed to submit exam');\n      setSubmitting(false);\n    }\n  };\n  const formatTime = seconds => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins}:${secs.toString().padStart(2, '0')}`;\n  };\n  if (loading) return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"loading\",\n    children: \"Starting exam...\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 99,\n    columnNumber: 23\n  }, this);\n  if (error) return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"error\",\n    children: error\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 100,\n    columnNumber: 21\n  }, this);\n  if (!exam) return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"error\",\n    children: \"Exam not found\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 101,\n    columnNumber: 21\n  }, this);\n  const question = exam.questions[currentQuestion];\n  const progress = (currentQuestion + 1) / exam.questions.length * 100;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"exam-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"exam-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"exam-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: exam.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"exam-meta\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"subject-badge\",\n            children: (_exam$subject = exam.subject) === null || _exam$subject === void 0 ? void 0 : _exam$subject.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"question-counter\",\n            children: [\"Question \", currentQuestion + 1, \" of \", exam.questions.length]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 114,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"timer-container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"timer\",\n          children: [\"\\u23F0 \", formatTime(timeLeft)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 109,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"progress-bar\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"progress-fill\",\n        style: {\n          width: `${progress}%`\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"progress-text\",\n        children: [Math.round(progress), \"% Complete\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 133,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 128,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"question-nav\",\n      children: exam.questions.map((_, index) => {\n        var _answers$exam$questio;\n        return /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `nav-dot ${index === currentQuestion ? 'active' : ''} ${((_answers$exam$questio = answers[exam.questions[index]._id]) === null || _answers$exam$questio === void 0 ? void 0 : _answers$exam$questio.selectedOptions.length) > 0 ? 'answered' : ''}`,\n          onClick: () => setCurrentQuestion(index),\n          children: index + 1\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 11\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 137,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"question-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"question-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"question-type-badge\",\n          children: question.type === 'MCQ' ? 'Multiple Choice' : question.type === 'TRUE_FALSE' ? 'True/False' : 'Multiple Answer'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 11\n        }, this), question.type === 'MULTI_ANSWER' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"instruction\",\n          children: \"Select all correct answers\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"question-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"question-text\",\n          children: [\"Q\", currentQuestion + 1, \". \", question.text]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"options-container\",\n          children: question.options.map((option, index) => {\n            var _answers$question$_id;\n            const isSelected = (_answers$question$_id = answers[question._id]) === null || _answers$question$_id === void 0 ? void 0 : _answers$question$_id.selectedOptions.includes(option.text);\n            const optionLetter = String.fromCharCode(65 + index);\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `option-card ${isSelected ? 'selected' : ''}`,\n              onClick: () => handleAnswerChange(question._id, option.text, question.type === 'MULTI_ANSWER'),\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"option-selector\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: question.type === 'MULTI_ANSWER' ? 'checkbox' : 'radio',\n                  name: `question_${question._id}`,\n                  value: option.text,\n                  checked: isSelected,\n                  onChange: () => {} // Handled by onClick above\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 185,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"option-letter\",\n                  children: optionLetter\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 192,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"option-text\",\n                children: option.text\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 194,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 152,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"exam-navigation\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"nav-left\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-secondary\",\n          onClick: () => setCurrentQuestion(Math.max(0, currentQuestion - 1)),\n          disabled: currentQuestion === 0,\n          children: \"\\u2190 Previous\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 204,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"nav-center\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"answer-status\",\n          children: ((_answers$question$_id2 = answers[question._id]) === null || _answers$question$_id2 === void 0 ? void 0 : _answers$question$_id2.selectedOptions.length) > 0 ? /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"answered\",\n            children: \"\\u2713 Answered\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"unanswered\",\n            children: \"Not answered\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 214,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"nav-right\",\n        children: currentQuestion < exam.questions.length - 1 ? /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary\",\n          onClick: () => setCurrentQuestion(currentQuestion + 1),\n          children: \"Next \\u2192\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-success\",\n          onClick: handleSubmit,\n          disabled: submitting,\n          children: submitting ? 'Submitting...' : 'Submit Exam 📝'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 224,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 203,\n      columnNumber: 7\n    }, this), currentQuestion === exam.questions.length - 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"exam-summary\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        children: \"\\uD83D\\uDCCB Exam Summary\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 247,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"summary-stats\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"stat-number\",\n            children: Object.values(answers).filter(a => a.selectedOptions.length > 0).length\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"stat-label\",\n            children: \"Answered\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"stat-number\",\n            children: exam.questions.length - Object.values(answers).filter(a => a.selectedOptions.length > 0).length\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"stat-label\",\n            children: \"Unanswered\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"stat-number\",\n            children: exam.questions.length\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"stat-label\",\n            children: \"Total\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 261,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 248,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"summary-note\",\n        children: \"Review your answers before submitting. You cannot change them after submission.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 266,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 246,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 107,\n    columnNumber: 5\n  }, this);\n};\n_s(ExamTaking, \"QklB5P3SQZq5oYTBHAuKel33V8A=\", false, function () {\n  return [useParams, useNavigate];\n});\n_c = ExamTaking;\nexport default ExamTaking;\nvar _c;\n$RefreshReg$(_c, \"ExamTaking\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useNavigate", "api", "jsxDEV", "_jsxDEV", "ExamTaking", "_s", "_exam$subject", "_answers$question$_id2", "examId", "navigate", "exam", "setExam", "attemptId", "setAttemptId", "answers", "setAnswers", "currentQuestion", "setCurrentQuestion", "timeLeft", "setTimeLeft", "loading", "setLoading", "error", "setError", "submitting", "setSubmitting", "token", "localStorage", "getItem", "startExam", "timer", "setTimeout", "clearTimeout", "handleSubmit", "console", "log", "response", "post", "data", "attempt", "duration", "initialAnswers", "questions", "for<PERSON>ach", "question", "_id", "selectedOptions", "answer", "_error$response", "_error$response$data", "message", "handleAnswerChange", "questionId", "value", "isMultiple", "prev", "includes", "filter", "opt", "answersArray", "Object", "values", "formatTime", "seconds", "mins", "Math", "floor", "secs", "toString", "padStart", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "progress", "length", "title", "subject", "name", "style", "width", "round", "map", "_", "index", "_answers$exam$questio", "onClick", "type", "text", "options", "option", "_answers$question$_id", "isSelected", "optionLetter", "String", "fromCharCode", "checked", "onChange", "max", "disabled", "a", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/ExamTaking.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport api from '../services/api';\n\nconst ExamTaking = () => {\n  const { examId } = useParams();\n  const navigate = useNavigate();\n  const [exam, setExam] = useState(null);\n  const [attemptId, setAttemptId] = useState(null);\n  const [answers, setAnswers] = useState({});\n  const [currentQuestion, setCurrentQuestion] = useState(0);\n  const [timeLeft, setTimeLeft] = useState(0);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [submitting, setSubmitting] = useState(false);\n\n  useEffect(() => {\n    const token = localStorage.getItem('token');\n    if (!token) {\n      navigate('/login');\n      return;\n    }\n\n    startExam();\n  }, [examId, navigate]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  useEffect(() => {\n    if (timeLeft > 0) {\n      const timer = setTimeout(() => setTimeLeft(timeLeft - 1), 1000);\n      return () => clearTimeout(timer);\n    } else if (timeLeft === 0 && exam) {\n      handleSubmit();\n    }\n  }, [timeLeft, exam]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  const startExam = async () => {\n    try {\n      console.log('Starting exam with ID:', examId);\n      const response = await api.post(`/exams/${examId}/start`);\n      console.log('Start exam response:', response.data);\n\n      setExam(response.data.exam);\n      setAttemptId(response.data.attempt);\n      setTimeLeft(response.data.exam.duration * 60); // Convert minutes to seconds\n\n      // Initialize answers\n      const initialAnswers = {};\n      response.data.exam.questions.forEach(question => {\n        initialAnswers[question._id] = {\n          question: question._id,\n          selectedOptions: [],\n          answer: ''\n        };\n      });\n      setAnswers(initialAnswers);\n      console.log('Exam started successfully');\n    } catch (error) {\n      console.error('Error starting exam:', error);\n      setError(error.response?.data?.message || 'Failed to start exam');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleAnswerChange = (questionId, value, isMultiple = false) => {\n    setAnswers(prev => ({\n      ...prev,\n      [questionId]: {\n        ...prev[questionId],\n        selectedOptions: isMultiple \n          ? (prev[questionId].selectedOptions.includes(value)\n              ? prev[questionId].selectedOptions.filter(opt => opt !== value)\n              : [...prev[questionId].selectedOptions, value])\n          : [value]\n      }\n    }));\n  };\n\n  const handleSubmit = async () => {\n    if (submitting) return;\n    \n    setSubmitting(true);\n    try {\n      const answersArray = Object.values(answers);\n      await api.post(`/exam-attempts/${attemptId}/submit`, { answers: answersArray });\n      navigate(`/results/${attemptId}`);\n    } catch (error) {\n      setError('Failed to submit exam');\n      setSubmitting(false);\n    }\n  };\n\n  const formatTime = (seconds) => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins}:${secs.toString().padStart(2, '0')}`;\n  };\n\n  if (loading) return <div className=\"loading\">Starting exam...</div>;\n  if (error) return <div className=\"error\">{error}</div>;\n  if (!exam) return <div className=\"error\">Exam not found</div>;\n\n  const question = exam.questions[currentQuestion];\n  const progress = ((currentQuestion + 1) / exam.questions.length) * 100;\n\n  return (\n    <div className=\"exam-container\">\n      {/* Header with timer and progress */}\n      <div className=\"exam-header\">\n        <div className=\"exam-info\">\n          <h2>{exam.title}</h2>\n          <div className=\"exam-meta\">\n            <span className=\"subject-badge\">{exam.subject?.name}</span>\n            <span className=\"question-counter\">\n              Question {currentQuestion + 1} of {exam.questions.length}\n            </span>\n          </div>\n        </div>\n\n        <div className=\"timer-container\">\n          <div className=\"timer\">\n            ⏰ {formatTime(timeLeft)}\n          </div>\n        </div>\n      </div>\n\n      {/* Progress bar */}\n      <div className=\"progress-bar\">\n        <div\n          className=\"progress-fill\"\n          style={{ width: `${progress}%` }}\n        ></div>\n        <span className=\"progress-text\">{Math.round(progress)}% Complete</span>\n      </div>\n\n      {/* Question navigation dots */}\n      <div className=\"question-nav\">\n        {exam.questions.map((_, index) => (\n          <button\n            key={index}\n            className={`nav-dot ${index === currentQuestion ? 'active' : ''} ${\n              answers[exam.questions[index]._id]?.selectedOptions.length > 0 ? 'answered' : ''\n            }`}\n            onClick={() => setCurrentQuestion(index)}\n          >\n            {index + 1}\n          </button>\n        ))}\n      </div>\n\n      {/* Main question area */}\n      <div className=\"question-section\">\n        <div className=\"question-header\">\n          <div className=\"question-type-badge\">\n            {question.type === 'MCQ' ? 'Multiple Choice' :\n             question.type === 'TRUE_FALSE' ? 'True/False' :\n             'Multiple Answer'}\n          </div>\n          {question.type === 'MULTI_ANSWER' && (\n            <div className=\"instruction\">Select all correct answers</div>\n          )}\n        </div>\n\n        <div className=\"question-content\">\n          <h3 className=\"question-text\">\n            Q{currentQuestion + 1}. {question.text}\n          </h3>\n\n          <div className=\"options-container\">\n            {question.options.map((option, index) => {\n              const isSelected = answers[question._id]?.selectedOptions.includes(option.text);\n              const optionLetter = String.fromCharCode(65 + index);\n\n              return (\n                <div\n                  key={index}\n                  className={`option-card ${isSelected ? 'selected' : ''}`}\n                  onClick={() => handleAnswerChange(\n                    question._id,\n                    option.text,\n                    question.type === 'MULTI_ANSWER'\n                  )}\n                >\n                  <div className=\"option-selector\">\n                    <input\n                      type={question.type === 'MULTI_ANSWER' ? 'checkbox' : 'radio'}\n                      name={`question_${question._id}`}\n                      value={option.text}\n                      checked={isSelected}\n                      onChange={() => {}} // Handled by onClick above\n                    />\n                    <span className=\"option-letter\">{optionLetter}</span>\n                  </div>\n                  <div className=\"option-text\">{option.text}</div>\n                </div>\n              );\n            })}\n          </div>\n        </div>\n      </div>\n\n      {/* Navigation buttons */}\n      <div className=\"exam-navigation\">\n        <div className=\"nav-left\">\n          <button\n            className=\"btn btn-secondary\"\n            onClick={() => setCurrentQuestion(Math.max(0, currentQuestion - 1))}\n            disabled={currentQuestion === 0}\n          >\n            ← Previous\n          </button>\n        </div>\n\n        <div className=\"nav-center\">\n          <div className=\"answer-status\">\n            {answers[question._id]?.selectedOptions.length > 0 ? (\n              <span className=\"answered\">✓ Answered</span>\n            ) : (\n              <span className=\"unanswered\">Not answered</span>\n            )}\n          </div>\n        </div>\n\n        <div className=\"nav-right\">\n          {currentQuestion < exam.questions.length - 1 ? (\n            <button\n              className=\"btn btn-primary\"\n              onClick={() => setCurrentQuestion(currentQuestion + 1)}\n            >\n              Next →\n            </button>\n          ) : (\n            <button\n              className=\"btn btn-success\"\n              onClick={handleSubmit}\n              disabled={submitting}\n            >\n              {submitting ? 'Submitting...' : 'Submit Exam 📝'}\n            </button>\n          )}\n        </div>\n      </div>\n\n      {/* Summary panel for final review */}\n      {currentQuestion === exam.questions.length - 1 && (\n        <div className=\"exam-summary\">\n          <h4>📋 Exam Summary</h4>\n          <div className=\"summary-stats\">\n            <div className=\"stat\">\n              <span className=\"stat-number\">\n                {Object.values(answers).filter(a => a.selectedOptions.length > 0).length}\n              </span>\n              <span className=\"stat-label\">Answered</span>\n            </div>\n            <div className=\"stat\">\n              <span className=\"stat-number\">\n                {exam.questions.length - Object.values(answers).filter(a => a.selectedOptions.length > 0).length}\n              </span>\n              <span className=\"stat-label\">Unanswered</span>\n            </div>\n            <div className=\"stat\">\n              <span className=\"stat-number\">{exam.questions.length}</span>\n              <span className=\"stat-label\">Total</span>\n            </div>\n          </div>\n          <p className=\"summary-note\">\n            Review your answers before submitting. You cannot change them after submission.\n          </p>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default ExamTaking;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,OAAOC,GAAG,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,aAAA,EAAAC,sBAAA;EACvB,MAAM;IAAEC;EAAO,CAAC,GAAGT,SAAS,CAAC,CAAC;EAC9B,MAAMU,QAAQ,GAAGT,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACU,IAAI,EAAEC,OAAO,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACe,SAAS,EAAEC,YAAY,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1C,MAAM,CAACmB,eAAe,EAAEC,kBAAkB,CAAC,GAAGpB,QAAQ,CAAC,CAAC,CAAC;EACzD,MAAM,CAACqB,QAAQ,EAAEC,WAAW,CAAC,GAAGtB,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAACuB,OAAO,EAAEC,UAAU,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACyB,KAAK,EAAEC,QAAQ,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC2B,UAAU,EAAEC,aAAa,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EAEnDC,SAAS,CAAC,MAAM;IACd,MAAM4B,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAI,CAACF,KAAK,EAAE;MACVjB,QAAQ,CAAC,QAAQ,CAAC;MAClB;IACF;IAEAoB,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAACrB,MAAM,EAAEC,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAExBX,SAAS,CAAC,MAAM;IACd,IAAIoB,QAAQ,GAAG,CAAC,EAAE;MAChB,MAAMY,KAAK,GAAGC,UAAU,CAAC,MAAMZ,WAAW,CAACD,QAAQ,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC;MAC/D,OAAO,MAAMc,YAAY,CAACF,KAAK,CAAC;IAClC,CAAC,MAAM,IAAIZ,QAAQ,KAAK,CAAC,IAAIR,IAAI,EAAE;MACjCuB,YAAY,CAAC,CAAC;IAChB;EACF,CAAC,EAAE,CAACf,QAAQ,EAAER,IAAI,CAAC,CAAC,CAAC,CAAC;;EAEtB,MAAMmB,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI;MACFK,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE3B,MAAM,CAAC;MAC7C,MAAM4B,QAAQ,GAAG,MAAMnC,GAAG,CAACoC,IAAI,CAAC,UAAU7B,MAAM,QAAQ,CAAC;MACzD0B,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEC,QAAQ,CAACE,IAAI,CAAC;MAElD3B,OAAO,CAACyB,QAAQ,CAACE,IAAI,CAAC5B,IAAI,CAAC;MAC3BG,YAAY,CAACuB,QAAQ,CAACE,IAAI,CAACC,OAAO,CAAC;MACnCpB,WAAW,CAACiB,QAAQ,CAACE,IAAI,CAAC5B,IAAI,CAAC8B,QAAQ,GAAG,EAAE,CAAC,CAAC,CAAC;;MAE/C;MACA,MAAMC,cAAc,GAAG,CAAC,CAAC;MACzBL,QAAQ,CAACE,IAAI,CAAC5B,IAAI,CAACgC,SAAS,CAACC,OAAO,CAACC,QAAQ,IAAI;QAC/CH,cAAc,CAACG,QAAQ,CAACC,GAAG,CAAC,GAAG;UAC7BD,QAAQ,EAAEA,QAAQ,CAACC,GAAG;UACtBC,eAAe,EAAE,EAAE;UACnBC,MAAM,EAAE;QACV,CAAC;MACH,CAAC,CAAC;MACFhC,UAAU,CAAC0B,cAAc,CAAC;MAC1BP,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;IAC1C,CAAC,CAAC,OAAOb,KAAK,EAAE;MAAA,IAAA0B,eAAA,EAAAC,oBAAA;MACdf,OAAO,CAACZ,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5CC,QAAQ,CAAC,EAAAyB,eAAA,GAAA1B,KAAK,CAACc,QAAQ,cAAAY,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBV,IAAI,cAAAW,oBAAA,uBAApBA,oBAAA,CAAsBC,OAAO,KAAI,sBAAsB,CAAC;IACnE,CAAC,SAAS;MACR7B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM8B,kBAAkB,GAAGA,CAACC,UAAU,EAAEC,KAAK,EAAEC,UAAU,GAAG,KAAK,KAAK;IACpEvC,UAAU,CAACwC,IAAI,KAAK;MAClB,GAAGA,IAAI;MACP,CAACH,UAAU,GAAG;QACZ,GAAGG,IAAI,CAACH,UAAU,CAAC;QACnBN,eAAe,EAAEQ,UAAU,GACtBC,IAAI,CAACH,UAAU,CAAC,CAACN,eAAe,CAACU,QAAQ,CAACH,KAAK,CAAC,GAC7CE,IAAI,CAACH,UAAU,CAAC,CAACN,eAAe,CAACW,MAAM,CAACC,GAAG,IAAIA,GAAG,KAAKL,KAAK,CAAC,GAC7D,CAAC,GAAGE,IAAI,CAACH,UAAU,CAAC,CAACN,eAAe,EAAEO,KAAK,CAAC,GAChD,CAACA,KAAK;MACZ;IACF,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMpB,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAIT,UAAU,EAAE;IAEhBC,aAAa,CAAC,IAAI,CAAC;IACnB,IAAI;MACF,MAAMkC,YAAY,GAAGC,MAAM,CAACC,MAAM,CAAC/C,OAAO,CAAC;MAC3C,MAAMb,GAAG,CAACoC,IAAI,CAAC,kBAAkBzB,SAAS,SAAS,EAAE;QAAEE,OAAO,EAAE6C;MAAa,CAAC,CAAC;MAC/ElD,QAAQ,CAAC,YAAYG,SAAS,EAAE,CAAC;IACnC,CAAC,CAAC,OAAOU,KAAK,EAAE;MACdC,QAAQ,CAAC,uBAAuB,CAAC;MACjCE,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAMqC,UAAU,GAAIC,OAAO,IAAK;IAC9B,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;IACrC,MAAMI,IAAI,GAAGJ,OAAO,GAAG,EAAE;IACzB,OAAO,GAAGC,IAAI,IAAIG,IAAI,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EACtD,CAAC;EAED,IAAIjD,OAAO,EAAE,oBAAOjB,OAAA;IAAKmE,SAAS,EAAC,SAAS;IAAAC,QAAA,EAAC;EAAgB;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;EACnE,IAAIrD,KAAK,EAAE,oBAAOnB,OAAA;IAAKmE,SAAS,EAAC,OAAO;IAAAC,QAAA,EAAEjD;EAAK;IAAAkD,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EACtD,IAAI,CAACjE,IAAI,EAAE,oBAAOP,OAAA;IAAKmE,SAAS,EAAC,OAAO;IAAAC,QAAA,EAAC;EAAc;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;EAE7D,MAAM/B,QAAQ,GAAGlC,IAAI,CAACgC,SAAS,CAAC1B,eAAe,CAAC;EAChD,MAAM4D,QAAQ,GAAI,CAAC5D,eAAe,GAAG,CAAC,IAAIN,IAAI,CAACgC,SAAS,CAACmC,MAAM,GAAI,GAAG;EAEtE,oBACE1E,OAAA;IAAKmE,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAE7BpE,OAAA;MAAKmE,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BpE,OAAA;QAAKmE,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBpE,OAAA;UAAAoE,QAAA,EAAK7D,IAAI,CAACoE;QAAK;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACrBxE,OAAA;UAAKmE,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBpE,OAAA;YAAMmE,SAAS,EAAC,eAAe;YAAAC,QAAA,GAAAjE,aAAA,GAAEI,IAAI,CAACqE,OAAO,cAAAzE,aAAA,uBAAZA,aAAA,CAAc0E;UAAI;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC3DxE,OAAA;YAAMmE,SAAS,EAAC,kBAAkB;YAAAC,QAAA,GAAC,WACxB,EAACvD,eAAe,GAAG,CAAC,EAAC,MAAI,EAACN,IAAI,CAACgC,SAAS,CAACmC,MAAM;UAAA;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENxE,OAAA;QAAKmE,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9BpE,OAAA;UAAKmE,SAAS,EAAC,OAAO;UAAAC,QAAA,GAAC,SACnB,EAACT,UAAU,CAAC5C,QAAQ,CAAC;QAAA;UAAAsD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNxE,OAAA;MAAKmE,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC3BpE,OAAA;QACEmE,SAAS,EAAC,eAAe;QACzBW,KAAK,EAAE;UAAEC,KAAK,EAAE,GAAGN,QAAQ;QAAI;MAAE;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CAAC,eACPxE,OAAA;QAAMmE,SAAS,EAAC,eAAe;QAAAC,QAAA,GAAEN,IAAI,CAACkB,KAAK,CAACP,QAAQ,CAAC,EAAC,YAAU;MAAA;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpE,CAAC,eAGNxE,OAAA;MAAKmE,SAAS,EAAC,cAAc;MAAAC,QAAA,EAC1B7D,IAAI,CAACgC,SAAS,CAAC0C,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK;QAAA,IAAAC,qBAAA;QAAA,oBAC3BpF,OAAA;UAEEmE,SAAS,EAAE,WAAWgB,KAAK,KAAKtE,eAAe,GAAG,QAAQ,GAAG,EAAE,IAC7D,EAAAuE,qBAAA,GAAAzE,OAAO,CAACJ,IAAI,CAACgC,SAAS,CAAC4C,KAAK,CAAC,CAACzC,GAAG,CAAC,cAAA0C,qBAAA,uBAAlCA,qBAAA,CAAoCzC,eAAe,CAAC+B,MAAM,IAAG,CAAC,GAAG,UAAU,GAAG,EAAE,EAC/E;UACHW,OAAO,EAAEA,CAAA,KAAMvE,kBAAkB,CAACqE,KAAK,CAAE;UAAAf,QAAA,EAExCe,KAAK,GAAG;QAAC,GANLA,KAAK;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAOJ,CAAC;MAAA,CACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNxE,OAAA;MAAKmE,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BpE,OAAA;QAAKmE,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BpE,OAAA;UAAKmE,SAAS,EAAC,qBAAqB;UAAAC,QAAA,EACjC3B,QAAQ,CAAC6C,IAAI,KAAK,KAAK,GAAG,iBAAiB,GAC3C7C,QAAQ,CAAC6C,IAAI,KAAK,YAAY,GAAG,YAAY,GAC7C;QAAiB;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC,EACL/B,QAAQ,CAAC6C,IAAI,KAAK,cAAc,iBAC/BtF,OAAA;UAAKmE,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAC;QAA0B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAC7D;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENxE,OAAA;QAAKmE,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BpE,OAAA;UAAImE,SAAS,EAAC,eAAe;UAAAC,QAAA,GAAC,GAC3B,EAACvD,eAAe,GAAG,CAAC,EAAC,IAAE,EAAC4B,QAAQ,CAAC8C,IAAI;QAAA;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC,eAELxE,OAAA;UAAKmE,SAAS,EAAC,mBAAmB;UAAAC,QAAA,EAC/B3B,QAAQ,CAAC+C,OAAO,CAACP,GAAG,CAAC,CAACQ,MAAM,EAAEN,KAAK,KAAK;YAAA,IAAAO,qBAAA;YACvC,MAAMC,UAAU,IAAAD,qBAAA,GAAG/E,OAAO,CAAC8B,QAAQ,CAACC,GAAG,CAAC,cAAAgD,qBAAA,uBAArBA,qBAAA,CAAuB/C,eAAe,CAACU,QAAQ,CAACoC,MAAM,CAACF,IAAI,CAAC;YAC/E,MAAMK,YAAY,GAAGC,MAAM,CAACC,YAAY,CAAC,EAAE,GAAGX,KAAK,CAAC;YAEpD,oBACEnF,OAAA;cAEEmE,SAAS,EAAE,eAAewB,UAAU,GAAG,UAAU,GAAG,EAAE,EAAG;cACzDN,OAAO,EAAEA,CAAA,KAAMrC,kBAAkB,CAC/BP,QAAQ,CAACC,GAAG,EACZ+C,MAAM,CAACF,IAAI,EACX9C,QAAQ,CAAC6C,IAAI,KAAK,cACpB,CAAE;cAAAlB,QAAA,gBAEFpE,OAAA;gBAAKmE,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9BpE,OAAA;kBACEsF,IAAI,EAAE7C,QAAQ,CAAC6C,IAAI,KAAK,cAAc,GAAG,UAAU,GAAG,OAAQ;kBAC9DT,IAAI,EAAE,YAAYpC,QAAQ,CAACC,GAAG,EAAG;kBACjCQ,KAAK,EAAEuC,MAAM,CAACF,IAAK;kBACnBQ,OAAO,EAAEJ,UAAW;kBACpBK,QAAQ,EAAEA,CAAA,KAAM,CAAC,CAAE,CAAC;gBAAA;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC,eACFxE,OAAA;kBAAMmE,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAEwB;gBAAY;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD,CAAC,eACNxE,OAAA;gBAAKmE,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAEqB,MAAM,CAACF;cAAI;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA,GAlB3CW,KAAK;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAmBP,CAAC;UAEV,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNxE,OAAA;MAAKmE,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9BpE,OAAA;QAAKmE,SAAS,EAAC,UAAU;QAAAC,QAAA,eACvBpE,OAAA;UACEmE,SAAS,EAAC,mBAAmB;UAC7BkB,OAAO,EAAEA,CAAA,KAAMvE,kBAAkB,CAACgD,IAAI,CAACmC,GAAG,CAAC,CAAC,EAAEpF,eAAe,GAAG,CAAC,CAAC,CAAE;UACpEqF,QAAQ,EAAErF,eAAe,KAAK,CAAE;UAAAuD,QAAA,EACjC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENxE,OAAA;QAAKmE,SAAS,EAAC,YAAY;QAAAC,QAAA,eACzBpE,OAAA;UAAKmE,SAAS,EAAC,eAAe;UAAAC,QAAA,EAC3B,EAAAhE,sBAAA,GAAAO,OAAO,CAAC8B,QAAQ,CAACC,GAAG,CAAC,cAAAtC,sBAAA,uBAArBA,sBAAA,CAAuBuC,eAAe,CAAC+B,MAAM,IAAG,CAAC,gBAChD1E,OAAA;YAAMmE,SAAS,EAAC,UAAU;YAAAC,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,gBAE5CxE,OAAA;YAAMmE,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAChD;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENxE,OAAA;QAAKmE,SAAS,EAAC,WAAW;QAAAC,QAAA,EACvBvD,eAAe,GAAGN,IAAI,CAACgC,SAAS,CAACmC,MAAM,GAAG,CAAC,gBAC1C1E,OAAA;UACEmE,SAAS,EAAC,iBAAiB;UAC3BkB,OAAO,EAAEA,CAAA,KAAMvE,kBAAkB,CAACD,eAAe,GAAG,CAAC,CAAE;UAAAuD,QAAA,EACxD;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,gBAETxE,OAAA;UACEmE,SAAS,EAAC,iBAAiB;UAC3BkB,OAAO,EAAEvD,YAAa;UACtBoE,QAAQ,EAAE7E,UAAW;UAAA+C,QAAA,EAEpB/C,UAAU,GAAG,eAAe,GAAG;QAAgB;UAAAgD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C;MACT;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL3D,eAAe,KAAKN,IAAI,CAACgC,SAAS,CAACmC,MAAM,GAAG,CAAC,iBAC5C1E,OAAA;MAAKmE,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC3BpE,OAAA;QAAAoE,QAAA,EAAI;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACxBxE,OAAA;QAAKmE,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BpE,OAAA;UAAKmE,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBpE,OAAA;YAAMmE,SAAS,EAAC,aAAa;YAAAC,QAAA,EAC1BX,MAAM,CAACC,MAAM,CAAC/C,OAAO,CAAC,CAAC2C,MAAM,CAAC6C,CAAC,IAAIA,CAAC,CAACxD,eAAe,CAAC+B,MAAM,GAAG,CAAC,CAAC,CAACA;UAAM;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpE,CAAC,eACPxE,OAAA;YAAMmE,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC,eACNxE,OAAA;UAAKmE,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBpE,OAAA;YAAMmE,SAAS,EAAC,aAAa;YAAAC,QAAA,EAC1B7D,IAAI,CAACgC,SAAS,CAACmC,MAAM,GAAGjB,MAAM,CAACC,MAAM,CAAC/C,OAAO,CAAC,CAAC2C,MAAM,CAAC6C,CAAC,IAAIA,CAAC,CAACxD,eAAe,CAAC+B,MAAM,GAAG,CAAC,CAAC,CAACA;UAAM;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5F,CAAC,eACPxE,OAAA;YAAMmE,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CAAC,eACNxE,OAAA;UAAKmE,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBpE,OAAA;YAAMmE,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAE7D,IAAI,CAACgC,SAAS,CAACmC;UAAM;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC5DxE,OAAA;YAAMmE,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNxE,OAAA;QAAGmE,SAAS,EAAC,cAAc;QAAAC,QAAA,EAAC;MAE5B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACtE,EAAA,CA5QID,UAAU;EAAA,QACKL,SAAS,EACXC,WAAW;AAAA;AAAAuG,EAAA,GAFxBnG,UAAU;AA8QhB,eAAeA,UAAU;AAAC,IAAAmG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}