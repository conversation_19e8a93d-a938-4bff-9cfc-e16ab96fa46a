[{"/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/index.js": "1", "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/App.js": "2", "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/Login.jsx": "3", "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/Home.jsx": "4", "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/Register.jsx": "5", "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/ExamResults.jsx": "6", "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/ExamList.jsx": "7", "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/ExamTaking.jsx": "8", "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/services/api.js": "9", "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/Debug.jsx": "10"}, {"size": 197, "mtime": 1751896792581, "results": "11", "hashOfConfig": "12"}, {"size": 971, "mtime": 1751897182491, "results": "13", "hashOfConfig": "12"}, {"size": 2189, "mtime": 1751897351367, "results": "14", "hashOfConfig": "12"}, {"size": 902, "mtime": 1751895554750, "results": "15", "hashOfConfig": "12"}, {"size": 2330, "mtime": 1751895581828, "results": "16", "hashOfConfig": "12"}, {"size": 2200, "mtime": 1751897127699, "results": "17", "hashOfConfig": "12"}, {"size": 2199, "mtime": 1751897337730, "results": "18", "hashOfConfig": "12"}, {"size": 5272, "mtime": 1751897090046, "results": "19", "hashOfConfig": "12"}, {"size": 333, "mtime": 1748868434017, "results": "20", "hashOfConfig": "12"}, {"size": 3027, "mtime": 1751897159151, "results": "21", "hashOfConfig": "12"}, {"filePath": "22", "messages": "23", "suppressedMessages": "24", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "hd94b0", {"filePath": "25", "messages": "26", "suppressedMessages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/index.js", [], [], "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/App.js", [], [], "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/Login.jsx", [], [], "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/Home.jsx", [], [], "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/Register.jsx", [], [], "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/ExamResults.jsx", [], [], "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/ExamList.jsx", [], [], "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/ExamTaking.jsx", [], ["52", "53"], "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/services/api.js", [], [], "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/Debug.jsx", [], [], {"ruleId": "54", "severity": 1, "message": "55", "line": 25, "column": 6, "nodeType": "56", "endLine": 25, "endColumn": 24, "suggestions": "57", "suppressions": "58"}, {"ruleId": "54", "severity": 1, "message": "59", "line": 34, "column": 6, "nodeType": "56", "endLine": 34, "endColumn": 22, "suggestions": "60", "suppressions": "61"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'startExam'. Either include it or remove the dependency array.", "ArrayExpression", ["62"], ["63"], "React Hook useEffect has a missing dependency: 'handleSubmit'. Either include it or remove the dependency array.", ["64"], ["65"], {"desc": "66", "fix": "67"}, {"kind": "68", "justification": "69"}, {"desc": "70", "fix": "71"}, {"kind": "68", "justification": "69"}, "Update the dependencies array to be: [examId, navigate, startExam]", {"range": "72", "text": "73"}, "directive", "", "Update the dependencies array to be: [timeLeft, exam, handleSubmit]", {"range": "74", "text": "75"}, [792, 810], "[examId, navigate, startExam]", [1094, 1110], "[timeLeft, exam, handleSubmit]"]