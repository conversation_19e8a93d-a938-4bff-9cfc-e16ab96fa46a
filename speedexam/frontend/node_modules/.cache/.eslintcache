[{"/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/index.js": "1", "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/App.js": "2", "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/Login.jsx": "3", "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/Home.jsx": "4", "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/Register.jsx": "5", "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/ExamResults.jsx": "6", "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/ExamList.jsx": "7", "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/ExamTaking.jsx": "8", "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/services/api.js": "9", "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/Debug.jsx": "10", "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/AdminLogin.jsx": "11", "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/StudentDashboard.jsx": "12", "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/AdminDashboard.jsx": "13", "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/CreateExam.jsx": "14", "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/ExamPreview.jsx": "15", "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/SimpleExamTaking.jsx": "16"}, {"size": 197, "mtime": 1751896792581, "results": "17", "hashOfConfig": "18"}, {"size": 1691, "mtime": 1752072530308, "results": "19", "hashOfConfig": "18"}, {"size": 2242, "mtime": 1751898208557, "results": "20", "hashOfConfig": "18"}, {"size": 4544, "mtime": 1751898146146, "results": "21", "hashOfConfig": "18"}, {"size": 2330, "mtime": 1751895581828, "results": "22", "hashOfConfig": "18"}, {"size": 11483, "mtime": 1751899493164, "results": "23", "hashOfConfig": "18"}, {"size": 2199, "mtime": 1751897337730, "results": "24", "hashOfConfig": "18"}, {"size": 15141, "mtime": 1751901667429, "results": "25", "hashOfConfig": "18"}, {"size": 364, "mtime": 1751901650725, "results": "26", "hashOfConfig": "18"}, {"size": 3027, "mtime": 1751897159151, "results": "27", "hashOfConfig": "18"}, {"size": 2849, "mtime": 1751897961368, "results": "28", "hashOfConfig": "18"}, {"size": 7504, "mtime": 1751897997681, "results": "29", "hashOfConfig": "18"}, {"size": 6809, "mtime": 1751898843348, "results": "30", "hashOfConfig": "18"}, {"size": 24536, "mtime": 1751898657652, "results": "31", "hashOfConfig": "18"}, {"size": 9253, "mtime": 1751898900046, "results": "32", "hashOfConfig": "18"}, {"size": 9091, "mtime": 1752072504759, "results": "33", "hashOfConfig": "18"}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "hd94b0", {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/index.js", [], [], "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/App.js", ["82"], [], "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/Login.jsx", [], [], "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/Home.jsx", [], [], "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/Register.jsx", [], [], "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/ExamResults.jsx", ["83"], [], "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/ExamList.jsx", [], [], "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/ExamTaking.jsx", ["84", "85"], ["86", "87"], "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/services/api.js", [], [], "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/Debug.jsx", [], [], "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/AdminLogin.jsx", [], [], "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/StudentDashboard.jsx", [], [], "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/AdminDashboard.jsx", [], [], "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/CreateExam.jsx", ["88"], [], "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/ExamPreview.jsx", [], [], "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/SimpleExamTaking.jsx", ["89", "90"], [], {"ruleId": "91", "severity": 1, "message": "92", "line": 7, "column": 8, "nodeType": "93", "messageId": "94", "endLine": 7, "endColumn": 18}, {"ruleId": "95", "severity": 1, "message": "96", "line": 87, "column": 6, "nodeType": "97", "endLine": 87, "endColumn": 17, "suggestions": "98"}, {"ruleId": "91", "severity": 1, "message": "99", "line": 16, "column": 10, "nodeType": "93", "messageId": "94", "endLine": 16, "endColumn": 23}, {"ruleId": "95", "severity": 1, "message": "100", "line": 172, "column": 6, "nodeType": "97", "endLine": 172, "endColumn": 38, "suggestions": "101"}, {"ruleId": "95", "severity": 1, "message": "102", "line": 31, "column": 6, "nodeType": "97", "endLine": 31, "endColumn": 24, "suggestions": "103", "suppressions": "104"}, {"ruleId": "95", "severity": 1, "message": "105", "line": 40, "column": 6, "nodeType": "97", "endLine": 40, "endColumn": 22, "suggestions": "106", "suppressions": "107"}, {"ruleId": "91", "severity": 1, "message": "108", "line": 3, "column": 8, "nodeType": "93", "messageId": "94", "endLine": 3, "endColumn": 11}, {"ruleId": "95", "severity": 1, "message": "102", "line": 24, "column": 6, "nodeType": "97", "endLine": 24, "endColumn": 24, "suggestions": "109"}, {"ruleId": "95", "severity": 1, "message": "105", "line": 33, "column": 6, "nodeType": "97", "endLine": 33, "endColumn": 22, "suggestions": "110"}, "no-unused-vars", "'ExamTaking' is defined but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'results?.percentage', 'results?.timeTaken', and 'results?.totalQuestions'. Either include them or remove the dependency array. You can also replace multiple useState variables with useReducer if 'setDetailedResults' needs the current value of 'results.timeTaken'.", "ArrayExpression", ["111"], "'examStartTime' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'handleTabSwitch'. Either include it or remove the dependency array.", ["112"], "React Hook useEffect has a missing dependency: 'startExam'. Either include it or remove the dependency array.", ["113"], ["114"], "React Hook useEffect has a missing dependency: 'handleSubmit'. Either include it or remove the dependency array.", ["115"], ["116"], "'api' is defined but never used.", ["117"], ["118"], {"desc": "119", "fix": "120"}, {"desc": "121", "fix": "122"}, {"desc": "123", "fix": "124"}, {"kind": "125", "justification": "126"}, {"desc": "127", "fix": "128"}, {"kind": "125", "justification": "126"}, {"desc": "123", "fix": "129"}, {"desc": "127", "fix": "130"}, "Update the dependencies array to be: [attemptId, results?.percentage, results?.timeTaken, results?.totalQuestions]", {"range": "131", "text": "132"}, "Update the dependencies array to be: [exam, handleTabSwitch, submitting, warningCount]", {"range": "133", "text": "134"}, "Update the dependencies array to be: [examId, navigate, startExam]", {"range": "135", "text": "136"}, "directive", "", "Update the dependencies array to be: [timeLeft, exam, handleSubmit]", {"range": "137", "text": "138"}, {"range": "139", "text": "136"}, {"range": "140", "text": "138"}, [3085, 3096], "[attemptId, results?.percentage, results?.timeTaken, results?.totalQuestions]", [5670, 5702], "[exam, handleTabSwitch, submitting, warningCount]", [1158, 1176], "[examId, navigate, startExam]", [1460, 1476], "[timeLeft, exam, handleSubmit]", [797, 815], [1048, 1064]]