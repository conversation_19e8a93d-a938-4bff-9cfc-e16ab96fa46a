[{"/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/index.js": "1", "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/App.js": "2", "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/Login.jsx": "3", "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/Home.jsx": "4", "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/Register.jsx": "5", "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/ExamResults.jsx": "6", "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/ExamList.jsx": "7", "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/ExamTaking.jsx": "8", "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/services/api.js": "9"}, {"size": 197, "mtime": 1751896792581, "results": "10", "hashOfConfig": "11"}, {"size": 882, "mtime": 1751895530210, "results": "12", "hashOfConfig": "11"}, {"size": 2029, "mtime": 1751895569477, "results": "13", "hashOfConfig": "11"}, {"size": 902, "mtime": 1751895554750, "results": "14", "hashOfConfig": "11"}, {"size": 2330, "mtime": 1751895581828, "results": "15", "hashOfConfig": "11"}, {"size": 1240, "mtime": 1748868049581, "results": "16", "hashOfConfig": "11"}, {"size": 2007, "mtime": 1751895602391, "results": "17", "hashOfConfig": "11"}, {"size": 5066, "mtime": 1751896826654, "results": "18", "hashOfConfig": "11"}, {"size": 333, "mtime": 1748868434017, "results": "19", "hashOfConfig": "11"}, {"filePath": "20", "messages": "21", "suppressedMessages": "22", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "hd94b0", {"filePath": "23", "messages": "24", "suppressedMessages": "25", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/index.js", [], [], "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/App.js", [], [], "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/Login.jsx", [], [], "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/Home.jsx", [], [], "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/Register.jsx", [], [], "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/ExamResults.jsx", [], [], "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/ExamList.jsx", [], [], "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/ExamTaking.jsx", [], ["47", "48"], "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/services/api.js", [], [], {"ruleId": "49", "severity": 1, "message": "50", "line": 25, "column": 6, "nodeType": "51", "endLine": 25, "endColumn": 24, "suggestions": "52", "suppressions": "53"}, {"ruleId": "49", "severity": 1, "message": "54", "line": 34, "column": 6, "nodeType": "51", "endLine": 34, "endColumn": 22, "suggestions": "55", "suppressions": "56"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'startExam'. Either include it or remove the dependency array.", "ArrayExpression", ["57"], ["58"], "React Hook useEffect has a missing dependency: 'handleSubmit'. Either include it or remove the dependency array.", ["59"], ["60"], {"desc": "61", "fix": "62"}, {"kind": "63", "justification": "64"}, {"desc": "65", "fix": "66"}, {"kind": "63", "justification": "64"}, "Update the dependencies array to be: [examId, navigate, startExam]", {"range": "67", "text": "68"}, "directive", "", "Update the dependencies array to be: [timeLeft, exam, handleSubmit]", {"range": "69", "text": "70"}, [792, 810], "[examId, navigate, startExam]", [1094, 1110], "[timeLeft, exam, handleSubmit]"]