[{"/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/index.js": "1", "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/App.js": "2", "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/Login.jsx": "3", "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/Home.jsx": "4", "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/Register.jsx": "5", "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/ExamResults.jsx": "6", "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/ExamList.jsx": "7", "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/ExamTaking.jsx": "8", "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/services/api.js": "9", "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/Debug.jsx": "10", "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/AdminLogin.jsx": "11", "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/StudentDashboard.jsx": "12", "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/AdminDashboard.jsx": "13", "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/CreateExam.jsx": "14", "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/ExamPreview.jsx": "15"}, {"size": 197, "mtime": 1751896792581, "results": "16", "hashOfConfig": "17"}, {"size": 1628, "mtime": 1751898990570, "results": "18", "hashOfConfig": "17"}, {"size": 2242, "mtime": 1751898208557, "results": "19", "hashOfConfig": "17"}, {"size": 4544, "mtime": 1751898146146, "results": "20", "hashOfConfig": "17"}, {"size": 2330, "mtime": 1751895581828, "results": "21", "hashOfConfig": "17"}, {"size": 11483, "mtime": 1751899493164, "results": "22", "hashOfConfig": "17"}, {"size": 2199, "mtime": 1751897337730, "results": "23", "hashOfConfig": "17"}, {"size": 14737, "mtime": 1751899689892, "results": "24", "hashOfConfig": "17"}, {"size": 820, "mtime": 1751901199617, "results": "25", "hashOfConfig": "17"}, {"size": 3027, "mtime": 1751897159151, "results": "26", "hashOfConfig": "17"}, {"size": 2849, "mtime": 1751897961368, "results": "27", "hashOfConfig": "17"}, {"size": 7504, "mtime": 1751897997681, "results": "28", "hashOfConfig": "17"}, {"size": 6809, "mtime": 1751898843348, "results": "29", "hashOfConfig": "17"}, {"size": 24536, "mtime": 1751898657652, "results": "30", "hashOfConfig": "17"}, {"size": 9253, "mtime": 1751898900046, "results": "31", "hashOfConfig": "17"}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "hd94b0", {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/index.js", [], [], "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/App.js", [], [], "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/Login.jsx", [], [], "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/Home.jsx", [], [], "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/Register.jsx", [], [], "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/ExamResults.jsx", ["77"], [], "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/ExamList.jsx", [], [], "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/ExamTaking.jsx", ["78", "79"], ["80", "81"], "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/services/api.js", [], [], "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/Debug.jsx", [], [], "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/AdminLogin.jsx", [], [], "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/StudentDashboard.jsx", [], [], "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/AdminDashboard.jsx", [], [], "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/CreateExam.jsx", ["82"], [], "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/ExamPreview.jsx", [], [], {"ruleId": "83", "severity": 1, "message": "84", "line": 87, "column": 6, "nodeType": "85", "endLine": 87, "endColumn": 17, "suggestions": "86"}, {"ruleId": "87", "severity": 1, "message": "88", "line": 16, "column": 10, "nodeType": "89", "messageId": "90", "endLine": 16, "endColumn": 23}, {"ruleId": "83", "severity": 1, "message": "91", "line": 163, "column": 6, "nodeType": "85", "endLine": 163, "endColumn": 38, "suggestions": "92"}, {"ruleId": "83", "severity": 1, "message": "93", "line": 31, "column": 6, "nodeType": "85", "endLine": 31, "endColumn": 24, "suggestions": "94", "suppressions": "95"}, {"ruleId": "83", "severity": 1, "message": "96", "line": 40, "column": 6, "nodeType": "85", "endLine": 40, "endColumn": 22, "suggestions": "97", "suppressions": "98"}, {"ruleId": "87", "severity": 1, "message": "99", "line": 3, "column": 8, "nodeType": "89", "messageId": "90", "endLine": 3, "endColumn": 11}, "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'results?.percentage', 'results?.timeTaken', and 'results?.totalQuestions'. Either include them or remove the dependency array. You can also replace multiple useState variables with useReducer if 'setDetailedResults' needs the current value of 'results.timeTaken'.", "ArrayExpression", ["100"], "no-unused-vars", "'examStartTime' is assigned a value but never used.", "Identifier", "unusedVar", "React Hook useEffect has a missing dependency: 'handleTabSwitch'. Either include it or remove the dependency array.", ["101"], "React Hook useEffect has a missing dependency: 'startExam'. Either include it or remove the dependency array.", ["102"], ["103"], "React Hook useEffect has a missing dependency: 'handleSubmit'. Either include it or remove the dependency array.", ["104"], ["105"], "'api' is defined but never used.", {"desc": "106", "fix": "107"}, {"desc": "108", "fix": "109"}, {"desc": "110", "fix": "111"}, {"kind": "112", "justification": "113"}, {"desc": "114", "fix": "115"}, {"kind": "112", "justification": "113"}, "Update the dependencies array to be: [attemptId, results?.percentage, results?.timeTaken, results?.totalQuestions]", {"range": "116", "text": "117"}, "Update the dependencies array to be: [exam, handleTabSwitch, submitting, warningCount]", {"range": "118", "text": "119"}, "Update the dependencies array to be: [examId, navigate, startExam]", {"range": "120", "text": "121"}, "directive", "", "Update the dependencies array to be: [timeLeft, exam, handleSubmit]", {"range": "122", "text": "123"}, [3085, 3096], "[attemptId, results?.percentage, results?.timeTaken, results?.totalQuestions]", [5266, 5298], "[exam, handleTabSwitch, submitting, warningCount]", [1158, 1176], "[examId, navigate, startExam]", [1460, 1476], "[timeLeft, exam, handleSubmit]"]