[{"/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/index.js": "1", "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/App.js": "2", "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/Login.jsx": "3", "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/Home.jsx": "4", "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/Register.jsx": "5", "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/ExamResults.jsx": "6", "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/ExamList.jsx": "7", "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/ExamTaking.jsx": "8", "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/services/api.js": "9", "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/Debug.jsx": "10", "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/AdminLogin.jsx": "11", "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/StudentDashboard.jsx": "12", "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/AdminDashboard.jsx": "13"}, {"size": 197, "mtime": 1751896792581, "results": "14", "hashOfConfig": "15"}, {"size": 1321, "mtime": 1751898110648, "results": "16", "hashOfConfig": "15"}, {"size": 2242, "mtime": 1751898208557, "results": "17", "hashOfConfig": "15"}, {"size": 4544, "mtime": 1751898146146, "results": "18", "hashOfConfig": "15"}, {"size": 2330, "mtime": 1751895581828, "results": "19", "hashOfConfig": "15"}, {"size": 3723, "mtime": 1751897761700, "results": "20", "hashOfConfig": "15"}, {"size": 2199, "mtime": 1751897337730, "results": "21", "hashOfConfig": "15"}, {"size": 7474, "mtime": 1751897666212, "results": "22", "hashOfConfig": "15"}, {"size": 333, "mtime": 1748868434017, "results": "23", "hashOfConfig": "15"}, {"size": 3027, "mtime": 1751897159151, "results": "24", "hashOfConfig": "15"}, {"size": 2849, "mtime": 1751897961368, "results": "25", "hashOfConfig": "15"}, {"size": 7504, "mtime": 1751897997681, "results": "26", "hashOfConfig": "15"}, {"size": 6734, "mtime": 1751897942532, "results": "27", "hashOfConfig": "15"}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "hd94b0", {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/index.js", [], [], "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/App.js", [], [], "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/Login.jsx", [], [], "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/Home.jsx", [], [], "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/Register.jsx", [], [], "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/ExamResults.jsx", [], [], "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/ExamList.jsx", [], [], "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/ExamTaking.jsx", [], ["67", "68"], "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/services/api.js", [], [], "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/Debug.jsx", [], [], "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/AdminLogin.jsx", [], [], "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/StudentDashboard.jsx", [], [], "/Users/<USER>/Documents/augment-projects/SpeedExam/speedexam/frontend/src/pages/AdminDashboard.jsx", [], [], {"ruleId": "69", "severity": 1, "message": "70", "line": 25, "column": 6, "nodeType": "71", "endLine": 25, "endColumn": 24, "suggestions": "72", "suppressions": "73"}, {"ruleId": "69", "severity": 1, "message": "74", "line": 34, "column": 6, "nodeType": "71", "endLine": 34, "endColumn": 22, "suggestions": "75", "suppressions": "76"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'startExam'. Either include it or remove the dependency array.", "ArrayExpression", ["77"], ["78"], "React Hook useEffect has a missing dependency: 'handleSubmit'. Either include it or remove the dependency array.", ["79"], ["80"], {"desc": "81", "fix": "82"}, {"kind": "83", "justification": "84"}, {"desc": "85", "fix": "86"}, {"kind": "83", "justification": "84"}, "Update the dependencies array to be: [examId, navigate, startExam]", {"range": "87", "text": "88"}, "directive", "", "Update the dependencies array to be: [timeLeft, exam, handleSubmit]", {"range": "89", "text": "90"}, [792, 810], "[examId, navigate, startExam]", [1094, 1110], "[timeLeft, exam, handleSubmit]"]