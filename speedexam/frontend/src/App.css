.App {
  text-align: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.btn {
  background-color: #007bff;
  color: white;
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  text-decoration: none;
  display: inline-block;
  margin: 5px;
}

.btn:hover {
  background-color: #0056b3;
}

.btn-secondary {
  background-color: #6c757d;
}

.btn-secondary:hover {
  background-color: #545b62;
}

.form-group {
  margin-bottom: 15px;
  text-align: left;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

.form-group input {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-sizing: border-box;
}

.exam-card {
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 20px;
  margin: 10px 0;
  text-align: left;
}

.exam-card h3 {
  margin-top: 0;
  color: #333;
}

.question-container {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin: 20px 0;
  text-align: left;
}

.question-text {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 15px;
}

.option {
  margin: 10px 0;
}

.option input {
  margin-right: 10px;
}

.timer {
  position: fixed;
  top: 20px;
  right: 20px;
  background: #dc3545;
  color: white;
  padding: 10px 15px;
  border-radius: 4px;
  font-weight: bold;
}

.results-container {
  text-align: center;
  padding: 20px;
}

.score-summary {
  background: #e9ecef;
  padding: 20px;
  border-radius: 8px;
  margin: 20px 0;
}

.score-summary h2 {
  color: #28a745;
  margin: 0 0 10px 0;
}

.error {
  color: #dc3545;
  background: #f8d7da;
  padding: 10px;
  border-radius: 4px;
  margin: 10px 0;
}

.success {
  color: #155724;
  background: #d4edda;
  padding: 10px;
  border-radius: 4px;
  margin: 10px 0;
}

.loading {
  text-align: center;
  padding: 40px;
  font-size: 18px;
}

/* Exam Taking Styles */
.exam-header {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  border-left: 4px solid #007bff;
}

.exam-header h2 {
  margin: 0 0 10px 0;
  color: #2c3e50;
}

.question-nav {
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.nav-btn {
  background: #e9ecef;
  border: 1px solid #ced4da;
  color: #495057;
  padding: 8px 12px;
  margin: 2px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
}

.nav-btn:hover {
  background: #dee2e6;
}

.nav-btn.active {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

.nav-btn.answered {
  background: #28a745;
  color: white;
  border-color: #28a745;
}

.nav-btn.answered.active {
  background: #1e7e34;
  border-color: #1e7e34;
}

.question-container {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 25px;
  margin-bottom: 20px;
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #e9ecef;
}

.question-header h3 {
  margin: 0;
  color: #2c3e50;
}

.question-type {
  background: #6f42c1;
  color: white;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
}

.question-text {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 20px;
  line-height: 1.5;
  color: #2c3e50;
}

.options {
  margin-bottom: 20px;
}

.option {
  margin: 12px 0;
  padding: 15px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #fafafa;
}

.option:hover {
  border-color: #007bff;
  background: #f0f8ff;
}

.option.selected {
  border-color: #28a745;
  background: #f8fff9;
  box-shadow: 0 2px 4px rgba(40, 167, 69, 0.2);
}

.option label {
  display: flex;
  align-items: center;
  cursor: pointer;
  margin: 0;
  width: 100%;
}

.option input {
  margin-right: 12px;
  transform: scale(1.2);
}

.option-letter {
  background: #6c757d;
  color: white;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 12px;
  margin-right: 12px;
}

.option.selected .option-letter {
  background: #28a745;
}

.option-text {
  flex: 1;
  font-size: 16px;
}

.answer-status {
  text-align: center;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 15px;
}

.answer-status .answered {
  color: #28a745;
  font-weight: bold;
}

.answer-status .unanswered {
  color: #dc3545;
  font-weight: bold;
}

.navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 20px;
}

.btn-success {
  background: #28a745;
  color: white;
  font-size: 16px;
  padding: 12px 24px;
}

.btn-success:hover {
  background: #218838;
}

.exam-summary {
  background: #e7f3ff;
  border: 1px solid #b3d9ff;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
}

.exam-summary h4 {
  color: #0056b3;
  margin-bottom: 15px;
}

.progress-bar {
  background: #e9ecef;
  height: 20px;
  border-radius: 10px;
  overflow: hidden;
  margin-top: 10px;
}

.progress {
  background: linear-gradient(90deg, #007bff, #28a745);
  height: 100%;
  border-radius: 10px;
  transition: width 0.3s ease;
}

/* Results Page Styles */
.results-header {
  text-align: center;
  margin-bottom: 30px;
}

.results-header h1 {
  color: #2c3e50;
  margin-bottom: 10px;
}

.exam-title {
  color: #7f8c8d;
  font-size: 18px;
  font-weight: 500;
}

.score-card {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 40px;
  background: white;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  margin-bottom: 30px;
}

.score-circle {
  text-align: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  width: 150px;
  height: 150px;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.score-number {
  font-size: 36px;
  font-weight: bold;
  margin-bottom: 5px;
}

.score-label {
  font-size: 14px;
  opacity: 0.9;
}

.score-details {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
}

.score-item {
  text-align: center;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 10px;
  border: 2px solid #e9ecef;
}

.score-value {
  display: block;
  font-size: 24px;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 5px;
}

.score-desc {
  font-size: 12px;
  color: #7f8c8d;
  text-transform: uppercase;
  font-weight: 500;
}

.performance-message {
  background: white;
  padding: 25px;
  border-radius: 15px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  margin-bottom: 30px;
  text-align: center;
}

.excellent {
  color: #27ae60;
}

.very-good {
  color: #2980b9;
}

.good {
  color: #16a085;
}

.average {
  color: #f39c12;
}

.needs-improvement {
  color: #e74c3c;
}

.performance-message h3 {
  margin-bottom: 10px;
  font-size: 24px;
}

.performance-message p {
  margin: 0;
  font-size: 16px;
  opacity: 0.8;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 30px;
}

.action-buttons .btn {
  padding: 12px 24px;
  font-size: 16px;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
  .score-card {
    flex-direction: column;
    gap: 20px;
  }

  .score-details {
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
  }

  .action-buttons {
    flex-direction: column;
    align-items: center;
  }
}

/* Enhanced Exam Taking Styles */
.exam-container {
  max-width: 900px;
  margin: 0 auto;
  padding: 20px;
  background: #f8f9fa;
  min-height: 100vh;
}

.exam-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  margin-bottom: 20px;
}

.exam-info h2 {
  margin: 0 0 10px 0;
  color: #2c3e50;
}

.exam-meta {
  display: flex;
  gap: 15px;
  align-items: center;
}

.subject-badge {
  background: #3498db;
  color: white;
  padding: 4px 12px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: bold;
}

.question-counter {
  color: #7f8c8d;
  font-weight: 500;
}

.timer-container {
  text-align: right;
}

.timer {
  background: #e74c3c;
  color: white;
  padding: 10px 20px;
  border-radius: 25px;
  font-weight: bold;
  font-size: 18px;
  box-shadow: 0 2px 5px rgba(231, 76, 60, 0.3);
}

.progress-bar {
  background: #ecf0f1;
  height: 8px;
  border-radius: 4px;
  margin-bottom: 20px;
  position: relative;
  overflow: hidden;
}

.progress-fill {
  background: linear-gradient(90deg, #3498db, #2ecc71);
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-text {
  position: absolute;
  top: -25px;
  right: 0;
  font-size: 12px;
  color: #7f8c8d;
  font-weight: 500;
}

.question-nav {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin-bottom: 30px;
  flex-wrap: wrap;
}

.nav-dot {
  width: 35px;
  height: 35px;
  border-radius: 50%;
  border: 2px solid #bdc3c7;
  background: white;
  color: #7f8c8d;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
}

.nav-dot:hover {
  border-color: #3498db;
  color: #3498db;
}

.nav-dot.active {
  background: #3498db;
  color: white;
  border-color: #3498db;
}

.nav-dot.answered {
  background: #2ecc71;
  color: white;
  border-color: #2ecc71;
}

.nav-dot.answered.active {
  background: #27ae60;
  border-color: #27ae60;
}

.question-section {
  background: white;
  border-radius: 10px;
  padding: 30px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  margin-bottom: 20px;
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.question-type-badge {
  background: #9b59b6;
  color: white;
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: bold;
}

.instruction {
  color: #e67e22;
  font-style: italic;
  font-size: 14px;
}

.question-content h3 {
  color: #2c3e50;
  font-size: 20px;
  line-height: 1.5;
  margin-bottom: 25px;
}

.options-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.option-card {
  display: flex;
  align-items: center;
  padding: 15px;
  border: 2px solid #ecf0f1;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #fafafa;
}

.option-card:hover {
  border-color: #3498db;
  background: #f0f8ff;
}

.option-card.selected {
  border-color: #2ecc71;
  background: #f0fff4;
  box-shadow: 0 2px 5px rgba(46, 204, 113, 0.2);
}

.option-selector {
  display: flex;
  align-items: center;
  margin-right: 15px;
}

.option-selector input {
  margin-right: 8px;
  transform: scale(1.2);
}

.option-letter {
  background: #34495e;
  color: white;
  width: 25px;
  height: 25px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 12px;
}

.option-card.selected .option-letter {
  background: #2ecc71;
}

.option-text {
  flex: 1;
  font-size: 16px;
  line-height: 1.4;
}

.exam-navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.nav-left, .nav-right {
  flex: 1;
}

.nav-right {
  text-align: right;
}

.nav-center {
  flex: 1;
  text-align: center;
}

.answer-status .answered {
  color: #2ecc71;
  font-weight: bold;
}

.answer-status .unanswered {
  color: #e74c3c;
  font-weight: bold;
}

.btn-primary {
  background: #3498db;
}

.btn-primary:hover {
  background: #2980b9;
}

.btn-success {
  background: #2ecc71;
  font-size: 16px;
  padding: 12px 24px;
}

.btn-success:hover {
  background: #27ae60;
}

.exam-summary {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 10px;
  padding: 20px;
  margin-top: 20px;
}

.exam-summary h4 {
  color: #856404;
  margin-bottom: 15px;
}

.summary-stats {
  display: flex;
  justify-content: space-around;
  margin-bottom: 15px;
}

.stat {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 24px;
  font-weight: bold;
  color: #2c3e50;
}

.stat-label {
  font-size: 12px;
  color: #7f8c8d;
  text-transform: uppercase;
}

.summary-note {
  color: #856404;
  font-style: italic;
  margin: 0;
  text-align: center;
}
