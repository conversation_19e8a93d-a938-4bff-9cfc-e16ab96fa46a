.App {
  text-align: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.btn {
  background-color: #007bff;
  color: white;
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  text-decoration: none;
  display: inline-block;
  margin: 5px;
}

.btn:hover {
  background-color: #0056b3;
}

.btn-secondary {
  background-color: #6c757d;
}

.btn-secondary:hover {
  background-color: #545b62;
}

.form-group {
  margin-bottom: 15px;
  text-align: left;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

.form-group input {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-sizing: border-box;
}

.exam-card {
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 20px;
  margin: 10px 0;
  text-align: left;
}

.exam-card h3 {
  margin-top: 0;
  color: #333;
}

.question-container {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin: 20px 0;
  text-align: left;
}

.question-text {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 15px;
}

.option {
  margin: 10px 0;
}

.option input {
  margin-right: 10px;
}

.timer {
  position: fixed;
  top: 20px;
  right: 20px;
  background: #dc3545;
  color: white;
  padding: 10px 15px;
  border-radius: 4px;
  font-weight: bold;
}

.results-container {
  text-align: center;
  padding: 20px;
}

.score-summary {
  background: #e9ecef;
  padding: 20px;
  border-radius: 8px;
  margin: 20px 0;
}

.score-summary h2 {
  color: #28a745;
  margin: 0 0 10px 0;
}

.error {
  color: #dc3545;
  background: #f8d7da;
  padding: 10px;
  border-radius: 4px;
  margin: 10px 0;
}

.success {
  color: #155724;
  background: #d4edda;
  padding: 10px;
  border-radius: 4px;
  margin: 10px 0;
}

.loading {
  text-align: center;
  padding: 40px;
  font-size: 18px;
}

/* Exam Taking Styles */
.exam-header {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  border-left: 4px solid #007bff;
}

.exam-header h2 {
  margin: 0 0 10px 0;
  color: #2c3e50;
}

.question-nav {
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.nav-btn {
  background: #e9ecef;
  border: 1px solid #ced4da;
  color: #495057;
  padding: 8px 12px;
  margin: 2px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
}

.nav-btn:hover {
  background: #dee2e6;
}

.nav-btn.active {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

.nav-btn.answered {
  background: #28a745;
  color: white;
  border-color: #28a745;
}

.nav-btn.answered.active {
  background: #1e7e34;
  border-color: #1e7e34;
}

.question-container {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 25px;
  margin-bottom: 20px;
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #e9ecef;
}

.question-header h3 {
  margin: 0;
  color: #2c3e50;
}

.question-type {
  background: #6f42c1;
  color: white;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
}

.question-text {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 20px;
  line-height: 1.5;
  color: #2c3e50;
}

.options {
  margin-bottom: 20px;
}

.option {
  margin: 12px 0;
  padding: 15px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #fafafa;
}

.option:hover {
  border-color: #007bff;
  background: #f0f8ff;
}

.option.selected {
  border-color: #28a745;
  background: #f8fff9;
  box-shadow: 0 2px 4px rgba(40, 167, 69, 0.2);
}

.option label {
  display: flex;
  align-items: center;
  cursor: pointer;
  margin: 0;
  width: 100%;
}

.option input {
  margin-right: 12px;
  transform: scale(1.2);
}

.option-letter {
  background: #6c757d;
  color: white;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 12px;
  margin-right: 12px;
}

.option.selected .option-letter {
  background: #28a745;
}

.option-text {
  flex: 1;
  font-size: 16px;
}

.answer-status {
  text-align: center;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 15px;
}

.answer-status .answered {
  color: #28a745;
  font-weight: bold;
}

.answer-status .unanswered {
  color: #dc3545;
  font-weight: bold;
}

.navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 20px;
}

.btn-success {
  background: #28a745;
  color: white;
  font-size: 16px;
  padding: 12px 24px;
}

.btn-success:hover {
  background: #218838;
}

.exam-summary {
  background: #e7f3ff;
  border: 1px solid #b3d9ff;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
}

.exam-summary h4 {
  color: #0056b3;
  margin-bottom: 15px;
}

.progress-bar {
  background: #e9ecef;
  height: 20px;
  border-radius: 10px;
  overflow: hidden;
  margin-top: 10px;
}

.progress {
  background: linear-gradient(90deg, #007bff, #28a745);
  height: 100%;
  border-radius: 10px;
  transition: width 0.3s ease;
}

/* Results Page Styles */
.results-header {
  text-align: center;
  margin-bottom: 30px;
}

.results-header h1 {
  color: #2c3e50;
  margin-bottom: 10px;
}

.exam-title {
  color: #7f8c8d;
  font-size: 18px;
  font-weight: 500;
}

.score-card {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 40px;
  background: white;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  margin-bottom: 30px;
}

.score-circle {
  text-align: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  width: 150px;
  height: 150px;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.score-number {
  font-size: 36px;
  font-weight: bold;
  margin-bottom: 5px;
}

.score-label {
  font-size: 14px;
  opacity: 0.9;
}

.score-details {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
}

.score-item {
  text-align: center;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 10px;
  border: 2px solid #e9ecef;
}

.score-value {
  display: block;
  font-size: 24px;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 5px;
}

.score-desc {
  font-size: 12px;
  color: #7f8c8d;
  text-transform: uppercase;
  font-weight: 500;
}

.performance-message {
  background: white;
  padding: 25px;
  border-radius: 15px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  margin-bottom: 30px;
  text-align: center;
}

.excellent {
  color: #27ae60;
}

.very-good {
  color: #2980b9;
}

.good {
  color: #16a085;
}

.average {
  color: #f39c12;
}

.needs-improvement {
  color: #e74c3c;
}

.performance-message h3 {
  margin-bottom: 10px;
  font-size: 24px;
}

.performance-message p {
  margin: 0;
  font-size: 16px;
  opacity: 0.8;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 30px;
}

.action-buttons .btn {
  padding: 12px 24px;
  font-size: 16px;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 500;
}

/* Admin Dashboard Styles */
.admin-dashboard {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.admin-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 15px;
}

.admin-header h1 {
  margin: 0;
  font-size: 28px;
}

.admin-nav {
  display: flex;
  gap: 10px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  padding: 25px;
  border-radius: 15px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  display: flex;
  align-items: center;
  gap: 15px;
}

.stat-icon {
  font-size: 40px;
  background: #f8f9fa;
  padding: 15px;
  border-radius: 50%;
  width: 70px;
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-content h3 {
  margin: 0;
  font-size: 32px;
  color: #2c3e50;
}

.stat-content p {
  margin: 5px 0 0 0;
  color: #7f8c8d;
  font-weight: 500;
}

.admin-section {
  background: white;
  padding: 25px;
  border-radius: 15px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  margin-bottom: 30px;
}

.admin-section h2 {
  margin-bottom: 20px;
  color: #2c3e50;
}

.exams-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.admin-exam-card {
  border: 2px solid #e9ecef;
  border-radius: 10px;
  padding: 20px;
  background: #f8f9fa;
}

.admin-exam-card h3 {
  margin-bottom: 10px;
  color: #2c3e50;
}

.exam-actions {
  display: flex;
  gap: 10px;
  margin-top: 15px;
}

.btn-sm {
  padding: 6px 12px;
  font-size: 14px;
}

.attempts-table {
  overflow-x: auto;
}

.attempts-table table {
  width: 100%;
  border-collapse: collapse;
}

.attempts-table th,
.attempts-table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #dee2e6;
}

.attempts-table th {
  background: #f8f9fa;
  font-weight: 600;
  color: #495057;
}

.percentage.good {
  color: #28a745;
  font-weight: bold;
}

.percentage.average {
  color: #ffc107;
  font-weight: bold;
}

.percentage.poor {
  color: #dc3545;
  font-weight: bold;
}

.status.passed {
  background: #d4edda;
  color: #155724;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.status.failed {
  background: #f8d7da;
  color: #721c24;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.quick-actions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.action-card {
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  border-radius: 10px;
  padding: 20px;
  text-decoration: none;
  color: inherit;
  transition: all 0.3s ease;
  text-align: center;
}

.action-card:hover {
  border-color: #007bff;
  background: #f0f8ff;
  transform: translateY(-2px);
}

.action-icon {
  font-size: 40px;
  margin-bottom: 10px;
}

.action-card h3 {
  margin-bottom: 10px;
  color: #2c3e50;
}

.action-card p {
  margin: 0;
  color: #7f8c8d;
}

/* Admin Login Styles */
.admin-login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.admin-login-card {
  background: white;
  padding: 40px;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0,0,0,0.2);
  width: 100%;
  max-width: 400px;
}

.admin-login-header {
  text-align: center;
  margin-bottom: 30px;
}

.admin-login-header h2 {
  color: #2c3e50;
  margin-bottom: 10px;
}

.admin-login-header p {
  color: #7f8c8d;
  margin: 0;
}

.admin-login-form {
  margin-bottom: 30px;
}

.btn-admin {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  width: 100%;
  padding: 12px;
  font-size: 16px;
  font-weight: 600;
}

.btn-admin:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

.admin-demo-info {
  background: #e7f3ff;
  border: 1px solid #b3d9ff;
  border-radius: 10px;
  padding: 20px;
  margin-bottom: 20px;
}

.admin-demo-info h4 {
  color: #0056b3;
  margin-bottom: 10px;
}

.demo-credentials p {
  margin: 5px 0;
  font-family: monospace;
  background: #f8f9fa;
  padding: 5px 10px;
  border-radius: 4px;
}

.login-links {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.link {
  color: #007bff;
  text-decoration: none;
  font-weight: 500;
}

.link:hover {
  text-decoration: underline;
}

/* Responsive Design */
@media (max-width: 768px) {
  .score-card {
    flex-direction: column;
    gap: 20px;
  }

  .score-details {
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
  }

  .action-buttons {
    flex-direction: column;
    align-items: center;
  }

  .admin-header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }
}

/* Student Dashboard Styles */
.student-dashboard {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 25px;
  background: linear-gradient(135deg, #3498db 0%, #2ecc71 100%);
  color: white;
  border-radius: 15px;
}

.student-info h1 {
  margin: 0 0 5px 0;
  font-size: 28px;
}

.student-details {
  margin: 0;
  opacity: 0.9;
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.btn-outline {
  background: transparent;
  border: 2px solid white;
  color: white;
}

.btn-outline:hover {
  background: white;
  color: #3498db;
}

.student-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.dashboard-section {
  background: white;
  padding: 25px;
  border-radius: 15px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  margin-bottom: 30px;
}

.dashboard-section h2 {
  margin-bottom: 20px;
  color: #2c3e50;
}

.student-exam-card {
  border: 2px solid #e9ecef;
  border-radius: 15px;
  padding: 20px;
  background: #f8f9fa;
  transition: all 0.3s ease;
}

.student-exam-card:hover {
  border-color: #3498db;
  background: #f0f8ff;
  transform: translateY(-2px);
}

.exam-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.exam-header h3 {
  margin: 0;
  color: #2c3e50;
}

.exam-subject {
  background: #3498db;
  color: white;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
}

.exam-details {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
  flex-wrap: wrap;
}

.exam-detail {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 14px;
  color: #7f8c8d;
}

.detail-icon {
  font-size: 16px;
}

.exam-description {
  color: #7f8c8d;
  margin-bottom: 15px;
  line-height: 1.5;
}

.exam-actions {
  display: flex;
  gap: 10px;
}

.btn-primary {
  background: #3498db;
  color: white;
}

.btn-primary:hover {
  background: #2980b9;
}

.attempts-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.attempt-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  border: 2px solid #e9ecef;
  border-radius: 10px;
  background: #f8f9fa;
}

.attempt-info h4 {
  margin: 0 0 5px 0;
  color: #2c3e50;
}

.attempt-date {
  margin: 0;
  color: #7f8c8d;
  font-size: 14px;
}

.attempt-score {
  display: flex;
  align-items: center;
  gap: 15px;
}

.score-circle {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3498db, #2ecc71);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}

.score-percentage {
  font-size: 14px;
}

.score-details p {
  margin: 2px 0;
  font-size: 14px;
  color: #7f8c8d;
}

.status-badge.passed {
  background: #d4edda;
  color: #155724;
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: bold;
}

.status-badge.failed {
  background: #f8d7da;
  color: #721c24;
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: bold;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .dashboard-header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .student-stats {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }

  .attempt-card {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .exam-details {
    justify-content: center;
  }

  .exam-actions {
    justify-content: center;
  }
}

/* Enhanced Home Page Styles */
.home-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.hero-section {
  text-align: center;
  padding: 60px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 20px;
  margin-bottom: 40px;
}

.hero-section h1 {
  font-size: 48px;
  margin-bottom: 15px;
  font-weight: 700;
}

.hero-subtitle {
  font-size: 20px;
  opacity: 0.9;
  margin: 0;
}

.demo-section {
  margin-bottom: 40px;
}

.demo-section h2 {
  text-align: center;
  color: #2c3e50;
  margin-bottom: 30px;
  font-size: 32px;
}

.app-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 30px;
  margin-bottom: 40px;
}

.app-card {
  background: white;
  border-radius: 20px;
  padding: 30px;
  box-shadow: 0 8px 25px rgba(0,0,0,0.1);
  text-align: center;
  border: 3px solid transparent;
  transition: all 0.3s ease;
}

.app-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 35px rgba(0,0,0,0.15);
}

.student-card {
  border-color: #3498db;
}

.student-card:hover {
  border-color: #2980b9;
}

.admin-card {
  border-color: #9b59b6;
}

.admin-card:hover {
  border-color: #8e44ad;
}

.app-icon {
  font-size: 60px;
  margin-bottom: 20px;
}

.app-card h3 {
  color: #2c3e50;
  margin-bottom: 15px;
  font-size: 24px;
}

.app-card p {
  color: #7f8c8d;
  margin-bottom: 25px;
  line-height: 1.6;
}

.demo-credentials {
  background: #f8f9fa;
  border-radius: 10px;
  padding: 20px;
  margin-bottom: 25px;
  border-left: 4px solid #3498db;
}

.demo-credentials h4 {
  color: #2c3e50;
  margin-bottom: 10px;
  font-size: 16px;
}

.demo-credentials p {
  margin: 5px 0;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  color: #2c3e50;
}

.app-actions {
  display: flex;
  gap: 10px;
  justify-content: center;
  flex-wrap: wrap;
}

.btn-admin {
  background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
  color: white;
  border: none;
}

.btn-admin:hover {
  background: linear-gradient(135deg, #8e44ad 0%, #7d3c98 100%);
}

.features-section {
  margin-bottom: 40px;
}

.features-section h2 {
  text-align: center;
  color: #2c3e50;
  margin-bottom: 30px;
  font-size: 32px;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 25px;
}

.feature-card {
  background: white;
  padding: 25px;
  border-radius: 15px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  text-align: center;
  border: 2px solid #e9ecef;
  transition: all 0.3s ease;
}

.feature-card:hover {
  border-color: #3498db;
  transform: translateY(-3px);
}

.feature-icon {
  font-size: 40px;
  margin-bottom: 15px;
}

.feature-card h4 {
  color: #2c3e50;
  margin-bottom: 10px;
}

.feature-card p {
  color: #7f8c8d;
  margin: 0;
  line-height: 1.5;
}

.sample-data-section {
  background: #f8f9fa;
  padding: 30px;
  border-radius: 15px;
  margin-bottom: 40px;
}

.sample-data-section h2 {
  text-align: center;
  color: #2c3e50;
  margin-bottom: 25px;
}

.sample-exams {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.sample-exam {
  background: white;
  padding: 20px;
  border-radius: 10px;
  border-left: 4px solid #3498db;
}

.sample-exam h4 {
  color: #2c3e50;
  margin-bottom: 15px;
}

.sample-exam ul {
  margin: 0;
  padding-left: 20px;
}

.sample-exam li {
  color: #7f8c8d;
  margin-bottom: 5px;
}

.logout-section {
  text-align: center;
  padding: 20px;
}

/* Mobile Responsive for Home */
@media (max-width: 768px) {
  .hero-section h1 {
    font-size: 36px;
  }

  .hero-subtitle {
    font-size: 18px;
  }

  .app-cards {
    grid-template-columns: 1fr;
  }

  .features-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }

  .sample-exams {
    grid-template-columns: 1fr;
  }

  .app-actions {
    flex-direction: column;
    align-items: center;
  }
}

/* Create Exam Styles */
.create-exam-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.create-exam-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 15px;
}

.create-exam-header h1 {
  margin: 0;
  font-size: 28px;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.create-exam-tabs {
  display: flex;
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  margin-bottom: 20px;
  overflow: hidden;
}

.tab {
  flex: 1;
  padding: 15px 20px;
  border: none;
  background: #f8f9fa;
  color: #6c757d;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.tab:hover {
  background: #e9ecef;
  color: #495057;
}

.tab.active {
  background: #007bff;
  color: white;
}

.tab-content {
  background: white;
  border-radius: 15px;
  padding: 30px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  min-height: 500px;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #2c3e50;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 12px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #007bff;
}

.question-builder {
  background: #f8f9fa;
  border-radius: 10px;
  padding: 25px;
  margin-bottom: 30px;
}

.question-form {
  background: white;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.question-meta {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}

.options-section {
  margin-bottom: 20px;
}

.options-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.options-header label {
  font-weight: 600;
  color: #2c3e50;
}

.options-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.option-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 2px solid #e9ecef;
}

.option-item input[type="radio"],
.option-item input[type="checkbox"] {
  width: auto;
  margin: 0;
}

.option-item input[type="text"] {
  flex: 1;
  margin: 0;
  border: 1px solid #ced4da;
}

.btn-sm {
  padding: 6px 12px;
  font-size: 12px;
  border-radius: 4px;
}

.btn-danger {
  background: #dc3545;
  color: white;
  border: none;
}

.btn-danger:hover {
  background: #c82333;
}

.questions-list {
  margin-top: 30px;
}

.questions-list h3 {
  color: #2c3e50;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #e9ecef;
}

.question-item {
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 10px;
  padding: 20px;
  margin-bottom: 15px;
  transition: all 0.3s ease;
}

.question-item:hover {
  border-color: #007bff;
  box-shadow: 0 4px 15px rgba(0,123,255,0.1);
}

.question-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
  flex-wrap: wrap;
}

.question-number {
  background: #007bff;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: bold;
  font-size: 12px;
}

.difficulty-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: bold;
  text-transform: uppercase;
}

.difficulty-badge.easy {
  background: #d4edda;
  color: #155724;
}

.difficulty-badge.medium {
  background: #fff3cd;
  color: #856404;
}

.difficulty-badge.hard {
  background: #f8d7da;
  color: #721c24;
}

.question-type {
  background: #e9ecef;
  color: #495057;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: bold;
}

.question-points {
  background: #d1ecf1;
  color: #0c5460;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: bold;
}

.question-text {
  color: #2c3e50;
  font-weight: 500;
  margin-bottom: 10px;
  line-height: 1.5;
}

.question-options {
  display: flex;
  flex-direction: column;
  gap: 5px;
  margin-bottom: 15px;
}

.question-options .option {
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
  font-size: 14px;
  border-left: 3px solid #e9ecef;
}

.question-options .option.correct {
  background: #d4edda;
  border-left-color: #28a745;
  font-weight: 500;
}

.question-actions {
  display: flex;
  gap: 10px;
}

.settings-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
}

.setting-group {
  background: #f8f9fa;
  border-radius: 10px;
  padding: 20px;
  border-left: 4px solid #007bff;
}

.setting-group h3 {
  color: #2c3e50;
  margin-bottom: 15px;
  font-size: 18px;
}

.exam-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.summary-card {
  background: #f8f9fa;
  border-radius: 10px;
  padding: 20px;
  border-left: 4px solid #28a745;
}

.summary-card h3 {
  color: #2c3e50;
  margin-bottom: 15px;
}

.summary-item {
  margin-bottom: 10px;
  padding: 8px 0;
  border-bottom: 1px solid #e9ecef;
}

.summary-item:last-child {
  border-bottom: none;
}

.question-stats {
  display: flex;
  justify-content: space-around;
  margin-bottom: 20px;
}

.stat {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 24px;
  font-weight: bold;
  color: #007bff;
}

.stat-label {
  font-size: 12px;
  color: #6c757d;
  text-transform: uppercase;
}

.question-types {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.type-stat {
  padding: 5px 10px;
  background: #e9ecef;
  border-radius: 4px;
  font-size: 14px;
}

.create-actions {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 30px;
}

.btn-large {
  padding: 15px 30px;
  font-size: 16px;
  font-weight: 600;
}

.btn-success {
  background: #28a745;
  color: white;
  border: none;
}

.btn-success:hover {
  background: #218838;
}

.btn-success:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

/* Mobile Responsive for Create Exam */
@media (max-width: 768px) {
  .create-exam-header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .create-exam-tabs {
    flex-direction: column;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }

  .question-meta {
    grid-template-columns: 1fr;
  }

  .settings-grid {
    grid-template-columns: 1fr;
  }

  .exam-summary {
    grid-template-columns: 1fr;
  }

  .create-actions {
    flex-direction: column;
    align-items: center;
  }

  .question-header {
    justify-content: center;
  }
}

/* Exam Preview Styles */
.exam-preview-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
  background: #f8f9fa;
  min-height: 100vh;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  border-radius: 15px;
}

.preview-header h1 {
  margin: 0;
  font-size: 28px;
}

.preview-actions {
  display: flex;
  gap: 10px;
}

.exam-info-card {
  background: white;
  border-radius: 15px;
  padding: 25px;
  margin-bottom: 20px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.exam-info-card h2 {
  color: #2c3e50;
  margin-bottom: 15px;
  font-size: 24px;
}

.exam-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 15px;
}

.meta-item {
  background: #e9ecef;
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 14px;
  color: #495057;
}

.exam-description {
  color: #6c757d;
  line-height: 1.6;
  margin-bottom: 15px;
}

.exam-instructions {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  padding: 15px;
  margin-top: 15px;
}

.exam-instructions h4 {
  color: #856404;
  margin-bottom: 10px;
}

.preview-navigation {
  background: white;
  border-radius: 15px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.preview-navigation h3 {
  color: #2c3e50;
  margin-bottom: 15px;
}

.question-nav {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.question-preview {
  background: white;
  border-radius: 15px;
  padding: 25px;
  margin-bottom: 20px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 2px solid #e9ecef;
}

.question-header h3 {
  color: #2c3e50;
  margin: 0;
}

.question-badges {
  display: flex;
  gap: 8px;
}

.type-badge {
  background: #6f42c1;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: bold;
}

.points-badge {
  background: #17a2b8;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: bold;
}

.question-content {
  margin-bottom: 20px;
}

.question-text {
  font-size: 18px;
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 20px;
  line-height: 1.6;
}

.mcq-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.preview-option {
  display: flex;
  align-items: center;
  padding: 15px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  background: #fafafa;
  transition: all 0.3s ease;
}

.preview-option.correct {
  border-color: #28a745;
  background: #f8fff9;
  box-shadow: 0 2px 8px rgba(40, 167, 69, 0.2);
}

.option-selector {
  display: flex;
  align-items: center;
  margin-right: 15px;
}

.option-selector input {
  margin-right: 8px;
}

.option-letter {
  background: #6c757d;
  color: white;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 12px;
}

.preview-option.correct .option-letter {
  background: #28a745;
}

.option-text {
  flex: 1;
  font-size: 16px;
}

.correct-indicator {
  color: #28a745;
  font-weight: bold;
  font-size: 18px;
}

.answer-input,
.fill-blank {
  margin-bottom: 15px;
}

.answer-input input {
  width: 100%;
  padding: 12px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 16px;
  margin-bottom: 10px;
}

.correct-answer {
  background: #d4edda;
  border: 1px solid #c3e6cb;
  border-radius: 6px;
  padding: 10px;
  color: #155724;
  font-weight: 500;
}

.question-explanation {
  background: #e7f3ff;
  border: 1px solid #b3d9ff;
  border-radius: 8px;
  padding: 15px;
  margin-top: 15px;
}

.question-explanation h4 {
  color: #0056b3;
  margin-bottom: 10px;
}

.preview-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20px;
  border-top: 2px solid #e9ecef;
}

.question-counter {
  font-weight: 600;
  color: #6c757d;
}

.exam-summary {
  background: white;
  border-radius: 15px;
  padding: 25px;
  margin-bottom: 20px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.exam-summary h3 {
  color: #2c3e50;
  margin-bottom: 20px;
}

.summary-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.stat-group {
  background: #f8f9fa;
  border-radius: 10px;
  padding: 20px;
  border-left: 4px solid #007bff;
}

.stat-group h4 {
  color: #2c3e50;
  margin-bottom: 15px;
  font-size: 16px;
}

.type-breakdown,
.difficulty-breakdown,
.scoring-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.type-stat,
.difficulty-stat,
.score-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.type-count,
.difficulty-count {
  background: #007bff;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
}

.difficulty-name.easy {
  color: #28a745;
  font-weight: bold;
}

.difficulty-name.medium {
  color: #ffc107;
  font-weight: bold;
}

.difficulty-name.hard {
  color: #dc3545;
  font-weight: bold;
}

.preview-footer {
  background: white;
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.footer-note {
  background: #e7f3ff;
  border: 1px solid #b3d9ff;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
}

.footer-note p {
  margin: 5px 0;
  color: #0056b3;
}

.footer-actions {
  display: flex;
  justify-content: center;
  gap: 15px;
}

/* Mobile Responsive for Preview */
@media (max-width: 768px) {
  .preview-header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .exam-meta {
    justify-content: center;
  }

  .question-header {
    flex-direction: column;
    gap: 10px;
    text-align: center;
  }

  .preview-controls {
    flex-direction: column;
    gap: 15px;
  }

  .summary-stats {
    grid-template-columns: 1fr;
  }

  .footer-actions {
    flex-direction: column;
    align-items: center;
  }
}

/* Enhanced Exam Taking Styles */
.exam-container {
  max-width: 900px;
  margin: 0 auto;
  padding: 20px;
  background: #f8f9fa;
  min-height: 100vh;
}

.exam-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  margin-bottom: 20px;
}

.exam-info h2 {
  margin: 0 0 10px 0;
  color: #2c3e50;
}

.exam-meta {
  display: flex;
  gap: 15px;
  align-items: center;
}

.subject-badge {
  background: #3498db;
  color: white;
  padding: 4px 12px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: bold;
}

.question-counter {
  color: #7f8c8d;
  font-weight: 500;
}

.timer-container {
  text-align: right;
}

.timer {
  background: #e74c3c;
  color: white;
  padding: 10px 20px;
  border-radius: 25px;
  font-weight: bold;
  font-size: 18px;
  box-shadow: 0 2px 5px rgba(231, 76, 60, 0.3);
}

.progress-bar {
  background: #ecf0f1;
  height: 8px;
  border-radius: 4px;
  margin-bottom: 20px;
  position: relative;
  overflow: hidden;
}

.progress-fill {
  background: linear-gradient(90deg, #3498db, #2ecc71);
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-text {
  position: absolute;
  top: -25px;
  right: 0;
  font-size: 12px;
  color: #7f8c8d;
  font-weight: 500;
}

.question-nav {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin-bottom: 30px;
  flex-wrap: wrap;
}

.nav-dot {
  width: 35px;
  height: 35px;
  border-radius: 50%;
  border: 2px solid #bdc3c7;
  background: white;
  color: #7f8c8d;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
}

.nav-dot:hover {
  border-color: #3498db;
  color: #3498db;
}

.nav-dot.active {
  background: #3498db;
  color: white;
  border-color: #3498db;
}

.nav-dot.answered {
  background: #2ecc71;
  color: white;
  border-color: #2ecc71;
}

.nav-dot.answered.active {
  background: #27ae60;
  border-color: #27ae60;
}

.question-section {
  background: white;
  border-radius: 10px;
  padding: 30px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  margin-bottom: 20px;
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.question-type-badge {
  background: #9b59b6;
  color: white;
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: bold;
}

.instruction {
  color: #e67e22;
  font-style: italic;
  font-size: 14px;
}

.question-content h3 {
  color: #2c3e50;
  font-size: 20px;
  line-height: 1.5;
  margin-bottom: 25px;
}

.options-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.option-card {
  display: flex;
  align-items: center;
  padding: 15px;
  border: 2px solid #ecf0f1;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #fafafa;
}

.option-card:hover {
  border-color: #3498db;
  background: #f0f8ff;
}

.option-card.selected {
  border-color: #2ecc71;
  background: #f0fff4;
  box-shadow: 0 2px 5px rgba(46, 204, 113, 0.2);
}

.option-selector {
  display: flex;
  align-items: center;
  margin-right: 15px;
}

.option-selector input {
  margin-right: 8px;
  transform: scale(1.2);
}

.option-letter {
  background: #34495e;
  color: white;
  width: 25px;
  height: 25px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 12px;
}

.option-card.selected .option-letter {
  background: #2ecc71;
}

.option-text {
  flex: 1;
  font-size: 16px;
  line-height: 1.4;
}

.exam-navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.nav-left, .nav-right {
  flex: 1;
}

.nav-right {
  text-align: right;
}

.nav-center {
  flex: 1;
  text-align: center;
}

.answer-status .answered {
  color: #2ecc71;
  font-weight: bold;
}

.answer-status .unanswered {
  color: #e74c3c;
  font-weight: bold;
}

.btn-primary {
  background: #3498db;
}

.btn-primary:hover {
  background: #2980b9;
}

.btn-success {
  background: #2ecc71;
  font-size: 16px;
  padding: 12px 24px;
}

.btn-success:hover {
  background: #27ae60;
}

.exam-summary {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 10px;
  padding: 20px;
  margin-top: 20px;
}

.exam-summary h4 {
  color: #856404;
  margin-bottom: 15px;
}

.summary-stats {
  display: flex;
  justify-content: space-around;
  margin-bottom: 15px;
}

.stat {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 24px;
  font-weight: bold;
  color: #2c3e50;
}

.stat-label {
  font-size: 12px;
  color: #7f8c8d;
  text-transform: uppercase;
}

.summary-note {
  color: #856404;
  font-style: italic;
  margin: 0;
  text-align: center;
}
