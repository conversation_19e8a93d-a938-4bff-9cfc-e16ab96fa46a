.App {
  text-align: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.btn {
  background-color: #007bff;
  color: white;
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  text-decoration: none;
  display: inline-block;
  margin: 5px;
}

.btn:hover {
  background-color: #0056b3;
}

.btn-secondary {
  background-color: #6c757d;
}

.btn-secondary:hover {
  background-color: #545b62;
}

.form-group {
  margin-bottom: 15px;
  text-align: left;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

.form-group input {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-sizing: border-box;
}

.exam-card {
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 20px;
  margin: 10px 0;
  text-align: left;
}

.exam-card h3 {
  margin-top: 0;
  color: #333;
}

.question-container {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin: 20px 0;
  text-align: left;
}

.question-text {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 15px;
}

.option {
  margin: 10px 0;
}

.option input {
  margin-right: 10px;
}

.timer {
  position: fixed;
  top: 20px;
  right: 20px;
  background: #dc3545;
  color: white;
  padding: 10px 15px;
  border-radius: 4px;
  font-weight: bold;
}

.results-container {
  text-align: center;
  padding: 20px;
}

.score-summary {
  background: #e9ecef;
  padding: 20px;
  border-radius: 8px;
  margin: 20px 0;
}

.score-summary h2 {
  color: #28a745;
  margin: 0 0 10px 0;
}

.error {
  color: #dc3545;
  background: #f8d7da;
  padding: 10px;
  border-radius: 4px;
  margin: 10px 0;
}

.success {
  color: #155724;
  background: #d4edda;
  padding: 10px;
  border-radius: 4px;
  margin: 10px 0;
}

.loading {
  text-align: center;
  padding: 40px;
  font-size: 18px;
}

/* Enhanced Exam Taking Styles */
.exam-container {
  max-width: 900px;
  margin: 0 auto;
  padding: 20px;
  background: #f8f9fa;
  min-height: 100vh;
}

.exam-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  margin-bottom: 20px;
}

.exam-info h2 {
  margin: 0 0 10px 0;
  color: #2c3e50;
}

.exam-meta {
  display: flex;
  gap: 15px;
  align-items: center;
}

.subject-badge {
  background: #3498db;
  color: white;
  padding: 4px 12px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: bold;
}

.question-counter {
  color: #7f8c8d;
  font-weight: 500;
}

.timer-container {
  text-align: right;
}

.timer {
  background: #e74c3c;
  color: white;
  padding: 10px 20px;
  border-radius: 25px;
  font-weight: bold;
  font-size: 18px;
  box-shadow: 0 2px 5px rgba(231, 76, 60, 0.3);
}

.progress-bar {
  background: #ecf0f1;
  height: 8px;
  border-radius: 4px;
  margin-bottom: 20px;
  position: relative;
  overflow: hidden;
}

.progress-fill {
  background: linear-gradient(90deg, #3498db, #2ecc71);
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-text {
  position: absolute;
  top: -25px;
  right: 0;
  font-size: 12px;
  color: #7f8c8d;
  font-weight: 500;
}

.question-nav {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin-bottom: 30px;
  flex-wrap: wrap;
}

.nav-dot {
  width: 35px;
  height: 35px;
  border-radius: 50%;
  border: 2px solid #bdc3c7;
  background: white;
  color: #7f8c8d;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
}

.nav-dot:hover {
  border-color: #3498db;
  color: #3498db;
}

.nav-dot.active {
  background: #3498db;
  color: white;
  border-color: #3498db;
}

.nav-dot.answered {
  background: #2ecc71;
  color: white;
  border-color: #2ecc71;
}

.nav-dot.answered.active {
  background: #27ae60;
  border-color: #27ae60;
}

.question-section {
  background: white;
  border-radius: 10px;
  padding: 30px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  margin-bottom: 20px;
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.question-type-badge {
  background: #9b59b6;
  color: white;
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: bold;
}

.instruction {
  color: #e67e22;
  font-style: italic;
  font-size: 14px;
}

.question-content h3 {
  color: #2c3e50;
  font-size: 20px;
  line-height: 1.5;
  margin-bottom: 25px;
}

.options-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.option-card {
  display: flex;
  align-items: center;
  padding: 15px;
  border: 2px solid #ecf0f1;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #fafafa;
}

.option-card:hover {
  border-color: #3498db;
  background: #f0f8ff;
}

.option-card.selected {
  border-color: #2ecc71;
  background: #f0fff4;
  box-shadow: 0 2px 5px rgba(46, 204, 113, 0.2);
}

.option-selector {
  display: flex;
  align-items: center;
  margin-right: 15px;
}

.option-selector input {
  margin-right: 8px;
  transform: scale(1.2);
}

.option-letter {
  background: #34495e;
  color: white;
  width: 25px;
  height: 25px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 12px;
}

.option-card.selected .option-letter {
  background: #2ecc71;
}

.option-text {
  flex: 1;
  font-size: 16px;
  line-height: 1.4;
}

.exam-navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.nav-left, .nav-right {
  flex: 1;
}

.nav-right {
  text-align: right;
}

.nav-center {
  flex: 1;
  text-align: center;
}

.answer-status .answered {
  color: #2ecc71;
  font-weight: bold;
}

.answer-status .unanswered {
  color: #e74c3c;
  font-weight: bold;
}

.btn-primary {
  background: #3498db;
}

.btn-primary:hover {
  background: #2980b9;
}

.btn-success {
  background: #2ecc71;
  font-size: 16px;
  padding: 12px 24px;
}

.btn-success:hover {
  background: #27ae60;
}

.exam-summary {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 10px;
  padding: 20px;
  margin-top: 20px;
}

.exam-summary h4 {
  color: #856404;
  margin-bottom: 15px;
}

.summary-stats {
  display: flex;
  justify-content: space-around;
  margin-bottom: 15px;
}

.stat {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 24px;
  font-weight: bold;
  color: #2c3e50;
}

.stat-label {
  font-size: 12px;
  color: #7f8c8d;
  text-transform: uppercase;
}

.summary-note {
  color: #856404;
  font-style: italic;
  margin: 0;
  text-align: center;
}
