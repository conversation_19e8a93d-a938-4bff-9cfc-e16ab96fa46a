import React, { useEffect, useState } from 'react';
import { usePara<PERSON>, Link } from 'react-router-dom';
import api from '../services/api';

const ExamResults = () => {
  const { attemptId } = useParams();
  const [results, setResults] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    const fetchResults = async () => {
      try {
        console.log('Fetching results for attempt:', attemptId);
        const response = await api.get(`/exam-attempts/${attemptId}/results`);
        console.log('Results response:', response.data);
        setResults(response.data);
      } catch (error) {
        console.error('Failed to fetch results:', error);
        setError(error.response?.data?.message || 'Failed to fetch results');
      } finally {
        setLoading(false);
      }
    };

    if (attemptId) {
      fetchResults();
    }
  }, [attemptId]);

  if (loading) return <div className="loading">Loading results...</div>;
  if (error) return <div className="error">{error}</div>;
  if (!results) return <div className="error">No results found</div>;

  return (
    <div className="container">
      <h1>Exam Results</h1>

      <div className="score-summary">
        <h2>Score: {results.score}/{results.totalQuestions}</h2>
        <p><strong>Percentage:</strong> {results.percentage}%</p>
        <p><strong>Time Taken:</strong> {results.timeTaken} minutes</p>
        <p><strong>Exam:</strong> {results.examTitle}</p>

        {results.percentage >= 70 ? (
          <div className="success">🎉 Congratulations! You passed!</div>
        ) : results.percentage >= 50 ? (
          <div style={{color: '#ff9800', padding: '10px', background: '#fff3cd', borderRadius: '4px'}}>
            ⚠️ Average performance. You can do better!
          </div>
        ) : (
          <div className="error">❌ You need more practice. Keep studying!</div>
        )}
      </div>

      <div style={{marginTop: '20px'}}>
        <Link to="/exams" className="btn">Take Another Exam</Link>
        <Link to="/" className="btn btn-secondary">Home</Link>
      </div>
    </div>
  );
};

export default ExamResults;