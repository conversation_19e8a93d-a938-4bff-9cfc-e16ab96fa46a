import React, { useEffect, useState } from 'react';
import { use<PERSON>ara<PERSON>, Link } from 'react-router-dom';
import api from '../services/api';

const ExamResults = () => {
  const { attemptId } = useParams();
  const [results, setResults] = useState(null);
  const [detailedResults, setDetailedResults] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [showDetailedAnalysis, setShowDetailedAnalysis] = useState(false);

  useEffect(() => {
    const fetchResults = async () => {
      try {
        console.log('Fetching results for attempt:', attemptId);
        const response = await api.get(`/exam-attempts/${attemptId}/results`);
        console.log('Results response:', response.data);
        setResults(response.data);

        // Mock detailed results for demonstration
        setDetailedResults({
          questionAnalysis: [
            {
              questionId: 'q1',
              questionText: 'What is 2 + 2?',
              userAnswer: '4',
              correctAnswer: '4',
              isCorrect: true,
              points: 1,
              maxPoints: 1,
              difficulty: 'EASY',
              explanation: 'Basic addition: 2 + 2 equals 4.'
            },
            {
              questionId: 'q2',
              questionText: 'What is 10 × 5?',
              userAnswer: '50',
              correctAnswer: '50',
              isCorrect: true,
              points: 1,
              maxPoints: 1,
              difficulty: 'EASY',
              explanation: 'Basic multiplication: 10 × 5 equals 50.'
            },
            {
              questionId: 'q3',
              questionText: 'Is 17 a prime number?',
              userAnswer: 'True',
              correctAnswer: 'True',
              isCorrect: true,
              points: 1,
              maxPoints: 1,
              difficulty: 'MEDIUM',
              explanation: '17 is prime because it has no divisors other than 1 and itself.'
            }
          ],
          timeAnalysis: {
            totalTime: results?.timeTaken || 0,
            averageTimePerQuestion: (results?.timeTaken || 0) / (results?.totalQuestions || 1),
            timeDistribution: [
              { questionId: 'q1', timeSpent: 2 },
              { questionId: 'q2', timeSpent: 3 },
              { questionId: 'q3', timeSpent: 5 }
            ]
          },
          performanceMetrics: {
            accuracy: results?.percentage || 0,
            speed: 'Average',
            difficulty: 'Easy',
            recommendation: results?.percentage >= 90 ? 'Excellent! Try harder topics.' :
                          results?.percentage >= 70 ? 'Good job! Keep practicing.' :
                          'Need more practice. Review the topics.'
          }
        });
      } catch (error) {
        console.error('Failed to fetch results:', error);
        setError(error.response?.data?.message || 'Failed to fetch results');
      } finally {
        setLoading(false);
      }
    };

    if (attemptId) {
      fetchResults();
    }
  }, [attemptId]);

  if (loading) return <div className="loading">Loading results...</div>;
  if (error) return <div className="error">{error}</div>;
  if (!results) return <div className="error">No results found</div>;

  return (
    <div className="container">
      <div className="results-header">
        <h1>📊 Exam Results</h1>
        <p className="exam-title">{results.examTitle}</p>
      </div>

      {/* Score Card */}
      <div className="score-card">
        <div className="score-circle">
          <div className="score-number">{results.percentage}%</div>
          <div className="score-label">Score</div>
        </div>

        <div className="score-details">
          <div className="score-item">
            <span className="score-value">{results.score}</span>
            <span className="score-desc">Correct</span>
          </div>
          <div className="score-item">
            <span className="score-value">{results.totalQuestions - results.score}</span>
            <span className="score-desc">Incorrect</span>
          </div>
          <div className="score-item">
            <span className="score-value">{results.totalQuestions}</span>
            <span className="score-desc">Total</span>
          </div>
          <div className="score-item">
            <span className="score-value">{results.timeTaken}</span>
            <span className="score-desc">Minutes</span>
          </div>
        </div>
      </div>

      {/* Performance Message */}
      <div className="performance-message">
        {results.percentage >= 90 ? (
          <div className="excellent">
            <h3>🏆 EXCELLENT!</h3>
            <p>Outstanding performance! You have mastered this subject.</p>
          </div>
        ) : results.percentage >= 80 ? (
          <div className="very-good">
            <h3>🎉 VERY GOOD!</h3>
            <p>Great job! You have a strong understanding.</p>
          </div>
        ) : results.percentage >= 70 ? (
          <div className="good">
            <h3>✅ GOOD!</h3>
            <p>Well done! You passed with solid performance.</p>
          </div>
        ) : results.percentage >= 50 ? (
          <div className="average">
            <h3>⚠️ AVERAGE</h3>
            <p>You passed, but there's room for improvement.</p>
          </div>
        ) : (
          <div className="needs-improvement">
            <h3>❌ NEEDS IMPROVEMENT</h3>
            <p>Don't give up! Review the material and try again.</p>
          </div>
        )}
      </div>

      {/* Detailed Analysis Toggle */}
      <div className="analysis-toggle">
        <button
          onClick={() => setShowDetailedAnalysis(!showDetailedAnalysis)}
          className="btn btn-outline"
        >
          {showDetailedAnalysis ? '📊 Hide Detailed Analysis' : '📊 Show Detailed Analysis'}
        </button>
      </div>

      {/* Detailed Analysis */}
      {showDetailedAnalysis && detailedResults && (
        <div className="detailed-analysis">
          {/* Question-by-Question Analysis */}
          <div className="question-analysis-section">
            <h3>📝 Question-by-Question Analysis</h3>
            <div className="questions-breakdown">
              {detailedResults.questionAnalysis.map((question, index) => (
                <div key={question.questionId} className={`question-result ${question.isCorrect ? 'correct' : 'incorrect'}`}>
                  <div className="question-header">
                    <span className="question-number">Q{index + 1}</span>
                    <span className={`difficulty-badge ${question.difficulty.toLowerCase()}`}>
                      {question.difficulty}
                    </span>
                    <span className="points-earned">
                      {question.points}/{question.maxPoints} pts
                    </span>
                    <span className={`result-indicator ${question.isCorrect ? 'correct' : 'incorrect'}`}>
                      {question.isCorrect ? '✅' : '❌'}
                    </span>
                  </div>

                  <div className="question-content">
                    <p className="question-text">{question.questionText}</p>

                    <div className="answer-comparison">
                      <div className="user-answer">
                        <strong>Your Answer:</strong>
                        <span className={question.isCorrect ? 'correct-answer' : 'incorrect-answer'}>
                          {question.userAnswer}
                        </span>
                      </div>
                      {!question.isCorrect && (
                        <div className="correct-answer">
                          <strong>Correct Answer:</strong>
                          <span className="correct-answer">{question.correctAnswer}</span>
                        </div>
                      )}
                    </div>

                    {question.explanation && (
                      <div className="explanation">
                        <strong>Explanation:</strong> {question.explanation}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Performance Metrics */}
          <div className="performance-metrics">
            <h3>📈 Performance Metrics</h3>
            <div className="metrics-grid">
              <div className="metric-card">
                <div className="metric-icon">🎯</div>
                <div className="metric-content">
                  <h4>Accuracy</h4>
                  <p className="metric-value">{detailedResults.performanceMetrics.accuracy}%</p>
                </div>
              </div>

              <div className="metric-card">
                <div className="metric-icon">⚡</div>
                <div className="metric-content">
                  <h4>Speed</h4>
                  <p className="metric-value">{detailedResults.performanceMetrics.speed}</p>
                </div>
              </div>

              <div className="metric-card">
                <div className="metric-icon">📊</div>
                <div className="metric-content">
                  <h4>Difficulty Level</h4>
                  <p className="metric-value">{detailedResults.performanceMetrics.difficulty}</p>
                </div>
              </div>
            </div>

            <div className="recommendation">
              <h4>💡 Recommendation</h4>
              <p>{detailedResults.performanceMetrics.recommendation}</p>
            </div>
          </div>

          {/* Time Analysis */}
          <div className="time-analysis">
            <h3>⏱️ Time Analysis</h3>
            <div className="time-stats">
              <div className="time-stat">
                <span className="stat-label">Total Time:</span>
                <span className="stat-value">{detailedResults.timeAnalysis.totalTime} minutes</span>
              </div>
              <div className="time-stat">
                <span className="stat-label">Average per Question:</span>
                <span className="stat-value">{detailedResults.timeAnalysis.averageTimePerQuestion.toFixed(1)} minutes</span>
              </div>
            </div>

            <div className="time-distribution">
              <h4>Time per Question:</h4>
              {detailedResults.timeAnalysis.timeDistribution.map((item, index) => (
                <div key={item.questionId} className="time-item">
                  <span>Question {index + 1}:</span>
                  <span>{item.timeSpent} minutes</span>
                  <div className="time-bar">
                    <div
                      className="time-fill"
                      style={{ width: `${(item.timeSpent / Math.max(...detailedResults.timeAnalysis.timeDistribution.map(t => t.timeSpent))) * 100}%` }}
                    ></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="action-buttons">
        <Link to="/exams" className="btn">Take Another Exam</Link>
        <Link to="/student" className="btn btn-secondary">Dashboard</Link>
        <Link to="/" className="btn btn-outline">Back to Home</Link>
      </div>
    </div>
  );
};

export default ExamResults;