import React, { useEffect, useState } from 'react';
import { usePara<PERSON>, Link } from 'react-router-dom';
import api from '../services/api';

const ExamResults = () => {
  const { attemptId } = useParams();
  const [results, setResults] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    const fetchResults = async () => {
      try {
        console.log('Fetching results for attempt:', attemptId);
        const response = await api.get(`/exam-attempts/${attemptId}/results`);
        console.log('Results response:', response.data);
        setResults(response.data);
      } catch (error) {
        console.error('Failed to fetch results:', error);
        setError(error.response?.data?.message || 'Failed to fetch results');
      } finally {
        setLoading(false);
      }
    };

    if (attemptId) {
      fetchResults();
    }
  }, [attemptId]);

  if (loading) return <div className="loading">Loading results...</div>;
  if (error) return <div className="error">{error}</div>;
  if (!results) return <div className="error">No results found</div>;

  return (
    <div className="container">
      <div className="results-header">
        <h1>📊 Exam Results</h1>
        <p className="exam-title">{results.examTitle}</p>
      </div>

      {/* Score Card */}
      <div className="score-card">
        <div className="score-circle">
          <div className="score-number">{results.percentage}%</div>
          <div className="score-label">Score</div>
        </div>

        <div className="score-details">
          <div className="score-item">
            <span className="score-value">{results.score}</span>
            <span className="score-desc">Correct</span>
          </div>
          <div className="score-item">
            <span className="score-value">{results.totalQuestions - results.score}</span>
            <span className="score-desc">Incorrect</span>
          </div>
          <div className="score-item">
            <span className="score-value">{results.totalQuestions}</span>
            <span className="score-desc">Total</span>
          </div>
          <div className="score-item">
            <span className="score-value">{results.timeTaken}</span>
            <span className="score-desc">Minutes</span>
          </div>
        </div>
      </div>

      {/* Performance Message */}
      <div className="performance-message">
        {results.percentage >= 90 ? (
          <div className="excellent">
            <h3>🏆 EXCELLENT!</h3>
            <p>Outstanding performance! You have mastered this subject.</p>
          </div>
        ) : results.percentage >= 80 ? (
          <div className="very-good">
            <h3>🎉 VERY GOOD!</h3>
            <p>Great job! You have a strong understanding.</p>
          </div>
        ) : results.percentage >= 70 ? (
          <div className="good">
            <h3>✅ GOOD!</h3>
            <p>Well done! You passed with solid performance.</p>
          </div>
        ) : results.percentage >= 50 ? (
          <div className="average">
            <h3>⚠️ AVERAGE</h3>
            <p>You passed, but there's room for improvement.</p>
          </div>
        ) : (
          <div className="needs-improvement">
            <h3>❌ NEEDS IMPROVEMENT</h3>
            <p>Don't give up! Review the material and try again.</p>
          </div>
        )}
      </div>

      {/* Action Buttons */}
      <div className="action-buttons">
        <Link to="/exams" className="btn">Take Another Exam</Link>
        <Link to="/" className="btn btn-secondary">Back to Home</Link>
      </div>
    </div>
  );
};

export default ExamResults;