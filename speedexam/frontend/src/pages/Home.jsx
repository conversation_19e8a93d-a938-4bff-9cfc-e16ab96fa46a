import React from 'react';
import { Link } from 'react-router-dom';

const Home = () => {
  const token = localStorage.getItem('token');
  const adminToken = localStorage.getItem('adminToken');

  return (
    <div className="home-container">
      <div className="hero-section">
        <h1>🎓 Welcome to SpeedExam</h1>
        <p className="hero-subtitle">Your comprehensive platform for online exams and assessments</p>
      </div>

      <div className="demo-section">
        <h2>🚀 Demo Applications</h2>
        <div className="app-cards">
          {/* Student Application */}
          <div className="app-card student-card">
            <div className="app-icon">👨‍🎓</div>
            <h3>Student Application</h3>
            <p>Take exams, view results, and track your progress</p>

            <div className="demo-credentials">
              <h4>Demo Credentials:</h4>
              <p><strong>Email:</strong> <EMAIL></p>
              <p><strong>Password:</strong> password123</p>
            </div>

            <div className="app-actions">
              {token ? (
                <div>
                  <Link to="/student" className="btn btn-primary">Student Dashboard</Link>
                  <Link to="/exams" className="btn btn-secondary">Browse Exams</Link>
                </div>
              ) : (
                <Link to="/login" className="btn btn-primary">Student Login</Link>
              )}
            </div>
          </div>

          {/* Admin Application */}
          <div className="app-card admin-card">
            <div className="app-icon">👨‍💼</div>
            <h3>Admin Application</h3>
            <p>Manage exams, view analytics, and monitor student progress</p>

            <div className="demo-credentials">
              <h4>Demo Credentials:</h4>
              <p><strong>Email:</strong> <EMAIL></p>
              <p><strong>Password:</strong> admin123</p>
            </div>

            <div className="app-actions">
              {adminToken ? (
                <Link to="/admin" className="btn btn-admin">Admin Dashboard</Link>
              ) : (
                <Link to="/admin/login" className="btn btn-admin">Admin Login</Link>
              )}
            </div>
          </div>
        </div>
      </div>

      <div className="features-section">
        <h2>✨ Key Features</h2>
        <div className="features-grid">
          <div className="feature-card">
            <div className="feature-icon">📝</div>
            <h4>Interactive Exams</h4>
            <p>Multiple choice questions with real-time timer</p>
          </div>

          <div className="feature-card">
            <div className="feature-icon">📊</div>
            <h4>Instant Results</h4>
            <p>Automatic scoring with detailed performance analysis</p>
          </div>

          <div className="feature-card">
            <div className="feature-icon">👥</div>
            <h4>User Management</h4>
            <p>Separate interfaces for students and administrators</p>
          </div>

          <div className="feature-card">
            <div className="feature-icon">📱</div>
            <h4>Responsive Design</h4>
            <p>Works seamlessly on desktop, tablet, and mobile</p>
          </div>
        </div>
      </div>

      <div className="sample-data-section">
        <h2>📚 Sample Exam Data</h2>
        <div className="sample-exams">
          <div className="sample-exam">
            <h4>📐 Basic Mathematics Test</h4>
            <ul>
              <li>Duration: 30 minutes</li>
              <li>Questions: 3 (MCQ, True/False)</li>
              <li>Topics: Basic arithmetic, prime numbers</li>
            </ul>
          </div>

          <div className="sample-exam">
            <h4>🔬 General Science Quiz</h4>
            <ul>
              <li>Duration: 20 minutes</li>
              <li>Questions: 2 (MCQ, True/False)</li>
              <li>Topics: Chemistry, astronomy</li>
            </ul>
          </div>
        </div>
      </div>

      {token && (
        <div className="logout-section">
          <button
            className="btn btn-outline"
            onClick={() => {
              localStorage.removeItem('token');
              localStorage.removeItem('adminToken');
              localStorage.removeItem('userRole');
              window.location.reload();
            }}
          >
            Logout
          </button>
        </div>
      )}
    </div>
  );
};

export default Home;
