import React, { useState } from 'react';
import { useNavigate, Link } from 'react-router-dom';

const AdminLogin = () => {
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  });
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      // Mock admin login - in real app, this would call admin login API
      if (formData.email === '<EMAIL>' && formData.password === 'admin123') {
        localStorage.setItem('adminToken', 'mock_admin_token_' + Date.now());
        localStorage.setItem('userRole', 'admin');
        navigate('/admin');
      } else {
        setError('Invalid admin credentials');
      }
    } catch (error) {
      setError('Login failed');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="admin-login-container">
      <div className="admin-login-card">
        <div className="admin-login-header">
          <h2>🔐 Admin Login</h2>
          <p>SpeedExam Administration Panel</p>
        </div>
        
        {error && <div className="error">{error}</div>}
        
        <form onSubmit={handleSubmit} className="admin-login-form">
          <div className="form-group">
            <label>Admin Email:</label>
            <input
              type="email"
              name="email"
              value={formData.email}
              onChange={handleChange}
              placeholder="<EMAIL>"
              required
            />
          </div>
          
          <div className="form-group">
            <label>Password:</label>
            <input
              type="password"
              name="password"
              value={formData.password}
              onChange={handleChange}
              placeholder="Enter admin password"
              required
            />
          </div>
          
          <button type="submit" className="btn btn-admin" disabled={loading}>
            {loading ? 'Logging in...' : 'Login as Admin'}
          </button>
        </form>
        
        <div className="admin-demo-info">
          <h4>🎯 Demo Admin Credentials:</h4>
          <div className="demo-credentials">
            <p><strong>Email:</strong> <EMAIL></p>
            <p><strong>Password:</strong> admin123</p>
          </div>
        </div>
        
        <div className="login-links">
          <Link to="/login" className="link">Student Login</Link>
          <Link to="/" className="link">Back to Home</Link>
        </div>
      </div>
    </div>
  );
};

export default AdminLogin;
