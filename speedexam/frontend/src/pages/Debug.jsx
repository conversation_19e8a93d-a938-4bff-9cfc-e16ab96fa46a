import React, { useState, useEffect } from 'react';
import api from '../services/api';

const Debug = () => {
  const [apiStatus, setApiStatus] = useState('Testing...');
  const [exams, setExams] = useState([]);
  const [loginStatus, setLoginStatus] = useState('Not tested');
  const [token, setToken] = useState(localStorage.getItem('token'));

  useEffect(() => {
    testAPI();
  }, []);

  const testAPI = async () => {
    try {
      // Test basic API connection
      const response = await fetch('http://localhost:5000/api/test');
      const data = await response.json();
      setApiStatus(`✅ API Connected: ${data.message}`);
      
      // Test exams endpoint
      const examsResponse = await fetch('http://localhost:5000/api/exams');
      const examsData = await examsResponse.json();
      setExams(examsData);
      
    } catch (error) {
      setApiStatus(`❌ API Error: ${error.message}`);
    }
  };

  const testLogin = async () => {
    try {
      const response = await api.post('/auth/login', {
        email: '<EMAIL>',
        password: 'password123'
      });
      
      localStorage.setItem('token', response.data.token);
      setToken(response.data.token);
      setLoginStatus(`✅ Login successful: ${response.data.user.name}`);
    } catch (error) {
      setLoginStatus(`❌ Login failed: ${error.message}`);
    }
  };

  const testStartExam = async (examId) => {
    try {
      const response = await api.post(`/exams/${examId}/start`);
      alert(`✅ Exam started successfully! Attempt ID: ${response.data.attempt}`);
    } catch (error) {
      alert(`❌ Failed to start exam: ${error.response?.data?.message || error.message}`);
    }
  };

  return (
    <div className="container">
      <h1>Debug Page</h1>
      
      <div style={{marginBottom: '20px'}}>
        <h3>API Status</h3>
        <p>{apiStatus}</p>
        <button onClick={testAPI} className="btn">Test API</button>
      </div>

      <div style={{marginBottom: '20px'}}>
        <h3>Authentication</h3>
        <p>Status: {loginStatus}</p>
        <p>Token: {token ? `${token.substring(0, 20)}...` : 'None'}</p>
        <button onClick={testLogin} className="btn">Test Login</button>
      </div>

      <div style={{marginBottom: '20px'}}>
        <h3>Available Exams ({exams.length})</h3>
        {exams.map(exam => (
          <div key={exam._id} style={{border: '1px solid #ccc', padding: '10px', margin: '10px 0'}}>
            <h4>{exam.title}</h4>
            <p>{exam.description}</p>
            <p>Questions: {exam.questions?.length}</p>
            <button 
              onClick={() => testStartExam(exam._id)} 
              className="btn"
              disabled={!token}
            >
              Test Start Exam
            </button>
          </div>
        ))}
      </div>

      <div>
        <h3>Environment</h3>
        <p>API URL: {process.env.REACT_APP_API_URL}</p>
        <p>Current URL: {window.location.href}</p>
      </div>
    </div>
  );
};

export default Debug;
