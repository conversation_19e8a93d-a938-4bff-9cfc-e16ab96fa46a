import React, { useState } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import api from '../services/api';

const CreateExam = () => {
  const navigate = useNavigate();
  const [examData, setExamData] = useState({
    title: '',
    description: '',
    subject: '',
    duration: 30,
    examType: 'SUBJECT_WISE',
    isPaid: false,
    price: 0,
    instructions: '',
    passingScore: 70
  });

  const [questions, setQuestions] = useState([]);
  const [currentQuestion, setCurrentQuestion] = useState({
    text: '',
    type: 'MCQ',
    options: [
      { text: '', isCorrect: false },
      { text: '', isCorrect: false },
      { text: '', isCorrect: false },
      { text: '', isCorrect: false }
    ],
    explanation: '',
    difficulty: 'EASY',
    points: 1
  });

  const [activeTab, setActiveTab] = useState('basic');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  const subjects = [
    { id: 'mathematics', name: 'Mathematics' },
    { id: 'science', name: 'Science' },
    { id: 'english', name: 'English' },
    { id: 'history', name: 'History' },
    { id: 'geography', name: 'Geography' },
    { id: 'computer', name: 'Computer Science' }
  ];

  const questionTypes = [
    { value: 'MCQ', label: 'Multiple Choice (Single Answer)' },
    { value: 'MULTI_ANSWER', label: 'Multiple Choice (Multiple Answers)' },
    { value: 'TRUE_FALSE', label: 'True/False' },
    { value: 'FILL_BLANK', label: 'Fill in the Blank' },
    { value: 'SHORT_ANSWER', label: 'Short Answer' }
  ];

  const difficulties = [
    { value: 'EASY', label: 'Easy', color: '#28a745' },
    { value: 'MEDIUM', label: 'Medium', color: '#ffc107' },
    { value: 'HARD', label: 'Hard', color: '#dc3545' }
  ];

  const handleExamDataChange = (e) => {
    const { name, value, type, checked } = e.target;
    setExamData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleQuestionChange = (field, value) => {
    setCurrentQuestion(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleOptionChange = (index, field, value) => {
    setCurrentQuestion(prev => ({
      ...prev,
      options: prev.options.map((option, i) => 
        i === index ? { ...option, [field]: value } : option
      )
    }));
  };

  const addOption = () => {
    if (currentQuestion.options.length < 6) {
      setCurrentQuestion(prev => ({
        ...prev,
        options: [...prev.options, { text: '', isCorrect: false }]
      }));
    }
  };

  const removeOption = (index) => {
    if (currentQuestion.options.length > 2) {
      setCurrentQuestion(prev => ({
        ...prev,
        options: prev.options.filter((_, i) => i !== index)
      }));
    }
  };

  const handleQuestionTypeChange = (type) => {
    let newOptions = [];
    
    if (type === 'TRUE_FALSE') {
      newOptions = [
        { text: 'True', isCorrect: false },
        { text: 'False', isCorrect: false }
      ];
    } else if (type === 'FILL_BLANK' || type === 'SHORT_ANSWER') {
      newOptions = [{ text: '', isCorrect: true }];
    } else {
      newOptions = [
        { text: '', isCorrect: false },
        { text: '', isCorrect: false },
        { text: '', isCorrect: false },
        { text: '', isCorrect: false }
      ];
    }

    setCurrentQuestion(prev => ({
      ...prev,
      type,
      options: newOptions
    }));
  };

  const addQuestion = () => {
    if (!currentQuestion.text.trim()) {
      setError('Question text is required');
      return;
    }

    const hasCorrectAnswer = currentQuestion.options.some(opt => opt.isCorrect);
    if (!hasCorrectAnswer && currentQuestion.type !== 'SHORT_ANSWER') {
      setError('Please mark at least one correct answer');
      return;
    }

    const newQuestion = {
      ...currentQuestion,
      id: Date.now(),
      options: currentQuestion.options.filter(opt => opt.text.trim() !== '')
    };

    setQuestions(prev => [...prev, newQuestion]);
    
    // Reset current question
    setCurrentQuestion({
      text: '',
      type: 'MCQ',
      options: [
        { text: '', isCorrect: false },
        { text: '', isCorrect: false },
        { text: '', isCorrect: false },
        { text: '', isCorrect: false }
      ],
      explanation: '',
      difficulty: 'EASY',
      points: 1
    });

    setError('');
    setSuccess('Question added successfully!');
    setTimeout(() => setSuccess(''), 3000);
  };

  const removeQuestion = (questionId) => {
    setQuestions(prev => prev.filter(q => q.id !== questionId));
  };

  const editQuestion = (questionId) => {
    const question = questions.find(q => q.id === questionId);
    if (question) {
      setCurrentQuestion(question);
      removeQuestion(questionId);
      setActiveTab('questions');
    }
  };

  const createExam = async () => {
    if (!examData.title.trim()) {
      setError('Exam title is required');
      return;
    }

    if (questions.length === 0) {
      setError('Please add at least one question');
      return;
    }

    setLoading(true);
    setError('');

    try {
      // Mock exam creation - in real app, this would call the API
      const newExam = {
        id: Date.now(),
        ...examData,
        questions: questions.map(q => ({
          text: q.text,
          type: q.type,
          options: q.options,
          explanation: q.explanation,
          difficulty: q.difficulty,
          points: q.points
        })),
        createdAt: new Date().toISOString(),
        isActive: true
      };

      console.log('Creating exam:', newExam);
      
      setSuccess('Exam created successfully!');
      setTimeout(() => {
        navigate('/admin');
      }, 2000);

    } catch (error) {
      setError('Failed to create exam: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const previewExam = () => {
    // Store exam data in localStorage for preview
    localStorage.setItem('previewExam', JSON.stringify({
      ...examData,
      questions: questions
    }));
    window.open('/exam/preview', '_blank');
  };

  return (
    <div className="create-exam-container">
      <div className="create-exam-header">
        <h1>📝 Create New Exam</h1>
        <div className="header-actions">
          <Link to="/admin" className="btn btn-secondary">Back to Admin</Link>
          {questions.length > 0 && (
            <button onClick={previewExam} className="btn btn-outline">
              👁️ Preview Exam
            </button>
          )}
        </div>
      </div>

      {error && <div className="error">{error}</div>}
      {success && <div className="success">{success}</div>}

      <div className="create-exam-tabs">
        <button 
          className={`tab ${activeTab === 'basic' ? 'active' : ''}`}
          onClick={() => setActiveTab('basic')}
        >
          📋 Basic Info
        </button>
        <button 
          className={`tab ${activeTab === 'questions' ? 'active' : ''}`}
          onClick={() => setActiveTab('questions')}
        >
          ❓ Questions ({questions.length})
        </button>
        <button 
          className={`tab ${activeTab === 'settings' ? 'active' : ''}`}
          onClick={() => setActiveTab('settings')}
        >
          ⚙️ Settings
        </button>
        <button 
          className={`tab ${activeTab === 'review' ? 'active' : ''}`}
          onClick={() => setActiveTab('review')}
        >
          👁️ Review
        </button>
      </div>

      <div className="tab-content">
        {activeTab === 'basic' && (
          <div className="basic-info-tab">
            <h2>📋 Basic Exam Information</h2>
            
            <div className="form-grid">
              <div className="form-group">
                <label>Exam Title *</label>
                <input
                  type="text"
                  name="title"
                  value={examData.title}
                  onChange={handleExamDataChange}
                  placeholder="Enter exam title"
                  required
                />
              </div>

              <div className="form-group">
                <label>Subject *</label>
                <select
                  name="subject"
                  value={examData.subject}
                  onChange={handleExamDataChange}
                  required
                >
                  <option value="">Select Subject</option>
                  {subjects.map(subject => (
                    <option key={subject.id} value={subject.id}>
                      {subject.name}
                    </option>
                  ))}
                </select>
              </div>

              <div className="form-group">
                <label>Duration (minutes) *</label>
                <input
                  type="number"
                  name="duration"
                  value={examData.duration}
                  onChange={handleExamDataChange}
                  min="5"
                  max="300"
                  required
                />
              </div>

              <div className="form-group">
                <label>Exam Type</label>
                <select
                  name="examType"
                  value={examData.examType}
                  onChange={handleExamDataChange}
                >
                  <option value="SUBJECT_WISE">Subject Wise</option>
                  <option value="MOCK_TEST">Mock Test</option>
                  <option value="PRACTICE">Practice Test</option>
                  <option value="FINAL">Final Exam</option>
                </select>
              </div>
            </div>

            <div className="form-group">
              <label>Description</label>
              <textarea
                name="description"
                value={examData.description}
                onChange={handleExamDataChange}
                placeholder="Enter exam description"
                rows="4"
              />
            </div>

            <div className="form-group">
              <label>Instructions for Students</label>
              <textarea
                name="instructions"
                value={examData.instructions}
                onChange={handleExamDataChange}
                placeholder="Enter special instructions for students"
                rows="3"
              />
            </div>
          </div>
        )}

        {activeTab === 'questions' && (
          <div className="questions-tab">
            <h2>❓ Add Questions</h2>
            
            <div className="question-builder">
              <div className="question-form">
                <div className="form-group">
                  <label>Question Text *</label>
                  <textarea
                    value={currentQuestion.text}
                    onChange={(e) => handleQuestionChange('text', e.target.value)}
                    placeholder="Enter your question here"
                    rows="3"
                  />
                </div>

                <div className="question-meta">
                  <div className="form-group">
                    <label>Question Type</label>
                    <select
                      value={currentQuestion.type}
                      onChange={(e) => handleQuestionTypeChange(e.target.value)}
                    >
                      {questionTypes.map(type => (
                        <option key={type.value} value={type.value}>
                          {type.label}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div className="form-group">
                    <label>Difficulty</label>
                    <select
                      value={currentQuestion.difficulty}
                      onChange={(e) => handleQuestionChange('difficulty', e.target.value)}
                    >
                      {difficulties.map(diff => (
                        <option key={diff.value} value={diff.value}>
                          {diff.label}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div className="form-group">
                    <label>Points</label>
                    <input
                      type="number"
                      value={currentQuestion.points}
                      onChange={(e) => handleQuestionChange('points', parseInt(e.target.value))}
                      min="1"
                      max="10"
                    />
                  </div>
                </div>

                {(currentQuestion.type === 'MCQ' || currentQuestion.type === 'MULTI_ANSWER' || currentQuestion.type === 'TRUE_FALSE') && (
                  <div className="options-section">
                    <div className="options-header">
                      <label>Answer Options</label>
                      {currentQuestion.type !== 'TRUE_FALSE' && (
                        <button type="button" onClick={addOption} className="btn btn-sm">
                          + Add Option
                        </button>
                      )}
                    </div>

                    <div className="options-list">
                      {currentQuestion.options.map((option, index) => (
                        <div key={index} className="option-item">
                          <input
                            type={currentQuestion.type === 'MULTI_ANSWER' ? 'checkbox' : 'radio'}
                            name="correctAnswer"
                            checked={option.isCorrect}
                            onChange={(e) => {
                              if (currentQuestion.type === 'MCQ' || currentQuestion.type === 'TRUE_FALSE') {
                                // Single correct answer
                                setCurrentQuestion(prev => ({
                                  ...prev,
                                  options: prev.options.map((opt, i) => ({
                                    ...opt,
                                    isCorrect: i === index
                                  }))
                                }));
                              } else {
                                // Multiple correct answers
                                handleOptionChange(index, 'isCorrect', e.target.checked);
                              }
                            }}
                          />
                          <input
                            type="text"
                            value={option.text}
                            onChange={(e) => handleOptionChange(index, 'text', e.target.value)}
                            placeholder={`Option ${index + 1}`}
                            disabled={currentQuestion.type === 'TRUE_FALSE'}
                          />
                          {currentQuestion.type !== 'TRUE_FALSE' && currentQuestion.options.length > 2 && (
                            <button
                              type="button"
                              onClick={() => removeOption(index)}
                              className="btn btn-sm btn-danger"
                            >
                              ×
                            </button>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {(currentQuestion.type === 'FILL_BLANK' || currentQuestion.type === 'SHORT_ANSWER') && (
                  <div className="form-group">
                    <label>Correct Answer</label>
                    <input
                      type="text"
                      value={currentQuestion.options[0]?.text || ''}
                      onChange={(e) => handleOptionChange(0, 'text', e.target.value)}
                      placeholder="Enter the correct answer"
                    />
                  </div>
                )}

                <div className="form-group">
                  <label>Explanation (Optional)</label>
                  <textarea
                    value={currentQuestion.explanation}
                    onChange={(e) => handleQuestionChange('explanation', e.target.value)}
                    placeholder="Explain the correct answer"
                    rows="2"
                  />
                </div>

                <button onClick={addQuestion} className="btn btn-primary">
                  ➕ Add Question
                </button>
              </div>
            </div>

            {questions.length > 0 && (
              <div className="questions-list">
                <h3>Added Questions ({questions.length})</h3>
                {questions.map((question, index) => (
                  <div key={question.id} className="question-item">
                    <div className="question-header">
                      <span className="question-number">Q{index + 1}</span>
                      <span className={`difficulty-badge ${question.difficulty.toLowerCase()}`}>
                        {question.difficulty}
                      </span>
                      <span className="question-type">{question.type}</span>
                      <span className="question-points">{question.points} pts</span>
                    </div>
                    <div className="question-text">{question.text}</div>
                    <div className="question-options">
                      {question.options.map((option, optIndex) => (
                        <div key={optIndex} className={`option ${option.isCorrect ? 'correct' : ''}`}>
                          {option.isCorrect && '✓ '}{option.text}
                        </div>
                      ))}
                    </div>
                    <div className="question-actions">
                      <button onClick={() => editQuestion(question.id)} className="btn btn-sm">
                        ✏️ Edit
                      </button>
                      <button onClick={() => removeQuestion(question.id)} className="btn btn-sm btn-danger">
                        🗑️ Delete
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {activeTab === 'settings' && (
          <div className="settings-tab">
            <h2>⚙️ Exam Settings</h2>
            
            <div className="settings-grid">
              <div className="setting-group">
                <h3>💰 Pricing</h3>
                <div className="form-group">
                  <label>
                    <input
                      type="checkbox"
                      name="isPaid"
                      checked={examData.isPaid}
                      onChange={handleExamDataChange}
                    />
                    This is a paid exam
                  </label>
                </div>
                
                {examData.isPaid && (
                  <div className="form-group">
                    <label>Price (₹)</label>
                    <input
                      type="number"
                      name="price"
                      value={examData.price}
                      onChange={handleExamDataChange}
                      min="0"
                      step="0.01"
                    />
                  </div>
                )}
              </div>

              <div className="setting-group">
                <h3>📊 Grading</h3>
                <div className="form-group">
                  <label>Passing Score (%)</label>
                  <input
                    type="number"
                    name="passingScore"
                    value={examData.passingScore}
                    onChange={handleExamDataChange}
                    min="0"
                    max="100"
                  />
                </div>
              </div>

              <div className="setting-group">
                <h3>⏰ Time Settings</h3>
                <div className="form-group">
                  <label>Show timer to students</label>
                  <select name="showTimer" defaultValue="true">
                    <option value="true">Yes</option>
                    <option value="false">No</option>
                  </select>
                </div>
              </div>

              <div className="setting-group">
                <h3>📋 Results</h3>
                <div className="form-group">
                  <label>Show results immediately</label>
                  <select name="showResultsImmediately" defaultValue="true">
                    <option value="true">Yes</option>
                    <option value="false">No</option>
                  </select>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'review' && (
          <div className="review-tab">
            <h2>👁️ Review Exam</h2>
            
            <div className="exam-summary">
              <div className="summary-card">
                <h3>📋 Exam Overview</h3>
                <div className="summary-item">
                  <strong>Title:</strong> {examData.title || 'Untitled Exam'}
                </div>
                <div className="summary-item">
                  <strong>Subject:</strong> {subjects.find(s => s.id === examData.subject)?.name || 'Not selected'}
                </div>
                <div className="summary-item">
                  <strong>Duration:</strong> {examData.duration} minutes
                </div>
                <div className="summary-item">
                  <strong>Type:</strong> {examData.examType}
                </div>
                <div className="summary-item">
                  <strong>Questions:</strong> {questions.length}
                </div>
                <div className="summary-item">
                  <strong>Total Points:</strong> {questions.reduce((sum, q) => sum + q.points, 0)}
                </div>
                <div className="summary-item">
                  <strong>Passing Score:</strong> {examData.passingScore}%
                </div>
                {examData.isPaid && (
                  <div className="summary-item">
                    <strong>Price:</strong> ₹{examData.price}
                  </div>
                )}
              </div>

              <div className="summary-card">
                <h3>📊 Question Breakdown</h3>
                <div className="question-stats">
                  <div className="stat">
                    <span className="stat-number">{questions.filter(q => q.difficulty === 'EASY').length}</span>
                    <span className="stat-label">Easy</span>
                  </div>
                  <div className="stat">
                    <span className="stat-number">{questions.filter(q => q.difficulty === 'MEDIUM').length}</span>
                    <span className="stat-label">Medium</span>
                  </div>
                  <div className="stat">
                    <span className="stat-number">{questions.filter(q => q.difficulty === 'HARD').length}</span>
                    <span className="stat-label">Hard</span>
                  </div>
                </div>
                
                <div className="question-types">
                  {questionTypes.map(type => {
                    const count = questions.filter(q => q.type === type.value).length;
                    return count > 0 ? (
                      <div key={type.value} className="type-stat">
                        <span>{type.label}: {count}</span>
                      </div>
                    ) : null;
                  })}
                </div>
              </div>
            </div>

            <div className="create-actions">
              <button 
                onClick={createExam} 
                className="btn btn-success btn-large"
                disabled={loading || questions.length === 0}
              >
                {loading ? '🔄 Creating...' : '✅ Create Exam'}
              </button>
              
              <button onClick={previewExam} className="btn btn-outline btn-large">
                👁️ Preview Exam
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default CreateExam;
