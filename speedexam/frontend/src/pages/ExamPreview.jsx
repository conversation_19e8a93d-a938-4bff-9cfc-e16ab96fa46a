import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';

const ExamPreview = () => {
  const [exam, setExam] = useState(null);
  const [currentQuestion, setCurrentQuestion] = useState(0);

  useEffect(() => {
    // Get exam data from localStorage (set by CreateExam component)
    const previewData = localStorage.getItem('previewExam');
    if (previewData) {
      setExam(JSON.parse(previewData));
    }
  }, []);

  if (!exam) {
    return (
      <div className="container">
        <div className="error">
          <h2>No Exam to Preview</h2>
          <p>Please create an exam first to preview it.</p>
          <Link to="/admin/create-exam" className="btn">Create Exam</Link>
        </div>
      </div>
    );
  }

  const question = exam.questions[currentQuestion];

  return (
    <div className="exam-preview-container">
      <div className="preview-header">
        <h1>📋 Exam Preview</h1>
        <div className="preview-actions">
          <button onClick={() => window.close()} className="btn btn-secondary">
            Close Preview
          </button>
          <Link to="/admin/create-exam" className="btn">
            Back to Editor
          </Link>
        </div>
      </div>

      <div className="exam-info-card">
        <h2>{exam.title}</h2>
        <div className="exam-meta">
          <span className="meta-item">📚 Subject: {exam.subject}</span>
          <span className="meta-item">⏰ Duration: {exam.duration} minutes</span>
          <span className="meta-item">❓ Questions: {exam.questions.length}</span>
          <span className="meta-item">📊 Type: {exam.examType}</span>
        </div>
        {exam.description && (
          <p className="exam-description">{exam.description}</p>
        )}
        {exam.instructions && (
          <div className="exam-instructions">
            <h4>Instructions:</h4>
            <p>{exam.instructions}</p>
          </div>
        )}
      </div>

      <div className="preview-navigation">
        <h3>Questions ({exam.questions.length})</h3>
        <div className="question-nav">
          {exam.questions.map((_, index) => (
            <button
              key={index}
              className={`nav-btn ${index === currentQuestion ? 'active' : ''}`}
              onClick={() => setCurrentQuestion(index)}
            >
              {index + 1}
            </button>
          ))}
        </div>
      </div>

      {question && (
        <div className="question-preview">
          <div className="question-header">
            <h3>Question {currentQuestion + 1}</h3>
            <div className="question-badges">
              <span className={`difficulty-badge ${question.difficulty.toLowerCase()}`}>
                {question.difficulty}
              </span>
              <span className="type-badge">{question.type}</span>
              <span className="points-badge">{question.points} pts</span>
            </div>
          </div>

          <div className="question-content">
            <div className="question-text">{question.text}</div>
            
            <div className="question-options">
              {question.type === 'SHORT_ANSWER' ? (
                <div className="answer-input">
                  <input 
                    type="text" 
                    placeholder="Student will type their answer here..."
                    disabled
                  />
                  <div className="correct-answer">
                    <strong>Correct Answer:</strong> {question.options[0]?.text}
                  </div>
                </div>
              ) : question.type === 'FILL_BLANK' ? (
                <div className="fill-blank">
                  <p>Fill in the blank: ___________</p>
                  <div className="correct-answer">
                    <strong>Correct Answer:</strong> {question.options[0]?.text}
                  </div>
                </div>
              ) : (
                <div className="mcq-options">
                  {question.options.map((option, index) => (
                    <div 
                      key={index} 
                      className={`preview-option ${option.isCorrect ? 'correct' : ''}`}
                    >
                      <div className="option-selector">
                        <input
                          type={question.type === 'MULTI_ANSWER' ? 'checkbox' : 'radio'}
                          name={`preview_question_${currentQuestion}`}
                          checked={option.isCorrect}
                          disabled
                        />
                        <span className="option-letter">
                          {String.fromCharCode(65 + index)}
                        </span>
                      </div>
                      <div className="option-text">{option.text}</div>
                      {option.isCorrect && (
                        <div className="correct-indicator">✓</div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>

            {question.explanation && (
              <div className="question-explanation">
                <h4>Explanation:</h4>
                <p>{question.explanation}</p>
              </div>
            )}
          </div>

          <div className="preview-controls">
            <button 
              onClick={() => setCurrentQuestion(Math.max(0, currentQuestion - 1))}
              disabled={currentQuestion === 0}
              className="btn btn-secondary"
            >
              ← Previous
            </button>
            
            <span className="question-counter">
              {currentQuestion + 1} of {exam.questions.length}
            </span>
            
            <button 
              onClick={() => setCurrentQuestion(Math.min(exam.questions.length - 1, currentQuestion + 1))}
              disabled={currentQuestion === exam.questions.length - 1}
              className="btn btn-secondary"
            >
              Next →
            </button>
          </div>
        </div>
      )}

      <div className="exam-summary">
        <h3>📊 Exam Summary</h3>
        <div className="summary-stats">
          <div className="stat-group">
            <h4>Question Types</h4>
            <div className="type-breakdown">
              {['MCQ', 'MULTI_ANSWER', 'TRUE_FALSE', 'FILL_BLANK', 'SHORT_ANSWER'].map(type => {
                const count = exam.questions.filter(q => q.type === type).length;
                return count > 0 ? (
                  <div key={type} className="type-stat">
                    <span className="type-name">{type.replace('_', ' ')}</span>
                    <span className="type-count">{count}</span>
                  </div>
                ) : null;
              })}
            </div>
          </div>

          <div className="stat-group">
            <h4>Difficulty Distribution</h4>
            <div className="difficulty-breakdown">
              {['EASY', 'MEDIUM', 'HARD'].map(difficulty => {
                const count = exam.questions.filter(q => q.difficulty === difficulty).length;
                return (
                  <div key={difficulty} className="difficulty-stat">
                    <span className={`difficulty-name ${difficulty.toLowerCase()}`}>
                      {difficulty}
                    </span>
                    <span className="difficulty-count">{count}</span>
                  </div>
                );
              })}
            </div>
          </div>

          <div className="stat-group">
            <h4>Scoring</h4>
            <div className="scoring-info">
              <div className="score-item">
                <span>Total Points:</span>
                <span>{exam.questions.reduce((sum, q) => sum + q.points, 0)}</span>
              </div>
              <div className="score-item">
                <span>Passing Score:</span>
                <span>{exam.passingScore}%</span>
              </div>
              <div className="score-item">
                <span>Average Points per Question:</span>
                <span>{(exam.questions.reduce((sum, q) => sum + q.points, 0) / exam.questions.length).toFixed(1)}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="preview-footer">
        <div className="footer-note">
          <p>📝 This is a preview of how the exam will appear to students.</p>
          <p>✅ Correct answers are highlighted for your reference.</p>
        </div>
        
        <div className="footer-actions">
          <Link to="/admin/create-exam" className="btn btn-primary">
            ✏️ Edit Exam
          </Link>
          <button 
            onClick={() => {
              if (window.confirm('Are you ready to publish this exam?')) {
                // In a real app, this would call the API to save the exam
                alert('Exam would be published in a real application!');
              }
            }}
            className="btn btn-success"
          >
            🚀 Publish Exam
          </button>
        </div>
      </div>
    </div>
  );
};

export default ExamPreview;
