import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import api from '../services/api';

const ExamTaking = () => {
  const { examId } = useParams();
  const navigate = useNavigate();
  const [exam, setExam] = useState(null);
  const [attemptId, setAttemptId] = useState(null);
  const [answers, setAnswers] = useState({});
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [timeLeft, setTimeLeft] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [submitting, setSubmitting] = useState(false);
  const [examStartTime, setExamStartTime] = useState(null);
  const [questionTimes, setQuestionTimes] = useState({});
  const [showInstructions, setShowInstructions] = useState(true);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [warningCount, setWarningCount] = useState(0);
  const [questionStartTime, setQuestionStartTime] = useState(null);

  useEffect(() => {
    const token = localStorage.getItem('token');
    if (!token) {
      navigate('/login');
      return;
    }

    startExam();
  }, [examId, navigate]); // eslint-disable-line react-hooks/exhaustive-deps

  useEffect(() => {
    if (timeLeft > 0) {
      const timer = setTimeout(() => setTimeLeft(timeLeft - 1), 1000);
      return () => clearTimeout(timer);
    } else if (timeLeft === 0 && exam) {
      handleSubmit();
    }
  }, [timeLeft, exam]); // eslint-disable-line react-hooks/exhaustive-deps

  const startExam = async () => {
    try {
      console.log('Starting exam with ID:', examId);
      const response = await api.post(`/exams/${examId}/start`);
      console.log('Start exam response:', response.data);

      setExam(response.data.exam);
      setAttemptId(response.data.attempt);
      setTimeLeft(response.data.exam.duration * 60); // Convert minutes to seconds
      setExamStartTime(Date.now());
      setQuestionStartTime(Date.now());

      // Initialize answers
      const initialAnswers = {};
      response.data.exam.questions.forEach(question => {
        initialAnswers[question._id] = {
          question: question._id,
          selectedOptions: [],
          answer: ''
        };
      });
      setAnswers(initialAnswers);

      // Initialize question times
      const initialTimes = {};
      response.data.exam.questions.forEach(question => {
        initialTimes[question._id] = 0;
      });
      setQuestionTimes(initialTimes);

      console.log('Exam started successfully');
    } catch (error) {
      console.error('Error starting exam:', error);
      setError(error.response?.data?.message || 'Failed to start exam');
    } finally {
      setLoading(false);
    }
  };

  const handleAnswerChange = (questionId, value, isMultiple = false) => {
    setAnswers(prev => ({
      ...prev,
      [questionId]: {
        ...prev[questionId],
        selectedOptions: isMultiple 
          ? (prev[questionId].selectedOptions.includes(value)
              ? prev[questionId].selectedOptions.filter(opt => opt !== value)
              : [...prev[questionId].selectedOptions, value])
          : [value]
      }
    }));
  };

  const handleSubmit = async () => {
    if (submitting) return;
    
    setSubmitting(true);
    try {
      const answersArray = Object.values(answers);
      await api.post(`/exam-attempts/${attemptId}/submit`, { answers: answersArray });
      navigate(`/results/${attemptId}`);
    } catch (error) {
      setError('Failed to submit exam');
      setSubmitting(false);
    }
  };

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const trackQuestionTime = (questionId) => {
    if (questionStartTime && questionId) {
      const timeSpent = Math.floor((Date.now() - questionStartTime) / 1000);
      setQuestionTimes(prev => ({
        ...prev,
        [questionId]: (prev[questionId] || 0) + timeSpent
      }));
    }
  };

  const navigateToQuestion = (questionIndex) => {
    if (exam && exam.questions[currentQuestion]) {
      trackQuestionTime(exam.questions[currentQuestion]._id);
    }
    setCurrentQuestion(questionIndex);
    setQuestionStartTime(Date.now());
  };

  const toggleFullscreen = () => {
    if (!isFullscreen) {
      document.documentElement.requestFullscreen?.();
      setIsFullscreen(true);
    } else {
      document.exitFullscreen?.();
      setIsFullscreen(false);
    }
  };

  const handleTabSwitch = () => {
    setWarningCount(prev => prev + 1);
    if (warningCount >= 2) {
      alert('Warning: Multiple tab switches detected. This exam will be auto-submitted for security.');
      handleSubmit();
    } else {
      alert(`Warning ${warningCount + 1}/3: Please stay on this tab during the exam.`);
    }
  };

  // Add event listeners for tab switching detection
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden && exam && !submitting) {
        handleTabSwitch();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, [exam, submitting, warningCount]);

  const startExamProper = () => {
    setShowInstructions(false);
    setQuestionStartTime(Date.now());
  };

  if (loading) return <div className="loading">Starting exam...</div>;
  if (error) return <div className="error">{error}</div>;
  if (!exam) return <div className="error">Exam not found</div>;

  // Show instructions screen first
  if (showInstructions) {
    return (
      <div className="exam-instructions-container">
        <div className="instructions-card">
          <h1>📋 Exam Instructions</h1>

          <div className="exam-info">
            <h2>{exam.title}</h2>
            <div className="exam-details">
              <div className="detail-item">
                <span className="detail-icon">📚</span>
                <span>Subject: {exam.subject?.name}</span>
              </div>
              <div className="detail-item">
                <span className="detail-icon">❓</span>
                <span>Questions: {exam.questions.length}</span>
              </div>
              <div className="detail-item">
                <span className="detail-icon">⏰</span>
                <span>Duration: {exam.duration} minutes</span>
              </div>
              <div className="detail-item">
                <span className="detail-icon">📊</span>
                <span>Total Points: {exam.questions.reduce((sum, q) => sum + (q.points || 1), 0)}</span>
              </div>
            </div>
          </div>

          <div className="general-instructions">
            <h3>📝 General Instructions</h3>
            <ul>
              <li>Read each question carefully before answering</li>
              <li>You can navigate between questions using the question numbers</li>
              <li>Your progress is automatically saved</li>
              <li>Make sure you have a stable internet connection</li>
              <li>Do not refresh the page or close the browser</li>
              <li>Avoid switching tabs or applications during the exam</li>
              <li>Submit your exam before the time runs out</li>
            </ul>
          </div>

          {exam.instructions && (
            <div className="specific-instructions">
              <h3>📋 Specific Instructions</h3>
              <p>{exam.instructions}</p>
            </div>
          )}

          <div className="exam-controls">
            <button onClick={toggleFullscreen} className="btn btn-secondary">
              🖥️ Enter Fullscreen
            </button>
            <button onClick={startExamProper} className="btn btn-success btn-large">
              🚀 Start Exam
            </button>
          </div>

          <div className="warning-note">
            <p>⚠️ Once you start the exam, the timer will begin and cannot be paused.</p>
          </div>
        </div>
      </div>
    );
  }

  const question = exam.questions[currentQuestion];

  return (
    <div className={`exam-taking-container ${isFullscreen ? 'fullscreen' : ''}`}>
      {/* Exam Header */}
      <div className="exam-header">
        <div className="exam-info">
          <h2>{exam.title}</h2>
          <p>Question {currentQuestion + 1} of {exam.questions.length}</p>
        </div>

        <div className="exam-controls">
          <div className="timer">
            ⏰ {formatTime(timeLeft)}
          </div>
          <button onClick={toggleFullscreen} className="btn btn-sm">
            {isFullscreen ? '🗗' : '🖥️'}
          </button>
        </div>
      </div>

      {/* Exam Header */}
      <div className="exam-header">
        <h2>{exam.title}</h2>
        <p><strong>Subject:</strong> {exam.subject?.name}</p>
        <p>Question {currentQuestion + 1} of {exam.questions.length}</p>
      </div>

      {/* Enhanced Question Navigation */}
      <div className="question-nav">
        <div className="nav-header">
          <h3>📝 Questions</h3>
          <div className="progress-info">
            <span className="answered-count">
              {Object.values(answers).filter(a => a.selectedOptions.length > 0).length} answered
            </span>
            <span className="total-count">of {exam.questions.length}</span>
          </div>
        </div>

        <div className="nav-grid">
          {exam.questions.map((_, index) => {
            const isAnswered = answers[exam.questions[index]._id]?.selectedOptions.length > 0;
            const timeSpent = questionTimes[exam.questions[index]._id] || 0;

            return (
              <button
                key={index}
                className={`nav-btn ${index === currentQuestion ? 'active' : ''} ${
                  isAnswered ? 'answered' : ''
                }`}
                onClick={() => navigateToQuestion(index)}
                title={`Question ${index + 1}${isAnswered ? ' (Answered)' : ''} - Time: ${Math.floor(timeSpent / 60)}:${(timeSpent % 60).toString().padStart(2, '0')}`}
              >
                <span className="question-number">{index + 1}</span>
                {isAnswered && <span className="answered-indicator">✓</span>}
                <span className="time-indicator">{Math.floor(timeSpent / 60)}m</span>
              </button>
            );
          })}
        </div>

        <div className="nav-legend">
          <div className="legend-item">
            <div className="legend-color current"></div>
            <span>Current</span>
          </div>
          <div className="legend-item">
            <div className="legend-color answered"></div>
            <span>Answered</span>
          </div>
          <div className="legend-item">
            <div className="legend-color unanswered"></div>
            <span>Unanswered</span>
          </div>
        </div>
      </div>

      {/* Question */}
      <div className="question-container">
        <div className="question-header">
          <h3>Question {currentQuestion + 1}</h3>
          <span className="question-type">
            {question.type === 'MCQ' ? 'Multiple Choice' :
             question.type === 'TRUE_FALSE' ? 'True/False' :
             'Multiple Answer'}
          </span>
        </div>

        <div className="question-text">{question.text}</div>

        <div className="options">
          {question.options.map((option, index) => {
            const isSelected = answers[question._id]?.selectedOptions.includes(option.text);
            const optionLetter = String.fromCharCode(65 + index);

            return (
              <div key={index} className={`option ${isSelected ? 'selected' : ''}`}>
                <label>
                  <input
                    type={question.type === 'MULTI_ANSWER' ? 'checkbox' : 'radio'}
                    name={`question_${question._id}`}
                    value={option.text}
                    checked={isSelected}
                    onChange={(e) => handleAnswerChange(
                      question._id,
                      option.text,
                      question.type === 'MULTI_ANSWER'
                    )}
                  />
                  <span className="option-letter">{optionLetter}.</span>
                  <span className="option-text">{option.text}</span>
                </label>
              </div>
            );
          })}
        </div>

        {/* Answer Status */}
        <div className="answer-status">
          {answers[question._id]?.selectedOptions.length > 0 ? (
            <span className="answered">✓ Answered</span>
          ) : (
            <span className="unanswered">⚠ Not answered</span>
          )}
        </div>
      </div>

      {/* Enhanced Navigation */}
      <div className="navigation">
        <button
          className="btn btn-secondary"
          onClick={() => navigateToQuestion(Math.max(0, currentQuestion - 1))}
          disabled={currentQuestion === 0}
        >
          ← Previous
        </button>

        <div className="nav-center">
          <div className="question-info">
            <span className="current-q">Q{currentQuestion + 1}</span>
            <span className="total-q">of {exam.questions.length}</span>
          </div>
          <div className="time-spent">
            Time on this question: {Math.floor((questionTimes[question._id] || 0) / 60)}:{((questionTimes[question._id] || 0) % 60).toString().padStart(2, '0')}
          </div>
        </div>

        {currentQuestion < exam.questions.length - 1 ? (
          <button
            className="btn btn-primary"
            onClick={() => navigateToQuestion(currentQuestion + 1)}
          >
            Next →
          </button>
        ) : (
          <button
            className="btn btn-success"
            onClick={handleSubmit}
            disabled={submitting}
          >
            {submitting ? 'Submitting...' : 'Submit Exam'}
          </button>
        )}
      </div>

      {/* Exam Summary */}
      <div className="exam-summary">
        <h4>Exam Progress</h4>
        <p>
          Answered: {Object.values(answers).filter(a => a.selectedOptions.length > 0).length} / {exam.questions.length}
        </p>
        <div className="progress-bar">
          <div
            className="progress"
            style={{
              width: `${(Object.values(answers).filter(a => a.selectedOptions.length > 0).length / exam.questions.length) * 100}%`
            }}
          ></div>
        </div>
      </div>
    </div>
  );
};

export default ExamTaking;
