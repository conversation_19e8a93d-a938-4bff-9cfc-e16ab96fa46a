import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import api from '../services/api';

const ExamTaking = () => {
  const { examId } = useParams();
  const navigate = useNavigate();
  const [exam, setExam] = useState(null);
  const [attemptId, setAttemptId] = useState(null);
  const [answers, setAnswers] = useState({});
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [timeLeft, setTimeLeft] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [submitting, setSubmitting] = useState(false);

  useEffect(() => {
    const token = localStorage.getItem('token');
    if (!token) {
      navigate('/login');
      return;
    }

    startExam();
  }, [examId, navigate]); // eslint-disable-line react-hooks/exhaustive-deps

  useEffect(() => {
    if (timeLeft > 0) {
      const timer = setTimeout(() => setTimeLeft(timeLeft - 1), 1000);
      return () => clearTimeout(timer);
    } else if (timeLeft === 0 && exam) {
      handleSubmit();
    }
  }, [timeLeft, exam]); // eslint-disable-line react-hooks/exhaustive-deps

  const startExam = async () => {
    try {
      console.log('Starting exam with ID:', examId);
      const response = await api.post(`/exams/${examId}/start`);
      console.log('Start exam response:', response.data);

      setExam(response.data.exam);
      setAttemptId(response.data.attempt);
      setTimeLeft(response.data.exam.duration * 60); // Convert minutes to seconds

      // Initialize answers
      const initialAnswers = {};
      response.data.exam.questions.forEach(question => {
        initialAnswers[question._id] = {
          question: question._id,
          selectedOptions: [],
          answer: ''
        };
      });
      setAnswers(initialAnswers);
      console.log('Exam started successfully');
    } catch (error) {
      console.error('Error starting exam:', error);
      setError(error.response?.data?.message || 'Failed to start exam');
    } finally {
      setLoading(false);
    }
  };

  const handleAnswerChange = (questionId, value, isMultiple = false) => {
    setAnswers(prev => ({
      ...prev,
      [questionId]: {
        ...prev[questionId],
        selectedOptions: isMultiple 
          ? (prev[questionId].selectedOptions.includes(value)
              ? prev[questionId].selectedOptions.filter(opt => opt !== value)
              : [...prev[questionId].selectedOptions, value])
          : [value]
      }
    }));
  };

  const handleSubmit = async () => {
    if (submitting) return;
    
    setSubmitting(true);
    try {
      const answersArray = Object.values(answers);
      await api.post(`/exam-attempts/${attemptId}/submit`, { answers: answersArray });
      navigate(`/results/${attemptId}`);
    } catch (error) {
      setError('Failed to submit exam');
      setSubmitting(false);
    }
  };

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  if (loading) return <div className="loading">Starting exam...</div>;
  if (error) return <div className="error">{error}</div>;
  if (!exam) return <div className="error">Exam not found</div>;

  const question = exam.questions[currentQuestion];

  return (
    <div className="container">
      {/* Timer */}
      <div className="timer">
        Time Left: {formatTime(timeLeft)}
      </div>

      {/* Exam Header */}
      <div className="exam-header">
        <h2>{exam.title}</h2>
        <p><strong>Subject:</strong> {exam.subject?.name}</p>
        <p>Question {currentQuestion + 1} of {exam.questions.length}</p>
      </div>

      {/* Question Navigation */}
      <div className="question-nav">
        <p><strong>Questions:</strong></p>
        {exam.questions.map((_, index) => (
          <button
            key={index}
            className={`nav-btn ${index === currentQuestion ? 'active' : ''} ${
              answers[exam.questions[index]._id]?.selectedOptions.length > 0 ? 'answered' : ''
            }`}
            onClick={() => setCurrentQuestion(index)}
          >
            {index + 1}
          </button>
        ))}
      </div>

      {/* Question */}
      <div className="question-container">
        <div className="question-header">
          <h3>Question {currentQuestion + 1}</h3>
          <span className="question-type">
            {question.type === 'MCQ' ? 'Multiple Choice' :
             question.type === 'TRUE_FALSE' ? 'True/False' :
             'Multiple Answer'}
          </span>
        </div>

        <div className="question-text">{question.text}</div>

        <div className="options">
          {question.options.map((option, index) => {
            const isSelected = answers[question._id]?.selectedOptions.includes(option.text);
            const optionLetter = String.fromCharCode(65 + index);

            return (
              <div key={index} className={`option ${isSelected ? 'selected' : ''}`}>
                <label>
                  <input
                    type={question.type === 'MULTI_ANSWER' ? 'checkbox' : 'radio'}
                    name={`question_${question._id}`}
                    value={option.text}
                    checked={isSelected}
                    onChange={(e) => handleAnswerChange(
                      question._id,
                      option.text,
                      question.type === 'MULTI_ANSWER'
                    )}
                  />
                  <span className="option-letter">{optionLetter}.</span>
                  <span className="option-text">{option.text}</span>
                </label>
              </div>
            );
          })}
        </div>

        {/* Answer Status */}
        <div className="answer-status">
          {answers[question._id]?.selectedOptions.length > 0 ? (
            <span className="answered">✓ Answered</span>
          ) : (
            <span className="unanswered">⚠ Not answered</span>
          )}
        </div>
      </div>

      {/* Navigation */}
      <div className="navigation">
        <button
          className="btn btn-secondary"
          onClick={() => setCurrentQuestion(Math.max(0, currentQuestion - 1))}
          disabled={currentQuestion === 0}
        >
          ← Previous
        </button>

        {currentQuestion < exam.questions.length - 1 ? (
          <button
            className="btn"
            onClick={() => setCurrentQuestion(currentQuestion + 1)}
          >
            Next →
          </button>
        ) : (
          <button
            className="btn btn-success"
            onClick={handleSubmit}
            disabled={submitting}
          >
            {submitting ? 'Submitting...' : 'Submit Exam'}
          </button>
        )}
      </div>

      {/* Exam Summary */}
      <div className="exam-summary">
        <h4>Exam Progress</h4>
        <p>
          Answered: {Object.values(answers).filter(a => a.selectedOptions.length > 0).length} / {exam.questions.length}
        </p>
        <div className="progress-bar">
          <div
            className="progress"
            style={{
              width: `${(Object.values(answers).filter(a => a.selectedOptions.length > 0).length / exam.questions.length) * 100}%`
            }}
          ></div>
        </div>
      </div>
    </div>
  );
};

export default ExamTaking;
