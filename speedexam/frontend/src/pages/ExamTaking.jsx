import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import api from '../services/api';

const ExamTaking = () => {
  const { examId } = useParams();
  const navigate = useNavigate();
  const [exam, setExam] = useState(null);
  const [attemptId, setAttemptId] = useState(null);
  const [answers, setAnswers] = useState({});
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [timeLeft, setTimeLeft] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [submitting, setSubmitting] = useState(false);

  useEffect(() => {
    const token = localStorage.getItem('token');
    if (!token) {
      navigate('/login');
      return;
    }

    startExam();
  }, [examId, navigate]); // eslint-disable-line react-hooks/exhaustive-deps

  useEffect(() => {
    if (timeLeft > 0) {
      const timer = setTimeout(() => setTimeLeft(timeLeft - 1), 1000);
      return () => clearTimeout(timer);
    } else if (timeLeft === 0 && exam) {
      handleSubmit();
    }
  }, [timeLeft, exam]); // eslint-disable-line react-hooks/exhaustive-deps

  const startExam = async () => {
    try {
      console.log('Starting exam with ID:', examId);
      const response = await api.post(`/exams/${examId}/start`);
      console.log('Start exam response:', response.data);

      setExam(response.data.exam);
      setAttemptId(response.data.attempt);
      setTimeLeft(response.data.exam.duration * 60); // Convert minutes to seconds

      // Initialize answers
      const initialAnswers = {};
      response.data.exam.questions.forEach(question => {
        initialAnswers[question._id] = {
          question: question._id,
          selectedOptions: [],
          answer: ''
        };
      });
      setAnswers(initialAnswers);
      console.log('Exam started successfully');
    } catch (error) {
      console.error('Error starting exam:', error);
      setError(error.response?.data?.message || 'Failed to start exam');
    } finally {
      setLoading(false);
    }
  };

  const handleAnswerChange = (questionId, value, isMultiple = false) => {
    setAnswers(prev => ({
      ...prev,
      [questionId]: {
        ...prev[questionId],
        selectedOptions: isMultiple 
          ? (prev[questionId].selectedOptions.includes(value)
              ? prev[questionId].selectedOptions.filter(opt => opt !== value)
              : [...prev[questionId].selectedOptions, value])
          : [value]
      }
    }));
  };

  const handleSubmit = async () => {
    if (submitting) return;
    
    setSubmitting(true);
    try {
      const answersArray = Object.values(answers);
      await api.post(`/exam-attempts/${attemptId}/submit`, { answers: answersArray });
      navigate(`/results/${attemptId}`);
    } catch (error) {
      setError('Failed to submit exam');
      setSubmitting(false);
    }
  };

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  if (loading) return <div className="loading">Starting exam...</div>;
  if (error) return <div className="error">{error}</div>;
  if (!exam) return <div className="error">Exam not found</div>;

  const question = exam.questions[currentQuestion];
  const progress = ((currentQuestion + 1) / exam.questions.length) * 100;

  return (
    <div className="exam-container">
      {/* Header with timer and progress */}
      <div className="exam-header">
        <div className="exam-info">
          <h2>{exam.title}</h2>
          <div className="exam-meta">
            <span className="subject-badge">{exam.subject?.name}</span>
            <span className="question-counter">
              Question {currentQuestion + 1} of {exam.questions.length}
            </span>
          </div>
        </div>

        <div className="timer-container">
          <div className="timer">
            ⏰ {formatTime(timeLeft)}
          </div>
        </div>
      </div>

      {/* Progress bar */}
      <div className="progress-bar">
        <div
          className="progress-fill"
          style={{ width: `${progress}%` }}
        ></div>
        <span className="progress-text">{Math.round(progress)}% Complete</span>
      </div>

      {/* Question navigation dots */}
      <div className="question-nav">
        {exam.questions.map((_, index) => (
          <button
            key={index}
            className={`nav-dot ${index === currentQuestion ? 'active' : ''} ${
              answers[exam.questions[index]._id]?.selectedOptions.length > 0 ? 'answered' : ''
            }`}
            onClick={() => setCurrentQuestion(index)}
          >
            {index + 1}
          </button>
        ))}
      </div>

      {/* Main question area */}
      <div className="question-section">
        <div className="question-header">
          <div className="question-type-badge">
            {question.type === 'MCQ' ? 'Multiple Choice' :
             question.type === 'TRUE_FALSE' ? 'True/False' :
             'Multiple Answer'}
          </div>
          {question.type === 'MULTI_ANSWER' && (
            <div className="instruction">Select all correct answers</div>
          )}
        </div>

        <div className="question-content">
          <h3 className="question-text">
            Q{currentQuestion + 1}. {question.text}
          </h3>

          <div className="options-container">
            {question.options.map((option, index) => {
              const isSelected = answers[question._id]?.selectedOptions.includes(option.text);
              const optionLetter = String.fromCharCode(65 + index);

              return (
                <div
                  key={index}
                  className={`option-card ${isSelected ? 'selected' : ''}`}
                  onClick={() => handleAnswerChange(
                    question._id,
                    option.text,
                    question.type === 'MULTI_ANSWER'
                  )}
                >
                  <div className="option-selector">
                    <input
                      type={question.type === 'MULTI_ANSWER' ? 'checkbox' : 'radio'}
                      name={`question_${question._id}`}
                      value={option.text}
                      checked={isSelected}
                      onChange={() => {}} // Handled by onClick above
                    />
                    <span className="option-letter">{optionLetter}</span>
                  </div>
                  <div className="option-text">{option.text}</div>
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Navigation buttons */}
      <div className="exam-navigation">
        <div className="nav-left">
          <button
            className="btn btn-secondary"
            onClick={() => setCurrentQuestion(Math.max(0, currentQuestion - 1))}
            disabled={currentQuestion === 0}
          >
            ← Previous
          </button>
        </div>

        <div className="nav-center">
          <div className="answer-status">
            {answers[question._id]?.selectedOptions.length > 0 ? (
              <span className="answered">✓ Answered</span>
            ) : (
              <span className="unanswered">Not answered</span>
            )}
          </div>
        </div>

        <div className="nav-right">
          {currentQuestion < exam.questions.length - 1 ? (
            <button
              className="btn btn-primary"
              onClick={() => setCurrentQuestion(currentQuestion + 1)}
            >
              Next →
            </button>
          ) : (
            <button
              className="btn btn-success"
              onClick={handleSubmit}
              disabled={submitting}
            >
              {submitting ? 'Submitting...' : 'Submit Exam 📝'}
            </button>
          )}
        </div>
      </div>

      {/* Summary panel for final review */}
      {currentQuestion === exam.questions.length - 1 && (
        <div className="exam-summary">
          <h4>📋 Exam Summary</h4>
          <div className="summary-stats">
            <div className="stat">
              <span className="stat-number">
                {Object.values(answers).filter(a => a.selectedOptions.length > 0).length}
              </span>
              <span className="stat-label">Answered</span>
            </div>
            <div className="stat">
              <span className="stat-number">
                {exam.questions.length - Object.values(answers).filter(a => a.selectedOptions.length > 0).length}
              </span>
              <span className="stat-label">Unanswered</span>
            </div>
            <div className="stat">
              <span className="stat-number">{exam.questions.length}</span>
              <span className="stat-label">Total</span>
            </div>
          </div>
          <p className="summary-note">
            Review your answers before submitting. You cannot change them after submission.
          </p>
        </div>
      )}
    </div>
  );
};

export default ExamTaking;
