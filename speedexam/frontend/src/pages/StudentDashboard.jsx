import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import api from '../services/api';

const StudentDashboard = () => {
  const [student, setStudent] = useState(null);
  const [exams, setExams] = useState([]);
  const [recentAttempts, setRecentAttempts] = useState([]);
  const [stats, setStats] = useState({
    totalAttempts: 0,
    averageScore: 0,
    bestScore: 0,
    totalTimeSpent: 0
  });
  const navigate = useNavigate();

  useEffect(() => {
    const token = localStorage.getItem('token');
    if (!token) {
      navigate('/login');
      return;
    }

    fetchStudentData();
  }, [navigate]);

  const fetchStudentData = async () => {
    try {
      // Mock student data
      setStudent({
        name: 'Test User',
        email: '<EMAIL>',
        studentId: 'STU001',
        joinDate: '2024-01-01'
      });

      // Fetch available exams
      const examsResponse = await api.get('/exams');
      setExams(examsResponse.data);

      // Mock recent attempts
      setRecentAttempts([
        {
          id: 1,
          examTitle: 'Basic Mathematics Test',
          score: 3,
          totalQuestions: 3,
          percentage: 100,
          timeTaken: 15,
          date: '2024-01-15',
          status: 'Completed'
        },
        {
          id: 2,
          examTitle: 'General Science Quiz',
          score: 1,
          totalQuestions: 2,
          percentage: 50,
          timeTaken: 12,
          date: '2024-01-14',
          status: 'Completed'
        },
        {
          id: 3,
          examTitle: 'Basic Mathematics Test',
          score: 2,
          totalQuestions: 3,
          percentage: 67,
          timeTaken: 20,
          date: '2024-01-13',
          status: 'Completed'
        }
      ]);

      // Calculate stats
      setStats({
        totalAttempts: 3,
        averageScore: 72,
        bestScore: 100,
        totalTimeSpent: 47
      });

    } catch (error) {
      console.error('Error fetching student data:', error);
    }
  };

  const handleLogout = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('userRole');
    navigate('/');
  };

  if (!student) {
    return <div className="loading">Loading student dashboard...</div>;
  }

  return (
    <div className="student-dashboard">
      {/* Header */}
      <div className="dashboard-header">
        <div className="student-info">
          <h1>👋 Welcome, {student.name}!</h1>
          <p className="student-details">
            Student ID: {student.studentId} | Email: {student.email}
          </p>
        </div>
        <div className="header-actions">
          <Link to="/admin/login" className="btn btn-secondary">Admin Panel</Link>
          <button onClick={handleLogout} className="btn btn-outline">Logout</button>
        </div>
      </div>

      {/* Statistics */}
      <div className="student-stats">
        <div className="stat-card">
          <div className="stat-icon">📝</div>
          <div className="stat-content">
            <h3>{stats.totalAttempts}</h3>
            <p>Exams Taken</p>
          </div>
        </div>
        
        <div className="stat-card">
          <div className="stat-icon">📊</div>
          <div className="stat-content">
            <h3>{stats.averageScore}%</h3>
            <p>Average Score</p>
          </div>
        </div>
        
        <div className="stat-card">
          <div className="stat-icon">🏆</div>
          <div className="stat-content">
            <h3>{stats.bestScore}%</h3>
            <p>Best Score</p>
          </div>
        </div>
        
        <div className="stat-card">
          <div className="stat-icon">⏱️</div>
          <div className="stat-content">
            <h3>{stats.totalTimeSpent}</h3>
            <p>Minutes Studied</p>
          </div>
        </div>
      </div>

      {/* Available Exams */}
      <div className="dashboard-section">
        <h2>📚 Available Exams</h2>
        <div className="exams-grid">
          {exams.map(exam => (
            <div key={exam._id} className="student-exam-card">
              <div className="exam-header">
                <h3>{exam.title}</h3>
                <span className="exam-subject">{exam.subject?.name}</span>
              </div>
              
              <div className="exam-details">
                <div className="exam-detail">
                  <span className="detail-icon">❓</span>
                  <span>{exam.questions?.length} Questions</span>
                </div>
                <div className="exam-detail">
                  <span className="detail-icon">⏰</span>
                  <span>{exam.duration} Minutes</span>
                </div>
                <div className="exam-detail">
                  <span className="detail-icon">📋</span>
                  <span>{exam.examType}</span>
                </div>
              </div>
              
              <p className="exam-description">{exam.description}</p>
              
              <div className="exam-actions">
                <Link to={`/exam/${exam._id}`} className="btn btn-primary">
                  Start Exam
                </Link>
                <button className="btn btn-outline">View Details</button>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Recent Attempts */}
      <div className="dashboard-section">
        <h2>📋 Recent Exam Attempts</h2>
        <div className="attempts-list">
          {recentAttempts.map(attempt => (
            <div key={attempt.id} className="attempt-card">
              <div className="attempt-info">
                <h4>{attempt.examTitle}</h4>
                <p className="attempt-date">{attempt.date}</p>
              </div>
              
              <div className="attempt-score">
                <div className="score-circle">
                  <span className="score-percentage">{attempt.percentage}%</span>
                </div>
                <div className="score-details">
                  <p>Score: {attempt.score}/{attempt.totalQuestions}</p>
                  <p>Time: {attempt.timeTaken} min</p>
                </div>
              </div>
              
              <div className="attempt-status">
                <span className={`status-badge ${
                  attempt.percentage >= 70 ? 'passed' : 'failed'
                }`}>
                  {attempt.percentage >= 70 ? 'Passed' : 'Failed'}
                </span>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Quick Actions */}
      <div className="dashboard-section">
        <h2>⚡ Quick Actions</h2>
        <div className="quick-actions">
          <Link to="/exams" className="action-card">
            <div className="action-icon">📚</div>
            <h3>Browse All Exams</h3>
            <p>View all available exams</p>
          </Link>
          
          <Link to="/student/results" className="action-card">
            <div className="action-icon">📊</div>
            <h3>View All Results</h3>
            <p>Check your exam history</p>
          </Link>
          
          <Link to="/student/profile" className="action-card">
            <div className="action-icon">👤</div>
            <h3>Update Profile</h3>
            <p>Manage your account</p>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default StudentDashboard;
