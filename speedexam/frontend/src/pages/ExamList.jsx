import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import api from '../services/api';

const ExamList = () => {
  const [exams, setExams] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const navigate = useNavigate();

  useEffect(() => {
    const token = localStorage.getItem('token');
    if (!token) {
      navigate('/login');
      return;
    }

    fetchExams();
  }, [navigate]);

  const fetchExams = async () => {
    try {
      console.log('Fetching exams from API...');
      const response = await api.get('/exams');
      console.log('Exams fetched successfully:', response.data);
      setExams(response.data);
    } catch (error) {
      console.error('Error fetching exams:', error);
      setError('Failed to fetch exams: ' + (error.response?.data?.message || error.message));
    } finally {
      setLoading(false);
    }
  };

  if (loading) return <div className="loading">Loading exams...</div>;

  return (
    <div className="container">
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <h2>Available Exams</h2>
        <Link to="/" className="btn btn-secondary">Home</Link>
      </div>
      
      {error && <div className="error">{error}</div>}
      
      {exams.length === 0 ? (
        <p>No exams available at the moment.</p>
      ) : (
        <div>
          {exams.map(exam => (
            <div key={exam._id} className="exam-card">
              <h3>{exam.title}</h3>
              <p>{exam.description}</p>
              <p><strong>Subject:</strong> {exam.subject?.name}</p>
              <p><strong>Duration:</strong> {exam.duration} minutes</p>
              <p><strong>Questions:</strong> {exam.questions?.length}</p>
              <p><strong>Type:</strong> {exam.examType}</p>
              {exam.isPaid && <p><strong>Price:</strong> ₹{exam.price}</p>}
              
              <Link to={`/exam/${exam._id}`} className="btn">
                Start Exam
              </Link>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default ExamList;
