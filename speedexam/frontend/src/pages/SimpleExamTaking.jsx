import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import api from '../services/api';

const SimpleExamTaking = () => {
  const { examId } = useParams();
  const navigate = useNavigate();
  const [exam, setExam] = useState(null);
  const [attemptId, setAttemptId] = useState(null);
  const [answers, setAnswers] = useState({});
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [timeLeft, setTimeLeft] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [submitting, setSubmitting] = useState(false);

  useEffect(() => {
    const token = localStorage.getItem('token');
    if (!token) {
      navigate('/login');
      return;
    }
    startExam();
  }, [examId, navigate]);

  useEffect(() => {
    if (timeLeft > 0) {
      const timer = setTimeout(() => setTimeLeft(timeLeft - 1), 1000);
      return () => clearTimeout(timer);
    } else if (timeLeft === 0 && exam) {
      handleSubmit();
    }
  }, [timeLeft, exam]);

  const startExam = async () => {
    try {
      console.log('🚀 Starting exam with ID:', examId);
      const response = await api.post(`/exams/${examId}/start`);
      console.log('✅ Start exam response:', response.data);

      setExam(response.data.exam);
      setAttemptId(response.data.attempt);
      setTimeLeft(response.data.exam.duration * 60);

      // Initialize answers
      const initialAnswers = {};
      response.data.exam.questions.forEach(question => {
        initialAnswers[question._id] = {
          question: question._id,
          selectedOptions: []
        };
      });
      setAnswers(initialAnswers);
      setLoading(false);
    } catch (error) {
      console.error('❌ Error starting exam:', error);
      setError(`Failed to start exam: ${error.response?.data?.message || error.message}`);
      setLoading(false);
    }
  };

  const handleAnswerChange = (questionId, value) => {
    setAnswers(prev => ({
      ...prev,
      [questionId]: {
        question: questionId,
        selectedOptions: [value]
      }
    }));
  };

  const handleSubmit = async () => {
    if (submitting) return;
    
    setSubmitting(true);
    try {
      console.log('📤 Submitting exam with attemptId:', attemptId);
      const answersArray = Object.values(answers);
      console.log('📝 Answers to submit:', answersArray);
      
      const response = await api.post(`/exam-attempts/${attemptId}/submit`, { answers: answersArray });
      console.log('✅ Submit response:', response.data);
      
      navigate(`/results/${attemptId}`);
    } catch (error) {
      console.error('❌ Submit error:', error);
      setError(`Failed to submit exam: ${error.response?.data?.message || error.message}`);
      setSubmitting(false);
    }
  };

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <h2>🔄 Loading Exam...</h2>
        <p>Please wait while we prepare your exam.</p>
      </div>
    );
  }

  if (error) {
    return (
      <div style={{ textAlign: 'center', padding: '50px', color: 'red' }}>
        <h2>❌ Error</h2>
        <p>{error}</p>
        <button onClick={() => navigate('/exams')} style={{ padding: '10px 20px', marginTop: '20px' }}>
          Back to Exams
        </button>
      </div>
    );
  }

  if (!exam) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <h2>❌ Exam Not Found</h2>
        <button onClick={() => navigate('/exams')} style={{ padding: '10px 20px' }}>
          Back to Exams
        </button>
      </div>
    );
  }

  const currentQ = exam.questions[currentQuestion];

  return (
    <div style={{ maxWidth: '800px', margin: '0 auto', padding: '20px' }}>
      {/* Header */}
      <div style={{ 
        background: '#007bff', 
        color: 'white', 
        padding: '20px', 
        borderRadius: '10px', 
        marginBottom: '20px',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <div>
          <h2 style={{ margin: 0 }}>{exam.title}</h2>
          <p style={{ margin: '5px 0 0 0' }}>Question {currentQuestion + 1} of {exam.questions.length}</p>
        </div>
        <div style={{ textAlign: 'right' }}>
          <div style={{ fontSize: '24px', fontWeight: 'bold' }}>
            ⏰ {formatTime(timeLeft)}
          </div>
          <div style={{ fontSize: '14px' }}>Time Remaining</div>
        </div>
      </div>

      {/* Question */}
      <div style={{ 
        background: 'white', 
        padding: '30px', 
        borderRadius: '10px', 
        boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
        marginBottom: '20px'
      }}>
        <h3 style={{ marginBottom: '20px', fontSize: '20px' }}>
          {currentQ.text}
        </h3>

        <div style={{ marginBottom: '20px' }}>
          {currentQ.options.map((option, index) => (
            <label key={index} style={{ 
              display: 'block', 
              padding: '15px', 
              margin: '10px 0',
              background: '#f8f9fa',
              border: '2px solid #dee2e6',
              borderRadius: '8px',
              cursor: 'pointer',
              transition: 'all 0.3s'
            }}>
              <input
                type="radio"
                name={`question_${currentQ._id}`}
                value={option.text}
                checked={answers[currentQ._id]?.selectedOptions[0] === option.text}
                onChange={(e) => handleAnswerChange(currentQ._id, e.target.value)}
                style={{ marginRight: '10px' }}
              />
              <span style={{ fontSize: '16px' }}>{option.text}</span>
            </label>
          ))}
        </div>
      </div>

      {/* Navigation */}
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        background: 'white',
        padding: '20px',
        borderRadius: '10px',
        boxShadow: '0 2px 10px rgba(0,0,0,0.1)'
      }}>
        <button
          onClick={() => setCurrentQuestion(Math.max(0, currentQuestion - 1))}
          disabled={currentQuestion === 0}
          style={{
            padding: '12px 24px',
            background: currentQuestion === 0 ? '#ccc' : '#6c757d',
            color: 'white',
            border: 'none',
            borderRadius: '5px',
            cursor: currentQuestion === 0 ? 'not-allowed' : 'pointer'
          }}
        >
          ← Previous
        </button>

        <div style={{ display: 'flex', gap: '10px' }}>
          {exam.questions.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentQuestion(index)}
              style={{
                width: '40px',
                height: '40px',
                borderRadius: '50%',
                border: 'none',
                background: index === currentQuestion ? '#007bff' : 
                           answers[exam.questions[index]._id]?.selectedOptions.length > 0 ? '#28a745' : '#dee2e6',
                color: index === currentQuestion || answers[exam.questions[index]._id]?.selectedOptions.length > 0 ? 'white' : '#6c757d',
                cursor: 'pointer',
                fontWeight: 'bold'
              }}
            >
              {index + 1}
            </button>
          ))}
        </div>

        {currentQuestion < exam.questions.length - 1 ? (
          <button
            onClick={() => setCurrentQuestion(currentQuestion + 1)}
            style={{
              padding: '12px 24px',
              background: '#007bff',
              color: 'white',
              border: 'none',
              borderRadius: '5px',
              cursor: 'pointer'
            }}
          >
            Next →
          </button>
        ) : (
          <button
            onClick={handleSubmit}
            disabled={submitting}
            style={{
              padding: '12px 24px',
              background: submitting ? '#ccc' : '#28a745',
              color: 'white',
              border: 'none',
              borderRadius: '5px',
              cursor: submitting ? 'not-allowed' : 'pointer',
              fontWeight: 'bold'
            }}
          >
            {submitting ? '⏳ Submitting...' : '✅ Submit Exam'}
          </button>
        )}
      </div>

      {/* Progress */}
      <div style={{ 
        marginTop: '20px', 
        textAlign: 'center',
        background: 'white',
        padding: '15px',
        borderRadius: '10px',
        boxShadow: '0 2px 10px rgba(0,0,0,0.1)'
      }}>
        <p style={{ margin: 0, fontSize: '16px' }}>
          📊 Progress: {Object.values(answers).filter(a => a.selectedOptions.length > 0).length} / {exam.questions.length} answered
        </p>
      </div>
    </div>
  );
};

export default SimpleExamTaking;
