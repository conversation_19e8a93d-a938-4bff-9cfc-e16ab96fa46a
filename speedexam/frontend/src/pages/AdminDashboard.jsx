import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import api from '../services/api';

const AdminDashboard = () => {
  const [stats, setStats] = useState({
    totalExams: 0,
    totalStudents: 0,
    totalAttempts: 0,
    averageScore: 0
  });
  const [recentAttempts, setRecentAttempts] = useState([]);
  const [exams, setExams] = useState([]);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      // Mock admin dashboard data
      setStats({
        totalExams: 2,
        totalStudents: 15,
        totalAttempts: 45,
        averageScore: 78
      });

      setRecentAttempts([
        {
          id: 1,
          studentName: '<PERSON>',
          examTitle: 'Basic Mathematics Test',
          score: 100,
          percentage: 100,
          timeTaken: 15,
          date: '2024-01-15'
        },
        {
          id: 2,
          studentName: '<PERSON>',
          examTitle: 'General Science Quiz',
          score: 1,
          percentage: 50,
          timeTaken: 12,
          date: '2024-01-15'
        },
        {
          id: 3,
          studentName: '<PERSON>',
          examTitle: 'Basic Mathematics Test',
          score: 2,
          percentage: 67,
          timeTaken: 20,
          date: '2024-01-14'
        },
        {
          id: 4,
          studentName: 'Sarah Wilson',
          examTitle: 'General Science Quiz',
          score: 2,
          percentage: 100,
          timeTaken: 8,
          date: '2024-01-14'
        },
        {
          id: 5,
          studentName: 'David Brown',
          examTitle: 'Basic Mathematics Test',
          score: 1,
          percentage: 33,
          timeTaken: 25,
          date: '2024-01-13'
        }
      ]);

      const examsResponse = await api.get('/exams');
      setExams(examsResponse.data);
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
    }
  };

  return (
    <div className="admin-dashboard">
      <div className="admin-header">
        <h1>🎓 Admin Dashboard</h1>
        <div className="admin-nav">
          <Link to="/" className="btn btn-secondary">Student View</Link>
          <Link to="/admin/create-exam" className="btn">Create Exam</Link>
          <Link to="/admin/exams" className="btn">Manage Exams</Link>
          <Link to="/admin/students" className="btn">View Students</Link>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="stats-grid">
        <div className="stat-card">
          <div className="stat-icon">📚</div>
          <div className="stat-content">
            <h3>{stats.totalExams}</h3>
            <p>Total Exams</p>
          </div>
        </div>
        
        <div className="stat-card">
          <div className="stat-icon">👥</div>
          <div className="stat-content">
            <h3>{stats.totalStudents}</h3>
            <p>Total Students</p>
          </div>
        </div>
        
        <div className="stat-card">
          <div className="stat-icon">📝</div>
          <div className="stat-content">
            <h3>{stats.totalAttempts}</h3>
            <p>Exam Attempts</p>
          </div>
        </div>
        
        <div className="stat-card">
          <div className="stat-icon">📊</div>
          <div className="stat-content">
            <h3>{stats.averageScore}%</h3>
            <p>Average Score</p>
          </div>
        </div>
      </div>

      {/* Available Exams */}
      <div className="admin-section">
        <h2>📚 Available Exams</h2>
        <div className="exams-grid">
          {exams.map(exam => (
            <div key={exam._id} className="admin-exam-card">
              <h3>{exam.title}</h3>
              <p><strong>Subject:</strong> {exam.subject?.name}</p>
              <p><strong>Questions:</strong> {exam.questions?.length}</p>
              <p><strong>Duration:</strong> {exam.duration} minutes</p>
              <p><strong>Type:</strong> {exam.examType}</p>
              <div className="exam-actions">
                <Link to={`/admin/exam/${exam._id}`} className="btn btn-sm">Edit</Link>
                <Link to={`/exam/${exam._id}`} className="btn btn-sm btn-secondary">Preview</Link>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Recent Exam Attempts */}
      <div className="admin-section">
        <h2>📋 Recent Exam Attempts</h2>
        <div className="attempts-table">
          <table>
            <thead>
              <tr>
                <th>Student</th>
                <th>Exam</th>
                <th>Score</th>
                <th>Percentage</th>
                <th>Time</th>
                <th>Date</th>
                <th>Status</th>
              </tr>
            </thead>
            <tbody>
              {recentAttempts.map(attempt => (
                <tr key={attempt.id}>
                  <td>{attempt.studentName}</td>
                  <td>{attempt.examTitle}</td>
                  <td>{attempt.score}/3</td>
                  <td>
                    <span className={`percentage ${
                      attempt.percentage >= 70 ? 'good' : 
                      attempt.percentage >= 50 ? 'average' : 'poor'
                    }`}>
                      {attempt.percentage}%
                    </span>
                  </td>
                  <td>{attempt.timeTaken} min</td>
                  <td>{attempt.date}</td>
                  <td>
                    <span className={`status ${
                      attempt.percentage >= 70 ? 'passed' : 'failed'
                    }`}>
                      {attempt.percentage >= 70 ? 'Passed' : 'Failed'}
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="admin-section">
        <h2>⚡ Quick Actions</h2>
        <div className="quick-actions">
          <Link to="/admin/create-exam" className="action-card">
            <div className="action-icon">➕</div>
            <h3>Create New Exam</h3>
            <p>Add a new exam with questions</p>
          </Link>
          
          <Link to="/admin/reports" className="action-card">
            <div className="action-icon">📈</div>
            <h3>View Reports</h3>
            <p>Detailed analytics and reports</p>
          </Link>
          
          <Link to="/admin/settings" className="action-card">
            <div className="action-icon">⚙️</div>
            <h3>System Settings</h3>
            <p>Configure application settings</p>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;
