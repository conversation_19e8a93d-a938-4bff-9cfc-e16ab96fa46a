import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Home from './pages/Home';
import Login from './pages/Login';
import Register from './pages/Register';
import ExamList from './pages/ExamList';
import ExamTaking from './pages/ExamTaking';
import ExamResults from './pages/ExamResults';
import Debug from './pages/Debug';
import './App.css';

function App() {
  return (
    <Router>
      <div className="App">
        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/login" element={<Login />} />
          <Route path="/register" element={<Register />} />
          <Route path="/exams" element={<ExamList />} />
          <Route path="/exam/:examId" element={<ExamTaking />} />
          <Route path="/results/:attemptId" element={<ExamResults />} />
          <Route path="/debug" element={<Debug />} />
        </Routes>
      </div>
    </Router>
  );
}

export default App;
