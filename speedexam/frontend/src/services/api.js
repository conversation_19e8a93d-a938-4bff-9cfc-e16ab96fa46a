import axios from 'axios';

// Safari-compatible API configuration
const getApiBaseUrl = () => {
  // Check if we're on Safari and having localhost issues
  const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
  const networkUrl = 'http://*************:5000/api';
  const localUrl = 'http://localhost:5000/api';

  // Use environment variable first, then network IP for Safari, then localhost
  return process.env.REACT_APP_API_URL ||
         (isSafari ? networkUrl : localUrl) ||
         localUrl;
};

const api = axios.create({
  baseURL: getApiBaseUrl()
});

// Add auth token to requests
api.interceptors.request.use(config => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

export default api;