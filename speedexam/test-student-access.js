const axios = require('axios');

const API_BASE = 'http://localhost:5000/api';

async function testStudentAccess() {
  console.log('🧪 Testing Student Access...\n');

  try {
    // Step 1: Test student login
    console.log('1. Testing student login...');
    const loginResponse = await axios.post(`${API_BASE}/auth/login`, {
      email: '<EMAIL>',
      password: 'password123'
    });
    
    const token = loginResponse.data.token;
    const user = loginResponse.data.user;
    
    console.log('✅ Student login successful');
    console.log('   User:', user.name, '(' + user.email + ')');
    console.log('   Role:', user.role);
    console.log('   Token:', token.substring(0, 30) + '...');

    // Step 2: Test exam list access
    console.log('\n2. Testing exam list access...');
    const examsResponse = await axios.get(`${API_BASE}/exams`);
    
    console.log('✅ Exam list loaded successfully');
    console.log('   Available exams:', examsResponse.data.length);
    
    examsResponse.data.forEach((exam, index) => {
      console.log(`   ${index + 1}. ${exam.title} (${exam.subject?.name || 'No subject'})`);
      console.log(`      Duration: ${exam.duration} min, Questions: ${exam.questions?.length || 0}`);
    });

    // Step 3: Test starting an exam
    if (examsResponse.data.length > 0) {
      const firstExam = examsResponse.data[0];
      console.log(`\n3. Testing exam start for: ${firstExam.title}...`);
      
      const startExamResponse = await axios.post(
        `${API_BASE}/exams/${firstExam._id}/start`,
        {},
        { headers: { Authorization: `Bearer ${token}` } }
      );
      
      console.log('✅ Exam started successfully');
      console.log('   Attempt ID:', startExamResponse.data.attempt);
      console.log('   Exam loaded:', startExamResponse.data.exam.title);
      console.log('   Questions available:', startExamResponse.data.exam.questions.length);
      
      // Display first question as example
      if (startExamResponse.data.exam.questions.length > 0) {
        const firstQuestion = startExamResponse.data.exam.questions[0];
        console.log('\n   📝 First Question Preview:');
        console.log('   Q:', firstQuestion.text);
        console.log('   Type:', firstQuestion.type);
        console.log('   Options:', firstQuestion.options.length);
        firstQuestion.options.forEach((option, i) => {
          console.log(`      ${String.fromCharCode(65 + i)}. ${option.text}`);
        });
      }
    }

    console.log('\n' + '='.repeat(50));
    console.log('🎉 STUDENT ACCESS TEST SUCCESSFUL!');
    console.log('='.repeat(50));
    
    console.log('\n✅ All Student Features Working:');
    console.log('• Student authentication');
    console.log('• Exam list retrieval');
    console.log('• Exam starting process');
    console.log('• Question loading');
    
    console.log('\n🌐 Frontend URLs to Test:');
    console.log('• Student Login: http://localhost:3000/login');
    console.log('• Student Dashboard: http://localhost:3000/student');
    console.log('• Exam List: http://localhost:3000/exams');
    console.log('• Home Page: http://localhost:3000');
    
    console.log('\n📝 Student Credentials:');
    console.log('• Email: <EMAIL>');
    console.log('• Password: password123');

  } catch (error) {
    console.error('\n❌ ERROR in student access test:');
    console.error('Message:', error.message);
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    }
    
    console.error('\n🔧 Troubleshooting Steps:');
    console.error('1. Check if backend is running on port 5000');
    console.error('2. Check if frontend is running on port 3000');
    console.error('3. Verify student credentials are correct');
    console.error('4. Check browser console for JavaScript errors');
    console.error('5. Try clearing browser cache and localStorage');
  }
}

// Run the test
testStudentAccess();
