const axios = require('axios');

const API_BASE = 'http://localhost:5000/api';

async function testExamFlow() {
  try {
    console.log('🚀 Testing SpeedExam API...\n');

    // 1. Test login
    console.log('1. Testing login...');
    const loginResponse = await axios.post(`${API_BASE}/auth/login`, {
      email: '<EMAIL>',
      password: 'password123'
    });
    
    const token = loginResponse.data.token;
    console.log('✅ Login successful');
    console.log('Token:', token.substring(0, 20) + '...\n');

    // 2. Get available exams
    console.log('2. Getting available exams...');
    const examsResponse = await axios.get(`${API_BASE}/exams`);
    const exams = examsResponse.data;
    
    console.log('✅ Found', exams.length, 'exams:');
    exams.forEach(exam => {
      console.log(`   - ${exam.title} (${exam.questions.length} questions, ${exam.duration} minutes)`);
    });
    console.log();

    if (exams.length === 0) {
      console.log('❌ No exams available');
      return;
    }

    // 3. Start an exam
    const examToTake = exams[0];
    console.log(`3. Starting exam: "${examToTake.title}"...`);
    
    const startResponse = await axios.post(
      `${API_BASE}/exams/${examToTake._id}/start`,
      {},
      { headers: { Authorization: `Bearer ${token}` } }
    );
    
    const { attempt: attemptId, exam } = startResponse.data;
    console.log('✅ Exam started successfully');
    console.log('Attempt ID:', attemptId);
    console.log('Questions:', exam.questions.length, '\n');

    // 4. Display questions and prepare answers
    console.log('4. Exam Questions:');
    const answers = [];
    
    exam.questions.forEach((question, index) => {
      console.log(`\nQ${index + 1}: ${question.text}`);
      console.log('Options:');
      question.options.forEach((option, optIndex) => {
        console.log(`   ${String.fromCharCode(65 + optIndex)}. ${option.text}`);
      });
      
      // For demo, automatically select the first option for each question
      const selectedAnswer = {
        question: question._id,
        selectedOptions: [question.options[0].text],
        answer: ''
      };
      answers.push(selectedAnswer);
      console.log(`   Selected: A. ${question.options[0].text}`);
    });

    console.log('\n5. Submitting exam...');
    
    // 5. Submit the exam
    const submitResponse = await axios.post(
      `${API_BASE}/exam-attempts/${attemptId}/submit`,
      { answers },
      { headers: { Authorization: `Bearer ${token}` } }
    );
    
    console.log('✅ Exam submitted successfully\n');

    // 6. Get results
    console.log('6. Getting exam results...');
    const resultsResponse = await axios.get(
      `${API_BASE}/exam-attempts/${attemptId}/results`,
      { headers: { Authorization: `Bearer ${token}` } }
    );
    
    const results = resultsResponse.data;
    console.log('✅ Results retrieved successfully\n');

    // 7. Display results
    console.log('📊 EXAM RESULTS');
    console.log('================');
    console.log(`Exam: ${results.examTitle}`);
    console.log(`Score: ${results.score}/${results.totalQuestions}`);
    console.log(`Percentage: ${results.percentage}%`);
    console.log(`Time Taken: ${results.timeTaken} minutes`);
    
    if (results.percentage >= 70) {
      console.log('🎉 PASSED! Great job!');
    } else if (results.percentage >= 50) {
      console.log('⚠️  AVERAGE - You can do better!');
    } else {
      console.log('❌ FAILED - Keep studying!');
    }

    console.log('\n🎯 Test completed successfully!');
    console.log('\nYou can now:');
    console.log('1. Open http://localhost:3000 in your browser');
    console.log('2. Login with: <EMAIL> / password123');
    console.log('3. Take the exam through the web interface');

  } catch (error) {
    console.error('❌ Error:', error.response?.data?.message || error.message);
    if (error.response?.data) {
      console.error('Response data:', error.response.data);
    }
  }
}

// Run the test
testExamFlow();
