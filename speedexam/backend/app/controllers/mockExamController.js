// Mock data for demonstration purposes
const mockExams = [
  {
    _id: '1',
    title: 'Basic Mathematics Test',
    description: 'A simple test covering basic mathematical concepts',
    subject: { _id: 'math', name: 'Mathematics' },
    questions: [
      {
        _id: 'q1',
        text: 'What is 2 + 2?',
        type: 'MCQ',
        options: [
          { text: '3', isCorrect: false },
          { text: '4', isCorrect: true },
          { text: '5', isCorrect: false },
          { text: '6', isCorrect: false }
        ]
      },
      {
        _id: 'q2',
        text: 'What is 10 × 5?',
        type: 'MCQ',
        options: [
          { text: '45', isCorrect: false },
          { text: '50', isCorrect: true },
          { text: '55', isCorrect: false },
          { text: '60', isCorrect: false }
        ]
      },
      {
        _id: 'q3',
        text: 'Is 17 a prime number?',
        type: 'TRUE_FALSE',
        options: [
          { text: 'True', isCorrect: true },
          { text: 'False', isCorrect: false }
        ]
      }
    ],
    duration: 30,
    price: 0,
    isPaid: false,
    examType: 'SUBJECT_WISE',
    isActive: true
  },
  {
    _id: '2',
    title: 'General Science Quiz',
    description: 'Test your general science knowledge',
    subject: { _id: 'science', name: 'Science' },
    questions: [
      {
        _id: 'q4',
        text: 'What is the chemical symbol for water?',
        type: 'MCQ',
        options: [
          { text: 'H2O', isCorrect: true },
          { text: 'CO2', isCorrect: false },
          { text: 'O2', isCorrect: false },
          { text: 'H2', isCorrect: false }
        ]
      },
      {
        _id: 'q5',
        text: 'The Earth revolves around the Sun.',
        type: 'TRUE_FALSE',
        options: [
          { text: 'True', isCorrect: true },
          { text: 'False', isCorrect: false }
        ]
      }
    ],
    duration: 20,
    price: 0,
    isPaid: false,
    examType: 'SUBJECT_WISE',
    isActive: true
  }
];

const mockUsers = [
  {
    _id: 'user1',
    name: 'Test User',
    email: '<EMAIL>',
    password: '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // password123
    role: 'student',
    isVerified: true
  },
  {
    _id: 'admin1',
    name: 'Admin User',
    email: '<EMAIL>',
    password: '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // admin123
    role: 'admin',
    isVerified: true
  }
];

const mockAttempts = new Map();
let attemptCounter = 1;

// Get all exams
exports.getAllExams = async (req, res) => {
  try {
    const exams = mockExams.map(exam => ({
      ...exam,
      questions: exam.questions.map(q => ({ _id: q._id, text: q.text, type: q.type }))
    }));
    res.json(exams);
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Get exam by ID
exports.getExamById = async (req, res) => {
  try {
    const exam = mockExams.find(e => e._id === req.params.id);
    if (!exam) {
      return res.status(404).json({ message: 'Exam not found' });
    }
    res.json(exam);
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Start exam attempt
exports.startExam = async (req, res) => {
  try {
    const { examId } = req.params;
    const userId = req.user?._id || 'user1'; // Mock user

    console.log('Starting exam request:', { examId, userId });

    const exam = mockExams.find(e => e._id === examId);
    if (!exam) {
      console.log('Exam not found:', examId);
      return res.status(404).json({ message: 'Exam not found' });
    }
    
    const attemptId = `attempt_${attemptCounter++}`;
    
    const attempt = {
      _id: attemptId,
      user: userId,
      exam: examId,
      totalQuestions: exam.questions.length,
      startTime: new Date(),
      isCompleted: false
    };
    
    mockAttempts.set(attemptId, attempt);

    // Return exam with questions but without correct answers
    const examForAttempt = {
      ...exam,
      questions: exam.questions.map(q => ({
        _id: q._id,
        text: q.text,
        type: q.type,
        options: q.options.map(opt => ({ text: opt.text }))
      }))
    };

    console.log('Exam started successfully:', { attemptId, examTitle: exam.title });
    res.json({ attempt: attemptId, exam: examForAttempt });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Submit exam attempt
exports.submitExam = async (req, res) => {
  try {
    const { attemptId } = req.params;
    const { answers } = req.body;
    
    const attempt = mockAttempts.get(attemptId);
    if (!attempt) {
      return res.status(404).json({ message: 'Exam attempt not found' });
    }
    
    if (attempt.isCompleted) {
      return res.status(400).json({ message: 'Exam already submitted' });
    }
    
    const exam = mockExams.find(e => e._id === attempt.exam);
    
    // Calculate score
    let score = 0;
    answers.forEach(answer => {
      const question = exam.questions.find(q => q._id === answer.question);
      if (question) {
        if (question.type === 'MCQ' || question.type === 'TRUE_FALSE') {
          const correctOption = question.options.find(opt => opt.isCorrect);
          if (correctOption && answer.selectedOptions.includes(correctOption.text)) {
            score++;
          }
        }
      }
    });
    
    const percentage = Math.round((score / exam.questions.length) * 100);
    const timeTaken = Math.round((new Date() - attempt.startTime) / (1000 * 60));
    
    attempt.answers = answers;
    attempt.score = score;
    attempt.percentage = percentage;
    attempt.timeTaken = timeTaken;
    attempt.endTime = new Date();
    attempt.isCompleted = true;
    
    mockAttempts.set(attemptId, attempt);
    
    res.json({ message: 'Exam submitted successfully', attemptId });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Get exam results
exports.getExamResults = async (req, res) => {
  try {
    const { attemptId } = req.params;
    
    const attempt = mockAttempts.get(attemptId);
    if (!attempt) {
      return res.status(404).json({ message: 'Exam attempt not found' });
    }
    
    if (!attempt.isCompleted) {
      return res.status(400).json({ message: 'Exam not yet completed' });
    }
    
    const exam = mockExams.find(e => e._id === attempt.exam);
    
    res.json({
      score: attempt.score,
      totalQuestions: attempt.totalQuestions,
      percentage: attempt.percentage,
      timeTaken: attempt.timeTaken,
      examTitle: exam.title,
      answers: attempt.answers
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Mock login
exports.login = async (req, res) => {
  try {
    const { email, password } = req.body;
    
    const user = mockUsers.find(u => u.email === email);
    if (!user) {
      return res.status(400).json({ message: 'Invalid credentials' });
    }
    
    // For demo purposes, accept any password
    const token = 'mock_jwt_token_' + Date.now();
    
    res.json({
      token,
      user: {
        id: user._id,
        name: user.name,
        email: user.email,
        role: user.role || 'student'
      }
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Mock register
exports.register = async (req, res) => {
  try {
    const { name, email, password } = req.body;

    const existingUser = mockUsers.find(u => u.email === email);
    if (existingUser) {
      return res.status(400).json({ message: 'User already exists' });
    }

    const newUser = {
      _id: 'user_' + Date.now(),
      name,
      email,
      password: 'hashed_password',
      isVerified: true
    };

    mockUsers.push(newUser);

    res.status(201).json({ message: 'User registered successfully. You can now login.' });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Create new exam
exports.createExam = async (req, res) => {
  try {
    const examData = req.body;
    console.log('Creating new exam:', examData.title);

    const newExam = {
      _id: 'exam_' + Date.now(),
      ...examData,
      subject: { _id: examData.subject, name: getSubjectName(examData.subject) },
      questions: examData.questions.map((q, index) => ({
        _id: 'q_' + Date.now() + '_' + index,
        ...q
      })),
      createdAt: new Date(),
      isActive: true
    };

    mockExams.push(newExam);

    console.log('Exam created successfully:', newExam.title);
    res.status(201).json({
      message: 'Exam created successfully',
      exam: newExam
    });
  } catch (error) {
    console.error('Error creating exam:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Get subjects
exports.getSubjects = async (req, res) => {
  try {
    const subjects = [
      { _id: 'mathematics', name: 'Mathematics' },
      { _id: 'science', name: 'Science' },
      { _id: 'english', name: 'English' },
      { _id: 'history', name: 'History' },
      { _id: 'geography', name: 'Geography' },
      { _id: 'computer', name: 'Computer Science' }
    ];

    res.json(subjects);
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Helper function to get subject name
function getSubjectName(subjectId) {
  const subjects = {
    'mathematics': 'Mathematics',
    'science': 'Science',
    'english': 'English',
    'history': 'History',
    'geography': 'Geography',
    'computer': 'Computer Science'
  };
  return subjects[subjectId] || 'Unknown Subject';
}

// Update exam
exports.updateExam = async (req, res) => {
  try {
    const { examId } = req.params;
    const updateData = req.body;

    const examIndex = mockExams.findIndex(e => e._id === examId);
    if (examIndex === -1) {
      return res.status(404).json({ message: 'Exam not found' });
    }

    mockExams[examIndex] = {
      ...mockExams[examIndex],
      ...updateData,
      updatedAt: new Date()
    };

    console.log('Exam updated successfully:', mockExams[examIndex].title);
    res.json({
      message: 'Exam updated successfully',
      exam: mockExams[examIndex]
    });
  } catch (error) {
    console.error('Error updating exam:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Delete exam
exports.deleteExam = async (req, res) => {
  try {
    const { examId } = req.params;

    const examIndex = mockExams.findIndex(e => e._id === examId);
    if (examIndex === -1) {
      return res.status(404).json({ message: 'Exam not found' });
    }

    const deletedExam = mockExams.splice(examIndex, 1)[0];

    console.log('Exam deleted successfully:', deletedExam.title);
    res.json({ message: 'Exam deleted successfully' });
  } catch (error) {
    console.error('Error deleting exam:', error);
    res.status(500).json({ message: 'Server error' });
  }
};
