const express = require('express');
const router = express.Router();
const mockExamController = require('../controllers/mockExamController');

// Mock auth middleware for demo
const mockAuth = (req, res, next) => {
  req.user = { _id: 'user1', name: 'Test User', email: '<EMAIL>' };
  next();
};

// Auth routes (using mock controller)
router.post('/auth/register', mockExamController.register);
router.post('/auth/login', mockExamController.login);

// Exam routes (using mock controller)
router.get('/exams', mockExamController.getAllExams);
router.post('/exams', mockAuth, mockExamController.createExam);
router.put('/exams/:examId', mockAuth, mockExamController.updateExam);
router.delete('/exams/:examId', mockAuth, mockExamController.deleteExam);
router.get('/subjects', mockExamController.getSubjects);
router.get('/exams/:id', mockExamController.getExamById);
router.post('/exams/:examId/start', mockAuth, mockExamController.startExam);
router.post('/exam-attempts/:attemptId/submit', mockAuth, mockExamController.submitExam);
router.get('/exam-attempts/:attemptId/results', mockAuth, mockExamController.getExamResults);

// Placeholder route for testing
router.get('/test', (req, res) => {
  res.json({ message: 'API is working with mock data!' });
});

module.exports = router;
