# SpeedExam Application Testing Guide

## 🚀 Application Status
- ✅ Backend running on: http://localhost:5000
- ✅ Frontend running on: http://localhost:3000
- ✅ API tests passing with 100% score
- ✅ Mock data loaded with 2 sample exams

## 🔧 Quick Test URLs

### Main Application
- **Home Page**: http://localhost:3000
- **Login Page**: http://localhost:3000/login
- **Exam List**: http://localhost:3000/exams
- **Debug Page**: http://localhost:3000/debug

### API Endpoints (for direct testing)
- **API Status**: http://localhost:5000/api/test
- **Get Exams**: http://localhost:5000/api/exams

## 📝 Step-by-Step Testing Instructions

### Step 1: Test API Connection
1. Open: http://localhost:3000/debug
2. Click "Test API" button
3. Should show: "✅ API Connected"
4. Should display 2 available exams

### Step 2: Test User Authentication
1. On debug page, click "Test Login" button
2. Should show: "✅ Login successful: Test User"
3. Should display a token

### Step 3: Test Exam Start (Debug Page)
1. On debug page, after login, click "Test Start Exam" for any exam
2. Should show alert: "✅ Exam started successfully!"

### Step 4: Test Full Application Flow
1. Go to: http://localhost:3000
2. Click "Login"
3. Enter credentials:
   - Email: `<EMAIL>`
   - Password: `password123`
4. Click "Login" button
5. Should redirect to exam list
6. Click "Start Exam" on any exam
7. Answer questions and submit
8. View results

## 🎯 Available Sample Exams

### 1. Basic Mathematics Test (30 minutes)
- **Question 1**: What is 2 + 2? (Answer: 4)
- **Question 2**: What is 10 × 5? (Answer: 50)
- **Question 3**: Is 17 a prime number? (Answer: True)

### 2. General Science Quiz (20 minutes)
- **Question 1**: What is the chemical symbol for water? (Answer: H2O)
- **Question 2**: The Earth revolves around the Sun. (Answer: True)

## 🔑 Test Credentials
- **Email**: <EMAIL>
- **Password**: password123

## 🐛 Troubleshooting

### If "Start Exam" is not working:

1. **Check Browser Console**:
   - Open browser developer tools (F12)
   - Look for JavaScript errors in Console tab
   - Look for failed network requests in Network tab

2. **Check API Connection**:
   - Visit: http://localhost:3000/debug
   - Test all functions on debug page
   - Verify API status shows "✅ API Connected"

3. **Check Authentication**:
   - Make sure you're logged in
   - Check if token exists in localStorage
   - Try logging out and logging back in

4. **Check Backend Logs**:
   - Look at terminal running backend
   - Should see "Starting exam request" messages when exam starts

5. **Manual API Test**:
   ```bash
   # Test login
   curl -X POST http://localhost:5000/api/auth/login \
     -H "Content-Type: application/json" \
     -d '{"email":"<EMAIL>","password":"password123"}'
   
   # Test start exam (replace TOKEN with actual token)
   curl -X POST http://localhost:5000/api/exams/1/start \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer TOKEN"
   ```

### Common Issues and Solutions:

1. **CORS Errors**: Backend has CORS enabled for localhost:3000
2. **Token Issues**: Try clearing localStorage and logging in again
3. **Network Errors**: Ensure both frontend and backend are running
4. **Route Issues**: Check that React Router is working correctly

## 📊 Expected Test Results

When everything works correctly:
- Login should redirect to exam list
- Exam list should show 2 exams
- Start exam should load questions
- Submit should calculate score
- Results should display percentage

## 🎉 Success Indicators

- ✅ Can login with test credentials
- ✅ Can see exam list after login
- ✅ Can click "Start Exam" and see questions
- ✅ Can navigate between questions
- ✅ Can submit exam and see results
- ✅ Timer counts down during exam
- ✅ Results show correct score and percentage

## 📞 Need Help?

If you encounter issues:
1. Check the debug page first: http://localhost:3000/debug
2. Look at browser console for errors
3. Check backend terminal for error messages
4. Verify both servers are running on correct ports
