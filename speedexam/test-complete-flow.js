const axios = require('axios');

const API_BASE = 'http://localhost:5000/api';

async function testCompleteFlow() {
  console.log('🚀 Testing Complete SpeedExam Flow...\n');

  try {
    // Step 1: Test API connectivity
    console.log('1. Testing API connectivity...');
    const testResponse = await axios.get(`${API_BASE}/test`);
    console.log('✅ API is working:', testResponse.data.message);

    // Step 2: Test login
    console.log('\n2. Testing user login...');
    const loginResponse = await axios.post(`${API_BASE}/auth/login`, {
      email: '<EMAIL>',
      password: 'password123'
    });
    
    const token = loginResponse.data.token;
    const user = loginResponse.data.user;
    console.log('✅ Login successful');
    console.log('   User:', user.name, '(' + user.email + ')');
    console.log('   Token:', token.substring(0, 30) + '...');

    // Step 3: Get available exams
    console.log('\n3. Fetching available exams...');
    const examsResponse = await axios.get(`${API_BASE}/exams`);
    const exams = examsResponse.data;
    
    console.log('✅ Found', exams.length, 'available exams:');
    exams.forEach((exam, index) => {
      console.log(`   ${index + 1}. ${exam.title}`);
      console.log(`      - Subject: ${exam.subject.name}`);
      console.log(`      - Questions: ${exam.questions.length}`);
      console.log(`      - Duration: ${exam.duration} minutes`);
      console.log(`      - Type: ${exam.examType}`);
    });

    if (exams.length === 0) {
      console.log('❌ No exams available to test');
      return;
    }

    // Step 4: Start first exam
    const examToTest = exams[0];
    console.log(`\n4. Starting exam: "${examToTest.title}"...`);
    
    const startResponse = await axios.post(
      `${API_BASE}/exams/${examToTest._id}/start`,
      {},
      { headers: { Authorization: `Bearer ${token}` } }
    );
    
    const { attempt: attemptId, exam } = startResponse.data;
    console.log('✅ Exam started successfully');
    console.log('   Attempt ID:', attemptId);
    console.log('   Questions loaded:', exam.questions.length);

    // Step 5: Display questions and simulate answers
    console.log('\n5. Exam Questions and Simulated Answers:');
    const answers = [];
    
    exam.questions.forEach((question, index) => {
      console.log(`\n   Q${index + 1}: ${question.text}`);
      console.log('   Type:', question.type);
      console.log('   Options:');
      
      question.options.forEach((option, optIndex) => {
        console.log(`      ${String.fromCharCode(65 + optIndex)}. ${option.text}`);
      });
      
      // Simulate intelligent answers (try to pick correct ones for demo)
      let selectedOptions = [];
      
      if (question.text.includes('2 + 2')) {
        selectedOptions = ['4']; // Correct answer
      } else if (question.text.includes('10 × 5')) {
        selectedOptions = ['50']; // Correct answer
      } else if (question.text.includes('prime number')) {
        selectedOptions = ['True']; // Correct answer
      } else if (question.text.includes('chemical symbol for water')) {
        selectedOptions = ['H2O']; // Correct answer
      } else if (question.text.includes('Earth revolves')) {
        selectedOptions = ['True']; // Correct answer
      } else {
        // Default to first option if we don't know the answer
        selectedOptions = [question.options[0].text];
      }
      
      const answer = {
        question: question._id,
        selectedOptions: selectedOptions,
        answer: ''
      };
      answers.push(answer);
      
      console.log(`   ✓ Selected: ${selectedOptions.join(', ')}`);
    });

    // Step 6: Submit exam
    console.log('\n6. Submitting exam...');
    const submitResponse = await axios.post(
      `${API_BASE}/exam-attempts/${attemptId}/submit`,
      { answers },
      { headers: { Authorization: `Bearer ${token}` } }
    );
    
    console.log('✅ Exam submitted successfully');

    // Step 7: Get results
    console.log('\n7. Retrieving exam results...');
    const resultsResponse = await axios.get(
      `${API_BASE}/exam-attempts/${attemptId}/results`,
      { headers: { Authorization: `Bearer ${token}` } }
    );
    
    const results = resultsResponse.data;
    console.log('✅ Results retrieved successfully');

    // Step 8: Display comprehensive results
    console.log('\n' + '='.repeat(50));
    console.log('📊 FINAL EXAM RESULTS');
    console.log('='.repeat(50));
    console.log(`Exam: ${results.examTitle}`);
    console.log(`Student: ${user.name}`);
    console.log(`Score: ${results.score}/${results.totalQuestions}`);
    console.log(`Percentage: ${results.percentage}%`);
    console.log(`Time Taken: ${results.timeTaken} minutes`);
    console.log('='.repeat(50));
    
    // Performance evaluation
    if (results.percentage >= 90) {
      console.log('🏆 EXCELLENT! Outstanding performance!');
    } else if (results.percentage >= 80) {
      console.log('🎉 GREAT! Very good performance!');
    } else if (results.percentage >= 70) {
      console.log('✅ GOOD! You passed the exam!');
    } else if (results.percentage >= 50) {
      console.log('⚠️  AVERAGE - You can improve!');
    } else {
      console.log('❌ NEEDS IMPROVEMENT - Keep studying!');
    }

    console.log('\n🎯 COMPLETE FLOW TEST SUCCESSFUL!');
    console.log('\n📝 Summary:');
    console.log('✅ API Connection: Working');
    console.log('✅ User Authentication: Working');
    console.log('✅ Exam Listing: Working');
    console.log('✅ Exam Starting: Working');
    console.log('✅ Question Display: Working');
    console.log('✅ Answer Submission: Working');
    console.log('✅ Results Calculation: Working');
    console.log('✅ Results Display: Working');

    console.log('\n🌐 Frontend URLs:');
    console.log('• Main App: http://localhost:3000');
    console.log('• Debug Page: http://localhost:3000/debug');
    console.log('• Login: http://localhost:3000/login');
    console.log('• Exams: http://localhost:3000/exams');

    console.log('\n🔑 Test Credentials:');
    console.log('• Email: <EMAIL>');
    console.log('• Password: password123');

  } catch (error) {
    console.error('\n❌ ERROR in flow test:');
    console.error('Message:', error.message);
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    }
    console.error('\n🔧 Troubleshooting:');
    console.error('1. Make sure backend is running on port 5000');
    console.error('2. Make sure frontend is running on port 3000');
    console.error('3. Check browser console for errors');
    console.error('4. Try the debug page: http://localhost:3000/debug');
  }
}

// Run the complete flow test
testCompleteFlow();
