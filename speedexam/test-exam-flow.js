const axios = require('axios');

const API_BASE = 'http://localhost:5000/api';

async function testExamFlow() {
  console.log('🧪 Testing Complete Exam Flow...\n');

  try {
    // Step 1: Student login
    console.log('1. Testing student login...');
    const loginResponse = await axios.post(`${API_BASE}/auth/login`, {
      email: '<EMAIL>',
      password: 'password123'
    });
    
    const token = loginResponse.data.token;
    console.log('✅ Login successful, token:', token.substring(0, 20) + '...');

    // Step 2: Start exam
    console.log('\n2. Starting exam...');
    const startResponse = await axios.post(
      `${API_BASE}/exams/1/start`,
      {},
      { headers: { Authorization: `Bearer ${token}` } }
    );
    
    const attemptId = startResponse.data.attempt;
    const exam = startResponse.data.exam;
    console.log('✅ Exam started successfully');
    console.log('   Attempt ID:', attemptId);
    console.log('   Exam:', exam.title);
    console.log('   Questions:', exam.questions.length);

    // Step 3: Prepare answers
    console.log('\n3. Preparing answers...');
    const answers = [
      {
        question: 'q1',
        selectedOptions: ['4']
      },
      {
        question: 'q2', 
        selectedOptions: ['50']
      },
      {
        question: 'q3',
        selectedOptions: ['True']
      }
    ];
    console.log('✅ Answers prepared:', answers.length, 'questions');

    // Step 4: Submit exam
    console.log('\n4. Submitting exam...');
    const submitResponse = await axios.post(
      `${API_BASE}/exam-attempts/${attemptId}/submit`,
      { answers: answers },
      { headers: { Authorization: `Bearer ${token}` } }
    );
    
    console.log('✅ Exam submitted successfully');
    console.log('   Response:', submitResponse.data);

    // Step 5: Get results
    console.log('\n5. Getting results...');
    const resultsResponse = await axios.get(
      `${API_BASE}/exam-attempts/${attemptId}/results`,
      { headers: { Authorization: `Bearer ${token}` } }
    );
    
    const results = resultsResponse.data;
    console.log('✅ Results retrieved successfully');
    console.log('   Score:', results.score, '/', results.totalQuestions);
    console.log('   Percentage:', results.percentage + '%');
    console.log('   Grade:', results.grade);

    console.log('\n' + '='.repeat(50));
    console.log('🎉 COMPLETE EXAM FLOW TEST SUCCESSFUL!');
    console.log('='.repeat(50));
    
    console.log('\n✅ All Steps Working:');
    console.log('• Student login ✅');
    console.log('• Exam start ✅');
    console.log('• Answer preparation ✅');
    console.log('• Exam submission ✅');
    console.log('• Results retrieval ✅');
    
    console.log('\n🎯 Expected Frontend Flow:');
    console.log('1. Login at: http://localhost:3000/login');
    console.log('2. Use credentials: <EMAIL> / password123');
    console.log('3. Start Mathematics Test');
    console.log('4. Answer questions and submit');
    console.log('5. View results with', results.percentage + '% score');

  } catch (error) {
    console.error('\n❌ ERROR in exam flow test:');
    console.error('Message:', error.message);
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    }
    
    console.error('\n🔧 Troubleshooting:');
    console.error('1. Check if backend is running on port 5000');
    console.error('2. Check if frontend is running on port 3000');
    console.error('3. Clear browser cache and localStorage');
    console.error('4. Check browser console for JavaScript errors');
  }
}

// Run the test
testExamFlow();
