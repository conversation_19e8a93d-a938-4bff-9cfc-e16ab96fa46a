const axios = require('axios');

const API_BASE = 'http://localhost:5000/api';

async function testCreateExamAPI() {
  console.log('🧪 Testing Create Exam API Endpoints...\n');

  try {
    // Step 1: Test subjects endpoint
    console.log('1. Testing subjects endpoint...');
    const subjectsResponse = await axios.get(`${API_BASE}/subjects`);
    console.log('✅ Subjects loaded:', subjectsResponse.data.length, 'subjects');
    subjectsResponse.data.forEach(subject => {
      console.log(`   - ${subject.name} (${subject._id})`);
    });

    // Step 2: Test admin login
    console.log('\n2. Testing admin authentication...');
    const adminLoginResponse = await axios.post(`${API_BASE}/auth/login`, {
      email: '<EMAIL>',
      password: 'admin123'
    });
    
    const adminToken = adminLoginResponse.data.token;
    console.log('✅ Admin login successful');
    console.log('   Token:', adminToken.substring(0, 30) + '...');

    // Step 3: Create a new exam
    console.log('\n3. Testing exam creation...');
    const newExamData = {
      title: 'API Test Exam - Advanced Physics',
      description: 'A comprehensive physics exam created via API',
      subject: 'science',
      duration: 45,
      examType: 'MOCK_TEST',
      isPaid: false,
      price: 0,
      instructions: 'Read all questions carefully. Use calculator when needed.',
      passingScore: 75,
      questions: [
        {
          text: 'What is the speed of light in vacuum?',
          type: 'MCQ',
          options: [
            { text: '3 × 10⁸ m/s', isCorrect: true },
            { text: '3 × 10⁶ m/s', isCorrect: false },
            { text: '3 × 10¹⁰ m/s', isCorrect: false },
            { text: '3 × 10⁴ m/s', isCorrect: false }
          ],
          explanation: 'The speed of light in vacuum is approximately 3 × 10⁸ meters per second.',
          difficulty: 'EASY',
          points: 2
        },
        {
          text: 'Which of the following are fundamental forces in physics?',
          type: 'MULTI_ANSWER',
          options: [
            { text: 'Gravitational force', isCorrect: true },
            { text: 'Electromagnetic force', isCorrect: true },
            { text: 'Centrifugal force', isCorrect: false },
            { text: 'Strong nuclear force', isCorrect: true },
            { text: 'Weak nuclear force', isCorrect: true }
          ],
          explanation: 'The four fundamental forces are gravitational, electromagnetic, strong nuclear, and weak nuclear forces.',
          difficulty: 'MEDIUM',
          points: 3
        },
        {
          text: 'Energy can be created or destroyed.',
          type: 'TRUE_FALSE',
          options: [
            { text: 'True', isCorrect: false },
            { text: 'False', isCorrect: true }
          ],
          explanation: 'According to the law of conservation of energy, energy cannot be created or destroyed, only transformed.',
          difficulty: 'EASY',
          points: 1
        },
        {
          text: 'The formula for kinetic energy is KE = ½mv²',
          type: 'TRUE_FALSE',
          options: [
            { text: 'True', isCorrect: true },
            { text: 'False', isCorrect: false }
          ],
          explanation: 'The kinetic energy formula is indeed KE = ½mv², where m is mass and v is velocity.',
          difficulty: 'EASY',
          points: 1
        },
        {
          text: 'What is the SI unit of electric current?',
          type: 'FILL_BLANK',
          options: [
            { text: 'Ampere', isCorrect: true }
          ],
          explanation: 'The ampere (A) is the SI unit of electric current.',
          difficulty: 'MEDIUM',
          points: 2
        }
      ]
    };

    const createExamResponse = await axios.post(
      `${API_BASE}/exams`,
      newExamData,
      { headers: { Authorization: `Bearer ${adminToken}` } }
    );

    console.log('✅ Exam created successfully!');
    console.log('   Exam ID:', createExamResponse.data.exam._id);
    console.log('   Title:', createExamResponse.data.exam.title);
    console.log('   Questions:', createExamResponse.data.exam.questions.length);
    console.log('   Total Points:', createExamResponse.data.exam.questions.reduce((sum, q) => sum + q.points, 0));

    // Step 4: Verify exam appears in exam list
    console.log('\n4. Verifying exam appears in exam list...');
    const examsResponse = await axios.get(`${API_BASE}/exams`);
    const createdExam = examsResponse.data.find(exam => exam._id === createExamResponse.data.exam._id);
    
    if (createdExam) {
      console.log('✅ Exam found in exam list');
      console.log('   Title:', createdExam.title);
      console.log('   Subject:', createdExam.subject.name);
      console.log('   Duration:', createdExam.duration, 'minutes');
    } else {
      console.log('❌ Exam not found in exam list');
    }

    // Step 5: Test exam update
    console.log('\n5. Testing exam update...');
    const updateData = {
      title: 'API Test Exam - Advanced Physics (Updated)',
      description: 'Updated description with more details',
      duration: 50
    };

    const updateResponse = await axios.put(
      `${API_BASE}/exams/${createExamResponse.data.exam._id}`,
      updateData,
      { headers: { Authorization: `Bearer ${adminToken}` } }
    );

    console.log('✅ Exam updated successfully');
    console.log('   New Title:', updateResponse.data.exam.title);
    console.log('   New Duration:', updateResponse.data.exam.duration, 'minutes');

    // Step 6: Test student can start the exam
    console.log('\n6. Testing student can start the created exam...');
    const studentLoginResponse = await axios.post(`${API_BASE}/auth/login`, {
      email: '<EMAIL>',
      password: 'password123'
    });

    const studentToken = studentLoginResponse.data.token;
    
    const startExamResponse = await axios.post(
      `${API_BASE}/exams/${createExamResponse.data.exam._id}/start`,
      {},
      { headers: { Authorization: `Bearer ${studentToken}` } }
    );

    console.log('✅ Student can start the exam');
    console.log('   Attempt ID:', startExamResponse.data.attempt);
    console.log('   Questions loaded:', startExamResponse.data.exam.questions.length);

    // Step 7: Test exam deletion
    console.log('\n7. Testing exam deletion...');
    const deleteResponse = await axios.delete(
      `${API_BASE}/exams/${createExamResponse.data.exam._id}`,
      { headers: { Authorization: `Bearer ${adminToken}` } }
    );

    console.log('✅ Exam deleted successfully');

    // Step 8: Verify exam is removed from list
    console.log('\n8. Verifying exam is removed from list...');
    const finalExamsResponse = await axios.get(`${API_BASE}/exams`);
    const deletedExam = finalExamsResponse.data.find(exam => exam._id === createExamResponse.data.exam._id);
    
    if (!deletedExam) {
      console.log('✅ Exam successfully removed from list');
    } else {
      console.log('❌ Exam still appears in list');
    }

    console.log('\n' + '='.repeat(50));
    console.log('🎉 CREATE EXAM API TEST SUCCESSFUL!');
    console.log('='.repeat(50));
    
    console.log('\n📝 API Endpoints Tested:');
    console.log('✅ GET /subjects - Fetch available subjects');
    console.log('✅ POST /exams - Create new exam');
    console.log('✅ PUT /exams/:id - Update existing exam');
    console.log('✅ DELETE /exams/:id - Delete exam');
    console.log('✅ GET /exams - List all exams');
    console.log('✅ POST /exams/:id/start - Start exam (student)');

    console.log('\n🔧 Features Validated:');
    console.log('✅ Admin authentication');
    console.log('✅ Exam CRUD operations');
    console.log('✅ Multiple question types (MCQ, Multi-answer, True/False, Fill-blank)');
    console.log('✅ Question difficulty levels');
    console.log('✅ Point allocation system');
    console.log('✅ Student exam access');
    console.log('✅ Data persistence');

    console.log('\n🌐 Frontend Integration:');
    console.log('• Admin Create Exam: http://localhost:3000/admin/create-exam');
    console.log('• Admin Dashboard: http://localhost:3000/admin');
    console.log('• Student Exam List: http://localhost:3000/exams');

  } catch (error) {
    console.error('\n❌ ERROR in API test:');
    console.error('Message:', error.message);
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    }
    console.error('\n🔧 Troubleshooting:');
    console.error('1. Make sure backend is running on port 5000');
    console.error('2. Check if all API endpoints are properly configured');
    console.error('3. Verify authentication is working');
  }
}

// Run the API test
testCreateExamAPI();
